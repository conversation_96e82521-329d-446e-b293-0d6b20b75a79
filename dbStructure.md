# SQL Database Tables

## languages

```sql
CREATE TABLE languages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    language VARCHAR(10) NOT NULL,
    expired TINYINT(1) NOT NULL DEFAULT 0,
    created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    modified_by <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## rooms

```sql
CREATE TABLE rooms (
    id INT AUTO_INCREMENT,
    roomid VARCHAR(15) NOT NULL PRIMARY KEY,
    is_demo TINYINT(1) NOT NULL DEFAULT 0,
    demo_length INT NOT NULL DEFAULT 0,
    user_email VARCHAR(255) NOT NULL,
    language_id INT NOT NULL,

    created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    modified_by <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (language_id) REFERENCES languages(id),
);
```

## responses

```sql
CREATE TABLE responses (
    roomid VARCHAR(15) NOT NULL,
    id INT AUTO_INCREMENT,
    question TEXT,
    answer TEXT,
    error_type TEXT,
    error_details TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (roomid) REFERENCES rooms(roomid)
);
```

## response_ratings

```sql
CREATE TABLE response_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    response_id INT,
    rating_id INT,
    rating_value INT CHECK(rating_value >= 0 AND rating_value <= 10),
    rating_reason TEXT,
    FOREIGN KEY (response_id) REFERENCES responses(id),
    FOREIGN KEY (rating_id) REFERENCES ratings(id)
);
```

## coding_conversation

```sql
CREATE TABLE coding_conversation (
  roomid VARCHAR(15) NOT NULL,
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  question TEXT NOT NULL,
  answer TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  modified_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (roomid) REFERENCES rooms(roomid),
  INDEX idx_roomid (roomid)
);
```

## coding_conversation_ratings

```sql
CREATE TABLE coding_conversation_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coding_conversation_id INT,
    rating_id INT,
    rating_value INT CHECK(rating_value >= 0 AND rating_value <= 10),
    rating_reason TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (coding_conversation_id) REFERENCES coding_conversation(id),
    FOREIGN KEY (rating_id) REFERENCES ratings(id)
);
```

## coding_hints

```sql
CREATE TABLE coding_hints (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coding_conversation_id INT,
    query TEXT NOT NULL,
    hint TEXT,
    case_triggered VARCHAR(15),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (coding_conversation_id) REFERENCES coding_conversation(id)
);
```

## conversations

```sql
CREATE TABLE conversation (
    roomid VARCHAR(15) NOT NULL,
    id INT AUTO_INCREMENT,
    text TEXT,
    speaker TINYINT(1) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (roomid) REFERENCES rooms(roomid)
    INDEX idx_roomid (roomid)
);
```

## conversation_ratings

```sql
CREATE TABLE conversation_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    conversation_id INT,
    rating_id INT,
    rating_value INT CHECK(rating_value >= 0 AND rating_value <= 10),
    rating_reason TEXT,
    FOREIGN KEY (conversation_id) REFERENCES conversation(id),
    FOREIGN KEY (rating_id) REFERENCES ratings(id)
);
```

## summary

```sql
CREATE TABLE summary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    roomid VARCHAR(15) NOT NULL,
    summary TEXT NOT NULL,
    other_info JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (roomid) REFERENCES rooms(roomid)
);
```

## ratings

```sql
CREATE TABLE ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rating_title VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expired TINYINT(1) NOT NULL DEFAULT 0
);
```

## initial_questions

```sql
CREATE TABLE initial_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expired TINYINT(1) NOT NULL DEFAULT 0
);
```

## user_journey_status

```sql
CREATE TABLE user_journey_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uid varchar(255) NOT NULL,
    config_version INT NOT NULL,
    checkpoint_id INT NOT NULL,
    status VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## user_tokens

```sql
CREATE TABLE user_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uid varchar(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expired TINYINT(1) NOT NULL DEFAULT 0
);
```

## user_assignment

```sql
CREATE TABLE `user_assignment` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uid VARCHAR(100) NOT NULL,
    assessment_id INT NOT NULL,
    deleted BOOLEAN DEFAULT false,

    created_by VARCHAR(255) NOT NULL,
    modified_by VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

    FOREIGN KEY (`assessment_id`) REFERENCES `assessment_pool` (`assessment_id`)
);
```

## assignment_submissions

```sql
CREATE TABLE `assignment_submissions` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uid VARCHAR(100) NOT NULL,
    github_link VARCHAR(255) NOT NULL,
    user_assignment_id INT,
    created_by VARCHAR(255) NOT NULL,
    modified_by VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_assignment_id`) REFERENCES `user_assignment` (`id`)
);
```

## time_slots

```sql
CREATE TABLE time_slots (
  id INT AUTO_INCREMENT PRIMARY KEY,
  roomid VARCHAR(15) NOT NULL,
  start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  preferred_time_range_start TIME NOT NULL DEFAULT '09:00:00',
  preferred_time_range_end TIME NOT NULL DEFAULT '21:00:00',

  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (roomid) REFERENCES rooms(roomid),

  INDEX idx_start_time (start_time),
  INDEX idx_end_time (end_time)
);
```

```sql
CREATE TABLE time_slots_availability (
  id INT AUTO_INCREMENT PRIMARY KEY,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  occupied_count INT NOT NULL DEFAULT 0,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## consumption_error_logs

```sql
CREATE TABLE consumption_error_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  message TEXT NOT NULL,
  error TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  modified_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  expired TINYINT(1) NOT NULL DEFAULT 0
);
```
