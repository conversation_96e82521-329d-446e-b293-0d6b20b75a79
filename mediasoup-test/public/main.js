const socket = io();
const mediasoup = require("mediasoup-client");
let device;
let producer;
let consumer;
let transportSend;
let transportRecv;
let roomId;
let peerId;
let params = {};

const joinRoom = (roomId) => {
  socket.emit("joinRoom", { roomId }, async ({ peerId: newPeerId }) => {
    peerId = newPeerId;
    console.log("Joined room with peer ID:", peerId);

    console.log("Getting router RTP capabilities");
    // Load the device with router RTP capabilities
    const routerRtpCapabilities = await new Promise((resolve) => {
      socket.emit("getRouterRtpCapabilities", { roomId }, resolve);
    });
    console.log("roomid: ", roomId);
    console.log("device loading");
    device = new mediasoup.Device();
    console.log("router rtp capabilities: ", routerRtpCapabilities);
    await device.load({ routerRtpCapabilities });

    console.log("device rtpCapabilities:", device.rtpCapabilities);

    // Create transport for sending media
    socket.emit(
      "createTransport",
      { roomId, peerId, type: "send" },
      async ({ id, iceParameters, iceCandidates, dtlsParameters }) => {
        console.log("transport iceParameters: ", iceParameters);
        transportSend = device.createSendTransport({
          id,
          iceParameters,
          iceCandidates,
          dtlsParameters,
        });
        console.log("device rtp capabilities:", device.rtpCapabilities);
        console.log("Transport created");
        transportSend.on("connect", ({ dtlsParameters }, callback, errback) => {
          socket.emit(
            "connectTransport",
            { roomId, peerId, dtlsParameters, type: "send" },
            callback
          );
        });

        transportSend.on(
          "produce",
          async ({ kind, rtpParameters }, callback, errback) => {
            socket.emit(
              "produce",
              { roomId, peerId, kind, rtpParameters },
              ({ id }) => {
                callback({ id });
              }
            );
          }
        );
        console.log("getting media stream");

        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true,
        });
        console.log("media stream created");
        const videoTrack = stream.getVideoTracks()[0];
        // const audioTrack = stream.getAudioTracks()[0];

        videoProducer = await transportSend.produce({ track: videoTrack });
        // audioProducer = await transportSend.produce({ track: audioTrack });

        // document.querySelector("#localVideo").srcObject = stream;
        console.log("Producer created");
      }
    );
    socket.emit(
      "createTransport",
      { roomId, peerId, type: "recv" },
      connectRecvTransport
    );
  });
};

socket.on("connect", async () => {
  console.log("Connected to server with ID:", socket.id);

  roomId = window.location.href.split("/")[4];
  console.log("roomID from url: ", roomId);
  if (!roomId)
    socket.emit("createRoom", ({ roomId: newRoomId }) => {
      roomId = newRoomId;
      console.log("Room created with ID:", roomId);
      joinRoom(roomId);
    });
  else joinRoom(roomId);
});

async function createRecvTransport() {
  console.log("ConnectRecvTransport called");
}

function connectRecvTransport({
  id,
  iceParameters,
  iceCandidates,
  dtlsParameters,
}) {
  transportRecv = device.createRecvTransport({
    id: id,
    iceParameters: iceParameters,
    iceCandidates: iceCandidates,
    dtlsParameters: dtlsParameters,
  });

  transportRecv.on("connect", ({ dtlsParameters }, callback, errback) => {
    socket.emit(
      "connectTransport",
      { roomId, peerId, dtlsParameters, type: "recv" },
      callback
    );
  });

  socket.on("newProducer", async ({producerId}) => {
    console.log("New producer available");
    await consume(producerId);
  });
}

async function consume(prodId) {
  console.log("Consume called");
  const {
    id,
    producerId,
    kind,
    rtpParameters,
    transport: transportInfo,
  } = await new Promise((resolve) => {
    socket.emit(
      "consume",
      { roomId, peerId, producerId: prodId, rtpCapabilities: device.rtpCapabilities },
      resolve
    );
  });

  console.log("TransportRecv id:", id);

  consumer = await transportRecv.consume({
    id,
    producerId,
    kind,
    rtpParameters,
  });

  const stream = new MediaStream();
  stream.addTrack(consumer.track);

  console.log("setting remote video");

  console.log("kind: ", kind);

  if (kind === "video") {
    const remoteElements = document.getElementById("remoteElements");
    let videoElement = remoteElements.appendChild(
      document.createElement("video")
    );
    videoElement.srcObject = stream;
    videoElement.play();
  } else if (kind === "audio") {
    const remoteElements = document.getElementById("remoteElements");
    let audioElement = remoteElements.appendChild(
      document.createElement("audio")
    );
    audioElement.srcObject = stream;
    audioElement.play();
  }
}
