PORT=3001
MEDIASOUP_LISTEN_IP='0.0.0.0'

FIREBASE_API_KEY="AIzaSyA1bSN3fFdwtgpLtG_W_NcAd2WjUM2px-E"

AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=hyrr;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
AZURE_STORAGE_CONTAINER_NAME="audio-recordings"

STATIC_TOKEN="w0321lud6t47h9x4lahuwiivh2yhisirno5hy3ekvx5ztp57lxcyman1w3mfsxdvhtik8tgt02bb29t6q8ot52sn0wi8lb0ir2uvl1ijm3bpwnqmom4he00p6oq7of7pxauhlqhsyj0wksyipq8gczxhxt791dgfe7uhs4nav9vedft85eecdtn8h4ejewsmviglu0cl"

SPEECH_TO_TEXT="azure"
AZURE_SPEECH_KEY="e2e78f3ef30b4016afe2add7671e5d8d"
AZURE_SPEECH_REGION="centralindia"

BASE_URL="http://localhost:5005/"

PYTHON_SERVER="http://localhost:3003/"

DISCOVERY_SERVER_URL="http://localhost:3004/"

RECORD_FILE_LOCATION_PATH="/mnt/fileshare/recordings"

AMQP_URL="amqp://Neusort_Test:Eval@9900@************:5672/?heartbeat=60&frameMax=8192"

DELAYED_EXCHANGE="delayed_exchange"

END_INTERVIEW_DELAY_QUEUE="end-interview-delay-queue"
ROOM_DEALLOCATION_QUEUE="room-deallocation-queue"
PYTHON_SERVER_PROCESS_QUEUE="python-server-process-queue"
