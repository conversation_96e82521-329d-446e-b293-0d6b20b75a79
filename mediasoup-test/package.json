{"name": "mediasoup-test", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon src/index.js", "bot": "nodemon connectBot.js", "watch": "watchify public/main.js -o public/bundle.js -v", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/storage-blob": "^12.23.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "amqp-connection-manager": "^4.1.14", "dotenv": "^16.4.5", "express": "^4.19.2", "firebase-admin": "^12.1.1", "fluent-ffmpeg": "^2.1.3", "mediasoup": "^3.14.6", "mediasoup-client": "^3.7.8", "microsoft-cognitiveservices-speech-sdk": "^1.42.0", "ps-list": "^8.1.1", "redaxios": "^0.5.1", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.0"}}