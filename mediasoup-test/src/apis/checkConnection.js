const { rooms } = require("../cache");

const checkConnection = async (req, res) => {
  try {
    const roomId = req.body.roomId;
    const peerId = res.locals.uid;
    const room = rooms[roomId];

    const peer = room?.peers[peerId];
    if (peer?.socket) {
      return res.status(200).json({
        success: true,
        code: "connected",
        message: "Already connected",
      });
    } else {
      return res.status(200).json({
        success: true,
        code: "not_connected",
        message: "Not connected",
      });
    }
  } catch (err) {
    console.error("Error checking connection", err);
    res.status(500).json({ success: false, error: err });
  }
};

module.exports = checkConnection;
