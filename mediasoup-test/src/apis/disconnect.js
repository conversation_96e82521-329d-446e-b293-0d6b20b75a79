const { rooms } = require("../cache");

const disconnect = async (req, res) => {
  try {
    const roomId = req.body.roomId;
    const peerId = res.locals.uid;
    const room = rooms[roomId];

    const peer = room?.peers[peerId];
    if (peer?.socket) {
      peer.socket.emit("connectedElsewhere");
      peer.socket.disconnect();
      delete room.peers[peerId];
      return res.status(200).json({
        success: true,
        code: "disconnected",
        message: "Disconnected",
      });
    } else {
      return res.status(200).json({
        success: true,
        code: "not_connected",
        message: "Not connected",
      });
    }
  } catch (err) {
    console.error("Error disconnecting", err);
    res.status(500).json({ success: false, error: err });
  }
};

module.exports = disconnect;