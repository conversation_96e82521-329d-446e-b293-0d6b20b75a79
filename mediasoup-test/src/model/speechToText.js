const speechsdk = require("microsoft-cognitiveservices-speech-sdk");
const phrases = require("../constants/phrases.js");
const { rooms } = require("../cache/index.js");
const { botId } = require("../constants/roomConstants.js");

class BaseSpeechToText {
  constructor(peerId, roomId) {
    this.peerId = peerId;
    this.roomId = roomId;
  }

  sendTranscribing(text) {
    try {
      const peers = rooms[this.roomId].peers;
      peers[this.peerId]?.socket?.emit("transcribing", text);
    } catch (error) {
      console.error("Error sendTranscribing():", this.roomId, this.peerId, error);
    }
  }

  sendTranscript(text) {
    try {
      const peers = rooms[this.roomId].peers;
      peers[this.peerId]?.socket?.emit("transcript", text);
    } catch (error) {
      console.error("Error sendTranscript():", this.roomId, this.peerId, error);
    }
  }

  onStartSpeaking() {
    try {
      const peers = rooms[this.roomId].peers;
      peers[botId]?.socket?.emit("intervieweeSpeaking", {});
    } catch (error) {
      console.error("Error onStartSpeaking():", this.roomId, this.peerId, error);
    }
  };
}

class AzureSTT extends BaseSpeechToText {
  constructor(peerId, roomId) {
    super(peerId, roomId);
    this.initialized = false;
    this.initializing = false;
    this.awaitingRecognizedEvent = null;
    this.stopSpeakingTimer = null;
    this.sendAudioToAzure = false;
    this.recognizer = null;
    this.audioStream = null;
    this.speechEvents = null;
  }

  async initializeRecognizer(restart = false) {
    this.initializing = true;
    const speechConfig = speechsdk.SpeechConfig.fromSubscription(
      process.env.AZURE_SPEECH_KEY,
      process.env.AZURE_SPEECH_REGION
    );
    speechConfig.speechRecognitionLanguage = 'en-IN';

    this.awaitingRecognizedEvent = false;
    this.stopSpeakingTimer = null;

    const deviceConfig = {
      sampleRate: 16000,
      sampleSize: 16,
      channelCount: 1,
    }

    this.audioStream = speechsdk.AudioInputStream.createPushStream(
      speechsdk.AudioStreamFormat.getWaveFormatPCM(
        deviceConfig.sampleRate,
        deviceConfig.sampleSize,
        deviceConfig.channelCount
      )
    );

    const audioConfig = speechsdk.AudioConfig.fromStreamInput(this.audioStream);

    this.recognizer = new speechsdk.SpeechRecognizer(speechConfig, audioConfig);

    speechsdk.PhraseListGrammar.fromRecognizer(this.recognizer).addPhrases(phrases);

    this.recognizer.recognizing = (s, e) => {
      const resultJson = JSON.parse(e.result.json);
      const text = resultJson.Text;
      this.sendTranscribing(text);
      console.log('recognizing', text);

      if (this.stopSpeakingTimer) clearTimeout(this.stopSpeakingTimer);

      if (!this.awaitingRecognizedEvent) this.onStartSpeaking();

      this.awaitingRecognizedEvent = true;

      this.stopSpeakingTimer = setTimeout(() => {
        if (this.awaitingRecognizedEvent) {
          this.awaitingRecognizedEvent = false;
          this.sendTranscript(text);
          this.sendTranscribing('');
        }
      }, 1500);
    };

    this.recognizer.recognized = (s, e) => {
      if (this.awaitingRecognizedEvent) {
        clearTimeout(this.stopSpeakingTimer);
        this.awaitingRecognizedEvent = false;

        if (e.result.reason === speechsdk.ResultReason.RecognizedSpeech) {
          console.log('RECOGNIZED: Text=' + e.result.text);
          setTimeout(() => {
            this.sendTranscribing('');
          }, 1500);
          this.sendTranscript(e.result.text);
        } else if (e.result.reason === speechsdk.ResultReason.NoMatch) {
          console.log('NOMATCH: Speech could not be recognized.');
        }
      }
    };

    this.recognizer.sessionStarted;

    this.recognizer.canceled = (s, e) => {
      console.log(`CANCELED: Reason=${e.reason}`);
      if (e.reason === speechsdk.CancellationReason.Error) {
        console.log(`CANCELED: ErrorCode=${e.errorCode}`);
        console.log(`CANCELED: ErrorDetails=${e.errorDetails}`);
      }
      this.recognizer.stopContinuousRecognition();
    };

    this.recognizer.sessionStopped = (s, e) => {
      console.log("Azure STT Session stopped");
      this.recognizer.stopContinuousRecognition();
    };

    return new Promise((resolve, reject) => {
      this.recognizer.startContinuousRecognitionAsync(
        () => {
          if (!restart) {
            this.sendAudioToAzure = true;
          }
          this.initialized = true;
          this.initializing = false;
          console.log("Azure STT start", this.roomId, this.peerId);
          resolve()
        },
        (err) => {
          console.log('Error starting continuous recognition', err);
          reject(err);
        }
      );
    });
  }

  async restart() {
    console.log("Azure STT restart", this.roomId, this.peerId);
    await this.close();
    await this.initializeRecognizer(true);
  }

  start() {
    if (!this.initialized) return;
    console.log("Azure STT start", this.roomId, this.peerId);
    this.sendAudioToAzure = true;
  }

  stop() {
    if (!this.initialized) return;
    console.log("Azure STT stop", this.roomId, this.peerId);
    this.sendAudioToAzure = false;
  }

  transcribeAudio(audioBuffer) {
    try {
      if (!this.sendAudioToAzure || this.initializing || !this.initialized) return;
      const buffer = audioBuffer.buffer.slice(audioBuffer.byteOffset, audioBuffer.byteOffset + audioBuffer.byteLength);
      this.audioStream.write(buffer);
    } catch (err) {
      console.error('Error transcribing audio:', this.roomId, this.peerId, err);
    }
  }

  async close() {
    this.initialized = false;
    if (this.stopSpeakingTimer) {
      clearTimeout(this.stopSpeakingTimer);
    }
    return new Promise((resolve, reject) => {
      if (this.recognizer) {
        try {
          this.recognizer?.close(() => {
            console.log("Azure STT close", this.roomId, this.peerId);
            this.recognizer = null;
          });
        } catch (error) {
          this.recognizer = null;
          console.log("Error closing recognizer", this.roomId, this.peerId, error);
        } finally {
          resolve();
        }
      } else {
        resolve();
      }
    });
  }
}

class SpeechToTextFactory {
  static createSpeechToTextService(service, peerId, roomId) {
    switch (service) {
      case "azure":
        return new AzureSTT(peerId, roomId);
      default:
        return new AzureSTT(peerId, roomId);
    }
  }
}

module.exports = { SpeechToTextFactory };
