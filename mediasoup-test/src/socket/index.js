const { rooms } = require("../cache");
const { authMiddlewareSocket } = require("../middleware/authMiddleware");
const connectTransportHandler = require("./eventHandlers/connectTransportHandler");
const consumeHandler = require("./eventHandlers/consumeHandler");
const createRoomHandler = require("./eventHandlers/createRoomHandler");
const createTransportHandler = require("./eventHandlers/createTransportHandler");
const consumeDataHandler = require("./eventHandlers/dataConsumerHandler");
const dataProducerHandler = require("./eventHandlers/dataProducerHandler");
const disconnectHandler = require("./eventHandlers/disconnectHandler");
const endInterviewHandler = require("./eventHandlers/endInterviewHandler");
const endTutorialHandler = require("./eventHandlers/endTutorialHandler");
const getRouterRtpCapabilitiesHandler = require("./eventHandlers/getRouterRtpCapabilitiesHandler");
const joinRoomHandler = require("./eventHandlers/joinRoomHandler");
const produceHandler = require("./eventHandlers/produceHandler");
const resumeConsumerHandler = require("./eventHandlers/resumeConsumerHandler");
const sendInputData = require("./eventHandlers/sendInputData");
const sendVolumeData = require("./eventHandlers/sendVolumeData");
const toggleMode = require("./eventHandlers/toggleMode");
const togglePause = require("./eventHandlers/togglePause");
const IntervalManager = require("../utils/intervalManager");
const { pingPongInterval } = require("../constants/intervalTimes");
const { botId } = require("../constants/roomConstants");
const tabFocusChange = require("./eventHandlers/tabFocusChange");
const roomJoined = require("./eventHandlers/roomJoined");

const socketHandler = (io) => {
  console.log("Socket handler initialized");

  io.use(authMiddlewareSocket);

  io.on("connection", (socket) => {
    console.log(`Socket connected: ${socket.id}`, socket.uid, botId);
    let taskId = null;

    // Ping-pong mechanism to check if the BOT is still connected
    if (socket.uid === botId) {
      let lastHeartbeat = Date.now();

      taskId = IntervalManager.addTask(
        () => {
          const timeElapsed = Date.now() - lastHeartbeat;
          if (timeElapsed > pingPongInterval * 5 * 1000) {
            console.log(`Client ${socket.id} timed out - no heartbeat`);

            IntervalManager.removeTask(taskId);
            socket.disconnect();
            disconnectHandler(socket);
            return;
          }

          socket.emit("ping");
        },
        pingPongInterval,
        "ping-pong"
      );

      IntervalManager.startTask(taskId);

      socket.on("pong", () => {
        lastHeartbeat = Date.now();
      });
    }

    socket.on("getRouterRtpCapabilities", getRouterRtpCapabilitiesHandler);

    socket.on("createRoom", (...params) =>
      createRoomHandler(...params, socket)
    );
    socket.on("joinRoom", (...params) => joinRoomHandler(...params, socket));

    socket.on("createTransport", (...params) =>
      createTransportHandler(...params, socket)
    );
    socket.on("connectTransport", connectTransportHandler);
    socket.on("produce", produceHandler);
    socket.on("produceData", dataProducerHandler);
    socket.on("consume", consumeHandler);
    socket.on("consumeData", consumeDataHandler);

    socket.on("resumeConsumer", (...params) =>
      resumeConsumerHandler(...params, socket)
    );

    socket.on("inputData", (...params) => {
      sendInputData(...params, socket);
    });

    socket.on("volumeData", (...params) => {
      sendVolumeData(...params, socket);
    });

    socket.on("togglePause", (...params) => {
      togglePause(...params, socket);
    });

    socket.on("toggleMode", (...params) => {
      toggleMode(...params, socket);
    });

    socket.on("tabFocusChange", (...params) => {
      tabFocusChange(...params, socket);
    });

    socket.on("roomJoined", (...params) => {
      roomJoined(...params, socket);
    });

    socket.on("endInterview", (...params) => {
      if (taskId) IntervalManager.removeTask(taskId);
      endInterviewHandler(...params, socket);
    });

    socket.on("endTutorial", (...params) => {
      if (taskId) IntervalManager.removeTask(taskId);
      endTutorialHandler(...params, socket);
    });

    socket.on("disconnect", () => {
      if (taskId) IntervalManager.removeTask(taskId);
      disconnectHandler(socket);
    });
  });
};

module.exports = socketHandler;
