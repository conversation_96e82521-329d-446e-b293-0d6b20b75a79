const { rooms } = require("../../cache");
const { gazeAiBotId } = require("../../constants/roomConstants");

const sendVolumeData = async (
  { roomId, data },
  callback,
  socket
) => {
  try {
    const room = rooms[roomId];
    room?.peers[gazeAiBotId]?.socket?.emit("volumeData", data);
    callback({ success: true });
  } catch (error) {
    console.error("Error in sendVolumeData:", error);
  }
};

module.exports = sendVolumeData;
