const { rooms, socketMap } = require("../../cache");
const { gazeAiBotId } = require("../../constants/roomConstants");
const stopRecord = require("../../recordProcess/utils/stopRecord");
const joinGazeAI = require("../../utils/joinGazeAI");
const endTutorialHandler = require("./endTutorialHandler");

const disconnectHandler = async (socket) => {
  try {
    if (!socketMap[socket.id]) {
      console.log(`No socket found with id ${socket.id} while disconnecting`);
      return;
    }
    const { roomId, peerId } = socketMap[socket.id];

    console.log(
      `user disconnected with roomId ${roomId} and peerId ${peerId} top`
    );

    const room = rooms[roomId];
    if (!room) {
      console.log(
        `Room ${roomId} does not exist in cache when peer ${peerId} disconnected`
      );
      return;
    }

    const peer = room.peers[peerId];
    if (!peer) {
      console.log(
        `Peer ${peerId} does not exist in room ${roomId} when disconnected`
      );
      return;
    }

    if (peer.consumers)
      Object.keys(peer.consumers).forEach((consumer) => {
        peer.consumers[consumer].close();
      });

    if (peer.dataConsumers) {
      Object.keys(peer.dataConsumers).forEach((dataConsumer) => {
        peer.dataConsumers[dataConsumer].close();
      });
    }

    if (peer.producers)
      Object.keys(peer.producers).forEach((producer) => {
        peer.producers[producer].close();
      });

    if (peer.role === "interviewee") {
      room.speechToText.close();
    } 

    peer.producer?.close();

    peer.dataProducer?.close();

    peer.transportSend?.close();

    peer.transportRecv?.close();

    Object.keys(room.peers).forEach((peer) => {
      room.peers[peer].socket.emit("peerDisconnected", { peerId });
    });

    if (room.isTutorial) {
      endTutorialHandler({ roomId, peerId }, () => { });
    }

    if (!room.isDemoRoom && !room.isTutorial) {
      stopRecord(room, peerId, roomId);
    }

    delete room.peers[peerId];

    // Reconnect GazeAi bot if it disconnects
    if (peerId === gazeAiBotId) {
      console.log("GazeAi bot disconnected from room", roomId);
      const room = rooms[roomId];

      if (room) {
        await joinGazeAI(roomId, room.companyId, room.intervieweeId);
        console.log("GazeAi bot rejoined room", roomId);
      }
    }
  } catch (error) {
    console.log("Error in disconnectHandler", error);
  }
};

module.exports = disconnectHandler;
