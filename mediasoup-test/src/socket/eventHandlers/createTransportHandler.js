const { botId } = require("../../../../server/src/constants/roomConstants");
const { rooms } = require("../../cache");
const consumeDataHandler = require("./dataConsumerHandler");

const createTransportHandler = async (
  { roomId, peerId, type },
  callback,
  socket
) => {
  try {
    console.log(
      `creating ${type} transport for peer ${peerId} in room ${roomId}`
    );
    const room = rooms[roomId];
    if (!room) {
      console.log(`room ${roomId} not found while creating ${type} transport`);
      return callback({ error: "Room not found" });
    }

    const peer = room.peers[peerId];
    if (!peer) {
      console.log(`peer ${peerId} not found while creating ${type} transport`);
      return callback({ error: "Peer not found" });
    }

    const { router } = room;
    const isProduction = process.env.PORT == 443;
    const transport = await router.createWebRtcTransport({
      listenInfos: [
        {
          protocol: "udp",
          ip: isProduction
            ? process.env.MEDIASOUP_LISTEN_IP || "127.0.0.1"
            : "127.0.0.1",
          announcedAddress: isProduction
            ? process.env.MEDIASOUP_ANNOUNCED_IP || null
            : null,
        },
        {
          protocol: "tcp",
          ip: isProduction
            ? process.env.MEDIASOUP_LISTEN_IP || "127.0.0.1"
            : "127.0.0.1",
          announcedAddress: isProduction
            ? process.env.MEDIASOUP_ANNOUNCED_IP || null
            : null,
        },
      ],
      enableUdp: true,
      enableTcp: true,
      enableSctp: true,
      // preferUdp: true,
    });
    if (type === "send") peer.transportSend = transport;
    else if (type === "recv") peer.transportRecv = transport;

    console.log(
      `${type} transport created for room ${roomId} and peer ${peerId}`
    );

    callback({
      id: transport.id,
      iceParameters: transport.iceParameters,
      iceCandidates: transport.iceCandidates,
      dtlsParameters: transport.dtlsParameters,
      sctpParameters: transport.sctpParameters,
    });

    if (type === "recv") {
      console.log(
        `Sending streams of previously connected producers in room ${roomId} to peer ${peerId}`
      );
      console.log("SocketID:", socket.id);
      Object.keys(room.peers).forEach(async (key) => {
        if (key === peerId) return;
        Object.keys(room.peers[key].producers).forEach((producerId) => {
          const producer = room.peers[key].producers[producerId];
          console.log(`Sending producer ${producer.id} to peer ${peerId}`);
          socket.emit("newProducer", {
            producerId: producer.id,
            peerId: key,
          });
        });
        if (room.peers[key].dataProducer) {
          const dataProducer = room.peers[key].dataProducer;
          console.log(
            `Sending data producer ${dataProducer.id} to peer ${peerId} in room ${roomId}`
          );
          const dataConsumer = await consumeDataHandler({
            roomId,
            peerId,
            dataProducerId: dataProducer.id,
          });
          socket.emit("newDataConsumer", {
            peerId,
            id: dataConsumer.id,
            dataProducerId: dataConsumer.dataProducerId,
            sctpStreamParameters: dataConsumer.sctpStreamParameters,
            label: dataConsumer.label,
            protocol: dataConsumer.protocol,
            appData: dataProducer.appData,
          });
        }
      });
    }
  } catch (error) {
    console.error("Error in createTransportHandler:", error);
  }
};

module.exports = createTransportHandler;
