const { rooms } = require("../../cache");
const { gazeAiBotId } = require("../../constants/roomConstants");

const roomJoined = async ({ roomId, type, timestamp }, callback) => {
  try {
    console.log("Room Joined", roomId, type, timestamp);
    const room = rooms[roomId];
    const botSocket = room.peers[gazeAiBotId]?.socket;
    botSocket?.emit("tabFocusChange", { type, timestamp });
    botSocket?.emit("badNetworkInterval", {
      type,
      timestamp,
    });

    callback({ success: true });
  } catch (error) {
    console.error("Error roomJoined():", roomId, error);
  }
};

module.exports = roomJoined;
