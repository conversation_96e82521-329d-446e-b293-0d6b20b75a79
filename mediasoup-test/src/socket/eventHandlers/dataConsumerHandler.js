const { rooms } = require("../../cache");

const consumeDataHandler = async (
  { roomId, peerId, dataProducerId },
  callback = () => {}
) => {
  try {
    const room = rooms[roomId];
    if (!room) {
      return callback({ error: "Room not found" });
    }

    const transport = room.peers[peerId].transportRecv;
    if (!transport) {
      return callback({ error: "Transport not found" });
    }

    const dataConsumer = await transport.consumeData({
      dataProducerId,
    });

    if (!room.peers[peerId].dataConsumers) {
      room.peers[peerId].dataConsumers = {};
    }

    room.peers[peerId].dataConsumers[dataConsumer.id] = dataConsumer;

    callback({
      id: dataConsumer.id,
      dataProducerId: dataConsumer.dataProducerId,
    });

    return dataConsumer;
  } catch (error) {
    console.error("Error in consumeDataHandler:", error);
  }
};

module.exports = consumeDataHandler;
