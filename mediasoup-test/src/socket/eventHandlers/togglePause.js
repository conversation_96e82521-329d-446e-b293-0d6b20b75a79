const { rooms } = require("../../cache");
const { gazeAiBotId } = require("../../constants/roomConstants");

const togglePause = async ({ roomId, paused, peerId }, callback) => {
  try {
    console.log("Toggling pause in room", roomId, paused);
    const room = rooms[roomId];
    paused ? room.speechToText.stop() : room.speechToText.start();
    Object.keys(room.peers).forEach((key) => {
      if (key !== peerId && key !== gazeAiBotId) {
        room.peers[key]?.socket?.emit("togglePause", { paused });
      }
    });
    callback({ success: true });
  } catch (error) {
    console.error("Error togglePause():", roomId, error);
  }
};

module.exports = togglePause;
