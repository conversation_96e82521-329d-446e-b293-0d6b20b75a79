const { rooms } = require("../../cache");
const { gazeAiBotId } = require("../../constants/roomConstants");

const tabFocusChange = async ({ roomId, type, timestamp }, callback) => {
  try {
    console.log("TabFocus Change", roomId, type, timestamp);
    const room = rooms[roomId];
    room.peers[gazeAiBotId]?.socket?.emit("tabFocusChange", { type, timestamp });
    callback({ success: true });
  } catch (error) {
    console.error("Error tabFocusChange():", roomId, error);
  }
};

module.exports = tabFocusChange;
