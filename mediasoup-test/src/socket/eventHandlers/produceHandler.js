const { rooms } = require("../../cache");
const FFmpeg = require("../../recordProcess/ffmpeg");
const publishProducerRtpStream = require("../../recordProcess/utils/publishProducerRtpStream");
const startRecord = require("../../recordProcess/utils/startRecord");
const monitorProducerStats = require("../../utils/monitorProducerStats");
const setupSpeechToText = require("../../utils/setupSpeechToText");

const produceHandler = async (
  { roomId, peerId, kind, rtpParameters, appData },
  callback
) => {
  try {
    console.log(
      `${kind} data being produced by peer ${peerId} in room ${roomId}`
    );
    const room = rooms[roomId];
    if (!room) {
      console.log(
        `Room ${roomId} not found while producing for peer ${peerId}`
      );
      return callback({ error: "Room not found" });
    }

    const { transportSend } = room.peers[peerId];
    const producer = await transportSend.produce({
      kind,
      rtpParameters,
      appData,
    });

    if (room.peers[peerId].role === "interviewee") {
      room.intervieweeId = peerId;
      console.log("intervieweeId:", peerId);

      if (kind === 'audio') {
        let recordInfo = {};
        recordInfo[kind] = await publishProducerRtpStream(roomId, peerId, producer, "extractAudio");

        recordInfo.peerId = peerId;
        recordInfo.kind = producer.kind;
        recordInfo.source = producer.appData.source;

        const isTutorial = roomId.includes("_TUTORIAL");
        if (!isTutorial) {
          await setupSpeechToText({
            service: process.env.SPEECH_TO_TEXT,
            peerId,
            roomId
          });

          room.peers[peerId].rawAudioProcess = new FFmpeg(recordInfo, "extractAudio", (buffer) => {
            room.speechToText.transcribeAudio(buffer);
          });
        }
      }
      monitorProducerStats(producer, roomId, peerId);
    }

    room.peers[peerId].producers[producer.id] = producer;

    if (
      !room.isDemoRoom &&
      ((kind === "audio" && appData.source === "mic") ||
        (kind === "video" && appData.source === "screen") ||
        (kind === "video" && appData.source === "camera"))
    ) {
      console.log(
        "Starting recording for ROOM_ID:",
        roomId,
        "PEER_ID:",
        peerId
      );
      await startRecord(roomId, peerId, producer);
    }

    console.log(
      `Notifying other peers in room ${roomId} about new producer ${producer.id}`
    );
    Object.keys(room.peers).forEach((key) => {
      if (key !== peerId) {
        console.log(
          `Notifying peer ${key} in room ${roomId} about new producer ${producer.id}`
        );
        room.peers[key].socket.emit("newProducer", {
          producerId: producer.id,
          peerId: peerId,
        });
      }
    });

    callback({ id: producer.id });
  } catch (error) {
    console.error("Error in produceHandler:", error);
  }
};

module.exports = produceHandler;
