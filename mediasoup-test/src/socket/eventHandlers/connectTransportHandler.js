const { rooms } = require("../../cache");

const connectTransportHandler = async (
  { roomId, peerId, dtlsParameters, sctpParameters, type },
  callback
) => {
  try {
    console.log(
      `connecting ${type} transport for peer ${peerId} in room ${roomId}`
    );
    const room = rooms[roomId];
    if (!room) {
      return callback({ error: "Room not found" });
    }
    let transport;
    if (type === "send") transport = room.peers[peerId].transportSend;
    else if (type === "recv") transport = room.peers[peerId].transportRecv;
    await transport?.connect({ dtlsParameters, sctpParameters });

    callback({ connected: true });
  } catch (error) {
    console.error("Error in connectTransportHandler:", error);
  }
};

module.exports = connectTransportHandler;
