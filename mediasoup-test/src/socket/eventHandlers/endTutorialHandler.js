const { rooms } = require("../../cache");
const { botId } = require("../../constants/roomConstants");
const { pushToQueue } = require("../../utils/rbmq");

const endTutorialHandler = async ({ roomId, peerId }, callback) => {
  try {
    const room = rooms[roomId];

    console.log(`End tutorial called by peer ${peerId} in room ${roomId}`);
    if (!room) {
      console.log(
        `Room not found with roomId: ${roomId} while ending tutorial`
      );
      callback({ roomId, peerId });
      return;
    }

    const peer = room.peers[peerId];
    if (!peer && peerId !== botId) {
      console.log(
        `Peer not found with peerId: ${peerId} while ending tutorial`
      );
      callback({ roomId, peerId });
      return;
    }

    Object.keys(room.peers).forEach((key) => {
      room.peers[key].socket.emit("endTutorial", {});
    });

    room.router.close();
    delete rooms[roomId];

    callback({ roomId, peerId });

    await pushToQueue(
      process.env.ROOM_DEALLOCATION_QUEUE,
      { roomId: roomId }
    );
  } catch (error) {
    callback({ roomId, peerId });
    console.error("Error in endTutorialHandler:", error);
  }
};

module.exports = endTutorialHandler;
