const { rooms } = require("../../cache");
const { conversationModes, botId } = require("../../constants/roomConstants");

const toggleMode = async ({ roomId, mode }, callback) => {
  try {
    console.log("Toggling mode in room", roomId, mode);
    const room = rooms[roomId];
    if (mode === conversationModes.text) {
      await room.speechToText.close();
    } else {
      await room.speechToText.restart();
    }
    room.peers[botId]?.socket?.emit("toggleMode", { mode });
    callback({ success: true });
  } catch (error) {
    console.error("Error toggleMode():", roomId, error);
  }
};

module.exports = toggleMode;
