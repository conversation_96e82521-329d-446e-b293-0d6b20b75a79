const { rooms } = require("../../cache");
const stopRecord = require("../../recordProcess/utils/stopRecord");
const IntervalManager = require("../../utils/intervalManager");
const { pushToQueue, sendDelayedMessage } = require("../../utils/rbmq");

const endInterviewHandler = async ({ roomId, peerId, isDelayed }, callback) => {
  try {
    console.log(
      `End interview ${
        isDelayed ? "delayed" : ""
      } called by peer ${peerId} in room ${roomId}`
    );

    const room = rooms[roomId];

    if (!room) {
      console.log(
        `Room not found with roomId: ${roomId} while ending interview`
      );
      callback({ roomId, peerId });
      return;
    }

    const { isDemoRoom, createTime, peers, companyId } = room;

    rooms[roomId]?.speechToText?.close();

    if (room.producers) {
      Object.keys(room.producers).forEach((producerId) => {
        const taskId = room.producers[producerId];
        IntervalManager.removeTask(taskId);
        delete room.producers[producerId];
      });
    }

    console.log(`room create time is ${createTime} for roomId ${roomId}`);

    Object.keys(peers).forEach((key) => {
      peers[key].socket.emit("endInterview", {});
    });

    const recordingDir = `${roomId}_${createTime}`;

    try {
      callback({ roomId, peerId });
      await rooms[roomId]?.router?.close();
    } catch (error) {
      console.error("Error closing router:", error);
    }

    delete rooms[roomId];

    if (!isDemoRoom) {
      if (companyId !== "tdb8c32bde588491") {
        if (!isDelayed) {
          await sendDelayedMessage(
            process.env.PYTHON_SERVER_PROCESS_QUEUE,
            {
              recordingDir: recordingDir,
              intervieweeId: room.intervieweeId,
              companyId: companyId || "",
            },
            5 * 60 * 1000 // 5 minute
          );
        }
      }

      //! Stop Record
      const stopPromises = Object.keys(peers).map((key) =>
        stopRecord(room, key, roomId)
      );

      await Promise.all(stopPromises);
      console.log("All recordings stopped successfully!", roomId, companyId);
    }

    await pushToQueue(
      process.env.ROOM_DEALLOCATION_QUEUE,
      { roomId: roomId }
    );
  } catch (error) {
    callback({ roomId, peerId });
    console.error("Error ending interview:", error);
  }
};

module.exports = endInterviewHandler;
