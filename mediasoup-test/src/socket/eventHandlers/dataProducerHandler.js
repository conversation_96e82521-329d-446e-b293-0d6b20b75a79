const { rooms } = require("../../cache");
const consumeDataHandler = require("./dataConsumerHandler");

const dataProducerHandler = async (
  { roomId, peerId, sctpStreamParameters, label, protocol, appData },
  callback
) => {
  try {
    const room = rooms[roomId];
    if (!room) {
      return callback({ error: "Room not found" });
    }
    const transport = room.peers[peerId].transportSend;
    if (!transport) {
      return callback({ error: "Transport not found" });
    }

    const dataProducer = await transport.produceData({
      sctpStreamParameters,
      label,
      protocol,
      appData,
    });

    room.peers[peerId].dataProducer = dataProducer;

    console.log(
      `Notifying other peers in room ${roomId} about new data producer ${dataProducer.id}`
    );
    Object.keys(room.peers).forEach(async (key) => {
      if (key !== peerId) {
        console.log(
          `Notifying peer ${key} in room ${roomId} about new producer ${dataProducer.id}`
        );

        const dataConsumer = await consumeDataHandler({
          roomId,
          peerId: key,
          dataProducerId: dataProducer.id,
          receivedId: peerId,
        });

        room.peers[key].socket.emit("newDataConsumer", {
          peerId,
          id: dataConsumer.id,
          dataProducerId: dataConsumer.dataProducerId,
          sctpStreamParameters: dataConsumer.sctpStreamParameters,
          label: dataConsumer.label,
          protocol: dataConsumer.protocol,
          appData: dataProducer.appData,
        });
      }
    });

    callback({
      id: dataProducer.id,
      sctpStreamParameters: dataProducer.sctpStreamParameters,
    });
  } catch (error) {
    console.log("Error in dataProducerHandler:", error);
  }
};

module.exports = dataProducerHandler;
