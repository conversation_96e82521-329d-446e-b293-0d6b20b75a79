const { rooms } = require("../../cache");

const consumeHandler = async (
  { roomId, peerId, producerId, producerPeerId, rtpCapabilities },
  callback
) => {
  try {
    console.log(
      `consume request for producer id: ${producerId} in room ${roomId} by peer ${peerId}`
    );
    const room = rooms[roomId];
    if (!room) {
      console.log(`Room ${roomId} not found while consuming`);
      return callback({ error: "Room not found" });
    }

    const { router } = room;
    if (!router.canConsume({ producerId, rtpCapabilities })) {
      console.log(
        `Cannot consume producer id: ${producerId} in room ${roomId}`
      );
      return callback({ error: "Cannot consume" });
    }

    const producerPeer = room.peers[producerPeerId];
    const producer = producerPeer.producers[producerId];
    if (!producer) {
      console.log(
        `Producer ${producerId} not found for peer ${producerPeerId}`
      );
      return callback({ error: "Producer not found" });
    }

    let { transportRecv } = room.peers[peerId];
    if (!transportRecv) {
      callback({ error: "Transport not found" });
      return;
    }

    const consumer = await transportRecv.consume({
      producerId,
      rtpCapabilities,
      paused: true,
      // pipe: true,
    });

    if (!room.peers[peerId].consumers) room.peers[peerId].consumers = {};
    room.peers[peerId].consumers[consumer.id] = consumer;

    callback({
      id: consumer.id,
      producerId: consumer.producerId,
      kind: consumer.kind,
      rtpParameters: consumer.rtpParameters,
      appData: producer.appData,
    });
  } catch (error) {
    console.error("Error in consumeHandler:", error);
  }
};

module.exports = consumeHandler;
