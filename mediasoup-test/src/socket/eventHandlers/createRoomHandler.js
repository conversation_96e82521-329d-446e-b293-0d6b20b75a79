const { v4: uuidv4 } = require("uuid");
const fs = require("fs");
const { rooms } = require("../../cache");
const { mediasoupConfig } = require("../../config");
const getMediasoupWorker = require("../../utils/getMediasoupWorker");

const createRoomHandler = async ({ roomId, companyId }, callback, socket) => {
  try {
    console.log(
      `createRoom<PERSON><PERSON><PERSON> called with roomID: ${roomId}, companyId: ${companyId}`
    );
    if (!roomId) roomId = uuidv4();
    const isTutorial = roomId.includes("_TUTORIAL");
    const worker = getMediasoupWorker();
    const router = await worker.createRouter({
      mediaCodecs: mediasoupConfig.codecs
        ?.filter(
          (c) =>
            c.mimeType !== "audio/SILK" &&
            c.mimeType !== "audio/CN" &&
            c.mimeType !== "audio/telephone-event"
        )
        .map((c) => {
          delete c.preferredPayloadType;
          return c;
        }),
      headerExtensions: mediasoupConfig.headerExtensions,
    });

    const timestamp = new Date();
    const time = timestamp.getTime();

    rooms[roomId] = {
      router,
      peers: {},
      gstreamer: {},
      createTime: time,
      isDemoRoom: {},
      intervieweeId: {},
      isTutorial: isTutorial,
      companyId: companyId || "",
      isDelayedMessageSent: false,
    };

    const File_Path = process.env.RECORD_FILE_LOCATION_PATH
      ? process.env.RECORD_FILE_LOCATION_PATH
      : "./files";

    rooms[roomId].isDemoRoom = ["a98faaef", "demoairtel1", "demoairtel2", "demoairtel3", "9ez2-lx49-oavz"].includes(roomId);

    if (!rooms[roomId].isDemoRoom) {
      if (isTutorial) {
        fs.mkdir(`${File_Path}/${roomId}`, { recursive: true }, (err) => {
          if (err) {
            console.error(err);
          } else {
            console.log(`Folder created successfully! ${roomId}_${time}`);
          }
        });
      } else {
        fs.mkdir(`${File_Path}/${roomId}_${time}`, { recursive: true }, (err) => {
          if (err) {
            console.error(err);
          } else {
            console.log(`Folder created successfully! ${roomId}_${time}`);
          }
        });
      }
    }

    console.log("NEW ROOM CREATED:", roomId);

    callback({ roomId });
  } catch (error) {
    console.error("Error in createRoomHandler:", error);
  }
};

module.exports = createRoomHandler;
