const { rooms } = require("../../cache");

const resumeConsumerHandler = async ({ roomId, consumerId }, callback, socket) => {
  try {
    const room = rooms[roomId];
    if (!room) {
      console.log(
        `Room ${roomId} not found while resuming consumer for peer ${socket.uid}`
      );
      return callback({ error: "Room not found" });
    }

    const peer = room.peers[socket.uid];
    if (!peer) {
      console.log(
        `Peer ${socket.uid} not found while resuming consumer in room ${roomId}`
      );
      return callback({ error: "Peer not found" });
    }

    const consumer = peer.consumers[consumerId];
    if (!consumer) {
      console.log(`Consumer ${consumerId} not found for peer ${socket.uid}`);
      return callback({ error: "Consumer not found" });
    }

    await consumer.resume();

    callback({ resumed: true });
  } catch (error) {
    console.error("Error in resumeConsumerHandler:", error);
  }
};

module.exports = resumeConsumerHandler;
