const { rooms } = require("../../cache");

const getRouterRtpCapabilitiesHandler = async ({ roomId }, callback) => {
  try {
    console.log("getRouterRtpCapabilitiesHandler called for roomId:", roomId);
    const room = rooms[roomId];
    if (!room) {
      console.log(
        `Room with roomID: ${roomId} not found while getting router rtp capabilities`
      );
      return callback({ error: "Room not found" });
    }

    callback(room.router.rtpCapabilities);
  } catch (error) {
    console.error("Error in getRouterRtpCapabilitiesHandler:", error);
  }
};

module.exports = getRouterRtpCapabilitiesHandler;
