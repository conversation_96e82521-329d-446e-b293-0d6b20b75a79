const { rooms, socketMap } = require("../../cache");
const { gazeAiBotId } = require("../../constants/roomConstants");
const joinGazeAI = require("../../utils/joinGazeAI");
const { sendDelayedMessage } = require("../../utils/rbmq");
const createRoomHandler = require("./createRoomHandler");

const joinRoomHandler = async ({ roomId, companyId }, callback, socket) => {
  try {
    const peerId = socket.uid;
    const peerName = socket.name;
    const role = socket.role;

    let room = rooms[roomId];
    if (!room) {
      if (peerId === gazeAiBotId) {
        callback({ peerId, peerName, role });
        console.log("room not found with roomId:", roomId, "while joining room for gazeAiBot");
        return;
      }
      console.log("Room not found with roomId:", roomId, "while joining room");
      await createRoomHandler(
        { roomId, companyId },
        ({ roomId }) => {
          room = rooms[roomId];
        },
        socket
      );
      // callback({ error: "Room not found" });
    }

    if (room.peers[peerId]?.socket) {
      callback({ error: "Already joined the room" });
      return;
    }

    room.peers[peerId] = {
      socket,
      peerName,
      role,
      consumers: {},
      producers: {},
    };

    socketMap[socket.id] = { roomId, peerId };

    Object.keys(room.peers).forEach((key) => {
      if (key === peerId) return;
      socket.emit("newPeer", {
        peerId: key,
        peerName: room.peers[key].peerName,
        role: room.peers[key].role,
      });
    });

    Object.keys(room.peers).forEach((key) => {
      if (key === peerId) return;
      room.peers[key].socket.emit("newPeer", { peerId, peerName, role });
    });

    callback({ peerId, peerName, role });

    if (!room.peers[gazeAiBotId]) {
      console.log("GazeAI bot not found in room", roomId, "while joining room");
      console.log("Joining GazeAI bot to room", roomId);
      await joinGazeAI(roomId, companyId, room.intervieweeId);
      console.log("GazeAI bot joined to room", roomId);
    }
    if (!room.isDelayedMessageSent && role === "interviewee") {
      console.log("sending end interview delayed message to queue", roomId, companyId, peerId);
      room.isDelayedMessageSent = true;
      if (!room.isDemoRoom && !room.isTutorial) {
        await sendDelayedMessage(
          process.env.END_INTERVIEW_DELAY_QUEUE,
          {
            companyId,
            roomId,
            recordingDir: `${roomId}_${room.createTime}`,
            intervieweeId: peerId
          },
          2 * 60 * 60 * 1000 // 2 hours
        );
      }
    }
  } catch (error) {
    console.error("Error in joinRoomHandler:", error);
  }
};

module.exports = joinRoomHandler;
