const { rooms } = require("../../cache");
const { gazeAiBotId } = require("../../constants/roomConstants");

const sendInputData = async (
  { roomId, keyData, botThinking, inputCount },
  callback,
  socket
) => {
  try {
    const room = rooms[roomId];
    room.peers[gazeAiBotId]?.socket?.emit("inputData", {
      keyData,
      inputCount: [
        inputCount.start_time,
        inputCount.key_count,
        inputCount.mouse_movement_count,
        inputCount.bot_speaking,
        inputCount.question,
      ],
      botThinking: botThinking,
    });

    callback({ success: true });
  } catch (error) {
    console.error("Error in sendInputData:", error);
  }
};

module.exports = sendInputData;
