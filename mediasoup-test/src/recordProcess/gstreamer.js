const child_process = require("child_process");
const os = require("os");
const path = require("path");
const fs = require("fs");
const { EventEmitter } = require("events");
const {
  getCodecInfoFromRtpParameters,
} = require("./utils/getCodecInfoFromRtpParameters");
const numCPUs = os.cpus().length;
const GSTREAMER_COMMAND = "gst-launch-1.0";
const GSTREAMER_OPTIONS = "-v -e --gst-debug=4";

module.exports = class GStreamer {
  constructor(rtpParameters) {
    this._rtpParameters = rtpParameters;
    this.kind = rtpParameters.kind;
    this._process = undefined;
    this._observer = new EventEmitter();
    this._createProcess();
  }

  _createProcess() {
    let commandArgs = this._commandArgs;

    const exe = `${GSTREAMER_COMMAND} ${GSTREAMER_OPTIONS}`;
    this._process = child_process.spawn(exe, commandArgs, {
      detached: false,
      shell: true,
    });

    if (this._process.stderr) {
      this._process.stderr.setEncoding("utf-8");
    }

    if (this._process.stdout) {
      this._process.stdout.setEncoding("utf-8");
    }

    this._process.on("error", (error) =>
      console.error(
        "-----error-----gstreamer::process::error [pid:%d, error:%o]",
        this._process.pid,
        error
      )
    );

    this._process.once("spawn", (data) => {
      console.log("gstreamer::process::spawn [data:%o]", data);
    });

    this._process.once("close", () => {
      console.log("gstreamer::process::close [pid:%d]", this._process.pid);
      this._observer.emit("process-close");
    });

    this._process.once("exit", (code, signal) => {
      console.log(
        "gstreamer::process::exit [pid:%d, code:%d, signal:%s]",
        this._process.pid,
        code,
        signal
      );
    });

    this._process.stderr.on("data", (data) =>
      console.log(
        "----error----gstreamer::process::stderr::data [data:%o]",
        data
      )
    );
  }

  kill() {
    console.log("kill() [pid:%d]", this._process.pid);
    if (this._process) {
      this._process.kill("SIGTERM");
      this._process.removeAllListeners();
      this._process = null;
    }
  }

  get _commandArgs() {
    let commandArgs = [
      `rtpbin name=rtpbin latency=500 buffer-mode=1 ntp-sync=true do-retransmission=true sdes="application/x-rtp-source-sdes, cname=(string)${
        this._rtpParameters[this.kind].rtpParameters.rtcp.cname
      }"`,
      "!",
    ];

    if (this.kind === "video") {
      commandArgs = commandArgs.concat(this._videoArgs);
    }
    if (this.kind === "audio") {
      commandArgs = commandArgs.concat(this._audioArgs);
    }

    commandArgs = commandArgs.concat(this._sinkArgs);
    commandArgs = commandArgs.concat(this._rtcpArgs);

    console.log("kind: ", this.kind);
    console.log("commandArgs: ", commandArgs);

    return commandArgs;
  }

  get _videoArgs() {
    const { video } = this._rtpParameters;
    // Get video codec info
    const videoCodecInfo = getCodecInfoFromRtpParameters(
      "video",
      video.rtpParameters
    );

    const VIDEO_CAPS = `application/x-rtp,media=(string)video,clock-rate=(int)${
      videoCodecInfo.clockRate
    },payload=(int)${
      videoCodecInfo.payloadType
    },encoding-name=(string)${videoCodecInfo.codecName.toUpperCase()},ssrc=(uint)${
      video.rtpParameters.encodings[0].ssrc
    }`;

    return [
      `udpsrc port=${video.remoteRtpPort} caps="${VIDEO_CAPS}"`,
      "!",
      "rtpbin.recv_rtp_sink_0 rtpbin.",
      "!",
      "queue",
      "!",
      `rtp${videoCodecInfo.codecName.toLowerCase()}depay`,
      "!",
      "decodebin",
      "!",
      "videoconvert",
      "!",
      "videorate",
      "!",
      "video/x-raw,framerate=30/1", // Ensure correct frame rate
      "!",
      "x264enc",
      "!",
      "h264parse",
      "!",
      "mux.",
    ];
  }

  get _audioArgs() {
    const { audio } = this._rtpParameters;
    // Get audio codec info
    const audioCodecInfo = getCodecInfoFromRtpParameters(
      "audio",
      audio.rtpParameters
    );
    const AUDIO_CAPS = `application/x-rtp,media=(string)audio,clock-rate=(int)${
      audioCodecInfo.clockRate
    },payload=(int)${
      audioCodecInfo.payloadType
    },encoding-name=(string)${audioCodecInfo.codecName.toUpperCase()},ssrc=(uint)${
      audio.rtpParameters.encodings[0].ssrc
    }`;

    return [
      `udpsrc port=${audio.remoteRtpPort} caps="${AUDIO_CAPS}"`,
      "!",
      "rtpbin.recv_rtp_sink_1 rtpbin.",
      "!",
      "queue",
      "!",
      `rtp${audioCodecInfo.codecName.toLowerCase()}depay`,
      "!",
      "decodebin",
      "!",
      "audioconvert",
      "!",
      "audioresample",
      "!",
      "audio/x-raw,rate=48000", // Ensure correct sample rate
      "!",
      "faac",
      "!",
      "aacparse",
      "!",
      "mux.",
    ];
  }

  get _rtcpArgs() {
    if (this.kind === "video")
      return [
        `udpsrc address=127.0.0.1 port=${this._rtpParameters.video.remoteRtcpPort}`,
        "!",
        "rtpbin.recv_rtcp_sink_0 rtpbin.send_rtcp_src_0",
        "!",
        `udpsink host=127.0.0.1 port=${this._rtpParameters.video.localRtcpPort} bind-address=127.0.0.1 bind-port=${this._rtpParameters.video.remoteRtcpPort} sync=false async=false`,
      ];

    if (this.kind === "audio")
      return [
        `udpsrc address=127.0.0.1 port=${this._rtpParameters.audio.remoteRtcpPort}`,
        "!",
        `rtpbin.recv_rtcp_sink_1 rtpbin.send_rtcp_src_1`,
        "!",
        `udpsink host=127.0.0.1 port=${this._rtpParameters.audio.localRtcpPort} bind-address=127.0.0.1 bind-port=${this._rtpParameters.audio.remoteRtcpPort} sync=false async=false`,
      ];
  }

  get _sinkArgs() {
    const timestamp = new Date();
    const time = timestamp.getTime();
    const filePath = process.env.RECORD_FILE_LOCATION_PATH
      ? path.join(
          process.env.RECORD_FILE_LOCATION_PATH,
          `${this._rtpParameters.folder}/${this._rtpParameters.peerId}_${this.kind}_${time}.mp4`
        )
      : `./files/${this._rtpParameters.folder}/${this._rtpParameters.peerId}_${this.kind}_${time}.mp4`;

    if (!fs.existsSync(filePath)) {
      fs.mkdirSync(path.dirname(filePath), { recursive: true });
      fs.writeFileSync(filePath, "");
    }

    // In the _sinkArgs getter
    return [
      "queue",
      "!",
      "mp4mux name=mux",
      "!",
      `filesink location=${filePath}`,
    ];
  }
};
