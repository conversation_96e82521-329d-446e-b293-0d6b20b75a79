const ffmpeg = require("fluent-ffmpeg");
const fs = require("fs").promises;

async function processAudioFiles(folder) {
  try {
    // await fs.mkdir(`${folder}/output`, { recursive: true });
    // console.log("Folder created successfully!");
    const files = await fs.readdir(folder);
    console.log("Reading all files :", files);
    await processWebmFiles(files, folder);
  } catch (err) {
    //! Ignore
  } finally {
    console.log("Process Audio Done !!!");
    return;
  }
}

async function processWebmFiles(files, folder) {
  for (const file of files) {
    const filePath = `${folder}/${file}`;
    if (file.endsWith(".webm")) {
      await repairWebmFile(filePath, folder);
    }
  }
}

async function repairWebmFile(filePath, folder) {
  const filename = filePath.split("/").pop().split(".")[0];
  const fileType = filename.split("_")[1];
  console.log("fileType:", fileType);
  const repairedFilePath = `${folder}/${filename}_repaired.mp4`;

  const outputOptions = fileType === "audio" ? "-c:a libopus" : "-c:v libvpx";
  // const outputOptions = fileType === "audio" ? "-c:a aac" : "-c:v libx264";
  return new Promise((resolve, reject) => {
    ffmpeg(filePath)
      .outputOptions(outputOptions)
      .output(repairedFilePath)
      .on("end", async () => {
        try {
          await fs.rename(repairedFilePath, filePath);
          console.log(`Repaired and replaced ${filename}`);
          resolve(filePath);
        } catch (error) {
          console.error(
            `Error replacing the old file with the repaired file:`,
            error
          );
          reject(error);
        }
      })
      .on("error", (err) => {
        console.error(`Error repairing ${filename}:`, err);
        reject(err);
      })
      .run();
  });
}

module.exports = processAudioFiles;
