const Ffmpeg = require("fluent-ffmpeg");
const fs = require("fs");
const path = require("path");

async function concatInterviewees(recordingFolderPath, intervieweeId) {
	const File_Path = "scripts";
	try {
		console.log("concatInterviewees()");
		const date = new Date();
		const time = date.getTime();
		const outputFilename = `${intervieweeId}_${time}.mp3`;

		const intervieweeFiles = fs.readdirSync(recordingFolderPath).filter((file) => file.startsWith(intervieweeId));

		if (intervieweeFiles.length === 0) {
			console.error("No interviewee audio files found.");
			return;
		}

		const audioFiles = intervieweeFiles.map((file) => ({
			filename: path.join(recordingFolderPath, file),
			timestamp: parseInt(file.split(".")[0].split("_")[1], 10),
		}));

		console.log(recordingFolderPath, audioFiles);

		if (audioFiles.length === 1) {
			return new Promise((resolve, reject) => {
				fs.copyFile(audioFiles[0].filename, path.join(File_Path, outputFilename), (err) => {
					if (err) reject("");
					resolve(path.join(File_Path, outputFilename));
					console.log(`Copied single interviewee audio file to ${File_Path}/${outputFilename}`);
				});
			});
		} else {
			audioFiles.sort((file1, file2) => file1.timestamp - file2.timestamp);
			return new Promise((resolve, reject) => {
				let command = Ffmpeg();

				audioFiles.forEach((file) => {
					command = command.input(file.filename);
				});

				const filterString = audioFiles.map((file, index) => `[${index}:a]`).join("");
				const complexFilter = `concat=n=${audioFiles.length}:v=0:a=1`;

				command
					.complexFilter(`${filterString}${complexFilter}`)
					.output(path.join(File_Path, outputFilename))
					.on("end", () => {
						console.log(`Merged interviewee audio files to ${File_Path}/${outputFilename}`);
						resolve(path.join(File_Path, outputFilename));
					})
					.on("error", (err) => {
						console.error("Error concatenate files:", err);
						reject("");
					})
					.run();
			});
		}
	} catch (err) {
		console.log(`Error in concatenate ${err}`);
	}
}

module.exports = concatInterviewees;
