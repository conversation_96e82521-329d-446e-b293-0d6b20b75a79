const { rooms } = require("../../cache");
const { audioReceiverId } = require("../../constants/roomConstants");
const { getPort } = require("./port");

const publishProducerRtpStream = async (
  roomId,
  peerId,
  producer,
  mode = ""
) => {
  const room = rooms[roomId];
  console.log("publishProducerRtpStream()");
  const isProduction = process.env.PORT == 443;

  // Create the mediasoup RTP Transport used to send media to the GStreamer process
  const rtpTransportConfig = {
    listenIp: {
      ip: "0.0.0.0",
      announcedIp: isProduction
        ? process.env.MEDIASOUP_ANNOUNCED_IP || null
        : null,
    },
    rtcpMux: false,
    comedia: false,
  };

  const { router } = room;
  const rtpTransport = await router.createPlainTransport(rtpTransportConfig);
  console.log("rtpTransport", rtpTransport && "yes");

  const [remoteRtpPort, remoteRtcpPort] = allocatePorts(
    room,
    mode === "extractAudio" ? audioReceiverId : peerId
  );

  // Connect the mediasoup RTP transport to the ports used by GStreamer
  await rtpTransport.connect({
    ip: "127.0.0.1",
    port: remoteRtpPort,
    rtcpPort: remoteRtcpPort,
  });

  if (mode === "extractAudio") {
    if (!room.peers[peerId].rawAudioTransport)
      room.peers[peerId].rawAudioTransport = {};
    room.peers[peerId].rawAudioTransport = rtpTransport;
  } else {
    if (!room.peers[peerId].transportRtp) room.peers[peerId].transportRtp = {};
    room.peers[peerId].transportRtp = rtpTransport;
  }

  const codecs = [];
  // Codec passed to the RTP Consumer must match the codec in the Mediasoup router rtpCapabilities
  const routerCodec = router.rtpCapabilities.codecs.find(
    (codec) => codec.kind === producer.kind
  );
  codecs.push(routerCodec);

  const rtpCapabilities = {
    codecs,
    rtcpFeedback: [],
  };

  const rtpConsumer = await rtpTransport.consume({
    producerId: producer.id,
    rtpCapabilities,
    paused: false,
  });

  if (mode === "extractAudio") {
    if (!room.peers[peerId].rawAudioConsumer)
      room.peers[peerId].rawAudioConsumer = {};
    room.peers[peerId].rawAudioConsumer = rtpConsumer;
  } else {
    if (!room.peers[peerId].rtpConsumer) room.peers[peerId].rtpConsumer = {};
    room.peers[peerId].rtpConsumer = rtpConsumer;
  }

  return {
    remoteRtpPort,
    remoteRtcpPort,
    localRtcpPort: rtpTransport.rtcpTuple
      ? rtpTransport.rtcpTuple.localPort
      : undefined,
    rtpCapabilities,
    rtpParameters: rtpConsumer.rtpParameters,
  };
};

const allocatePorts = (room, peerId) => {
  // Set the receiver RTP ports & RTCP ports
  const [remoteRtpPort, remoteRtcpPort] = getPort();

  room.remotePorts = room.remotePorts || {};
  room.remotePorts[peerId] = room.remotePorts[peerId] || [];

  room.remotePorts[peerId] = [remoteRtpPort, remoteRtcpPort];
  console.log(`ports of:${peerId}`, JSON.stringify(room.remotePorts[peerId]));

  return [remoteRtpPort, remoteRtcpPort];
};

module.exports = publishProducerRtpStream;
