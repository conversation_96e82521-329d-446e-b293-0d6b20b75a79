const { rooms } = require("../../cache");
const GStreamer = require("../gstreamer");
const FFmpeg = require("../ffmpeg");
const publishProducerRtpStream = require("./publishProducerRtpStream");
const stopRecord = require("./stopRecord");

const startRecord = async (roomId, peerId, producer) => {
  const room = rooms[roomId];
  let recordInfo = {};

  if (room.peers[peerId].process) {
    if (room.peers[peerId].process.kind === producer.kind) {
      console.log(`Stopping old recording for`, room, peerId, producer.kind);
      await stopRecord(room, peerId, roomId);
    }
  }

  console.log(`Starting new recording for`, producer.kind);
  recordInfo[producer.kind] = await publishProducerRtpStream(
    roomId,
    peerId,
    producer
  );
  recordInfo.folder = room.isTutorial ? `${roomId}` : `${roomId}_${room.createTime}`;
  recordInfo.peerId = peerId;
  recordInfo.roomId = roomId;
  recordInfo.kind = producer.kind;
  recordInfo.source = producer.appData.source;
  room.peers[peerId].process = new FFmpeg(recordInfo);
  console.log(
    `Recording started for room ${roomId} with peer ${peerId} and process ${room.peers[peerId].process.pid}`
  );
};

module.exports = startRecord;
