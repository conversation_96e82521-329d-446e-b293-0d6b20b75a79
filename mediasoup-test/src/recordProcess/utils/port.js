const MIN_PORT = 20000;
const MAX_PORT = 30000;

const availablePorts = new Set([...Array(MAX_PORT - MIN_PORT + 1).keys()].map((i) => i + MIN_PORT));

function getRandomPort() {
	const availableArray = Array.from(availablePorts);
	if (availableArray.length === 0) {
		return null;
	}
	const randomIndex = Math.floor(Math.random() * availableArray.length);
	return availableArray[randomIndex];
}

module.exports.getPort = () => {
	const Port = getRandomPort();
	if (Port === null) {
		return null;
	}

	rtpPort = Port % 2 === 0 ? Port : Port - 1;
	rtcpPort = Port % 2 === 0 ? Port + 1 : Port;

	console.log("rtpPort:", rtpPort, "rtcpPort:", rtcpPort);

	availablePorts.delete(rtpPort);
	availablePorts.delete(rtcpPort);

	return [rtpPort, rtcpPort];
};

module.exports.releasePort = (port) => {
	availablePorts.add(port);
};
