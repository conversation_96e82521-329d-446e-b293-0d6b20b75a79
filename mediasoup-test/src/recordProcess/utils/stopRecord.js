const { audioReceiverId } = require("../../constants/roomConstants");
const { releasePort } = require("./port");

const stopRecord = async (room, peerId, roomId) => {
  const remotePorts = [...(room.remotePorts[peerId] || [])];
  const rtpConsumer = room.peers[peerId]?.rtpConsumer;
  const transportRtp = room.peers[peerId]?.transportRtp;
  const process = room.peers[peerId]?.process;
  const rawAudioConsumer = room.peers[peerId]?.rawAudioConsumer;
  const rawAudioTransport = room.peers[peerId]?.rawAudioTransport;
  const rawAudioProcess = room.peers[peerId]?.rawAudioProcess;

  try {
    for (const remotePort of remotePorts) {
      console.log(`release port of ${peerId}:`, remotePort);
      releasePort(remotePort);
    }

    await rtpConsumer?.close();
    await transportRtp?.close();
    await rawAudioConsumer?.close();
    await rawAudioTransport?.close();

    if (process) {
      await process.kill();
    }

    if (room.remotePorts[audioReceiverId]) {
      for (const rawAudioProcessPort of room.remotePorts[audioReceiverId]) {
        console.log(`release port of ${audioReceiverId}:`, rawAudioProcessPort);
        releasePort(rawAudioProcessPort);
      }
      room.remotePorts[audioReceiverId] = [];
    }

    if (rawAudioProcess) {
      await rawAudioProcess.kill();
    }

    if (room.remotePorts[peerId]) {
      room.remotePorts[peerId] = [];
    }

    if (room.peers[peerId]) {
      delete room.peers[peerId].process;
      delete room.peers[peerId].rawAudioProcess;
    }

    console.log(`Recording stopped for room ${roomId} - ${peerId}`);
  } catch (error) {
    console.error(
      "Error stopping recording for peer",
      peerId,
      "in room",
      roomId,
      error
    );
  }
};

module.exports = stopRecord;
