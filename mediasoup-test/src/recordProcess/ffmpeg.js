// Class to handle child process used for running FFmpeg

const child_process = require("child_process");
const path = require("path");
const fs = require("fs");
const { createSdpText } = require("./utils/sdp");
const { convertStringToStream } = require("./utils/convertStringToStream");

module.exports = class FFmpeg {
  constructor(rtpParameters, mode = "", callback = () => { }) {
    this._rtpParameters = rtpParameters;
    this._mode = mode;
    this._callback = callback;
    this._kind = rtpParameters.kind;
    this._source = rtpParameters.source;
    this._process = undefined;
    this._sdpStream = undefined;
    this._filePath = "";
    this._startTime = new Date().getTime();

    if (this._mode === "extractAudio") {
      this._createExtractAudioProcess();
    } else {
      this._createProcess();
    }
  }

  _setupCommonEvents() {
    if (this._process.stderr) {
      this._process.stderr.setEncoding("utf-8");
      this._process.stderr.on("data", (data) => {
        console.error(`[FFmpeg stderr data] Room: ${this._rtpParameters.roomId} | Peer: ${this._rtpParameters.peerId} | Data:`, data);
      });
    }

    this._process.on("message", (message) => {
      console.log(`[FFmpeg Message] Room: ${this._rtpParameters.roomId} | Peer: ${this._rtpParameters.peerId} | Message:`, message);
    });

    this._process.on("error", (error) => {
      console.error(`[FFmpeg Error] Room: ${this._rtpParameters.roomId} | Peer: ${this._rtpParameters.peerId} | Error:`, error);
    });

    this._process.once("close", () => {
      console.log(`[FFmpeg Process Close] Room: ${this._rtpParameters.roomId} | Peer: ${this._rtpParameters.peerId}`);
    });

    this._sdpStream.on("error", (error) => {
      console.error(`[FFmpeg sdpStream Error] Room: ${this._rtpParameters.roomId} | Peer: ${this._rtpParameters.peerId} | Error:`, error);
    });

    this._sdpStream.pipe(this._process.stdin);
    this._sdpStream.resume();
  }

  _createProcess() {
    const sdpString = createSdpText(this._kind, this._rtpParameters);
    this._sdpStream = convertStringToStream(sdpString);

    console.log("createProcess()", sdpString);

    this._process = child_process.spawn("ffmpeg", this._commandArgs);

    if (this._process.stdout) {
      this._process.stdout.setEncoding("utf-8");
      this._process.stdout.on("data", (data) => {
        console.log(`[FFmpeg stdout data] Room: ${this._rtpParameters.roomId} | Peer: ${this._rtpParameters.peerId} | Data:`, data);
      });
    }

    this._setupCommonEvents();
  }

  _createExtractAudioProcess() {
    const sdpString = createSdpText(this._kind, this._rtpParameters);
    this._sdpStream = convertStringToStream(sdpString);

    const commandArgs = [
      "-loglevel",
      "panic",
      "-protocol_whitelist",
      "pipe,udp,rtp",
      "-fflags",
      "+genpts+igndts+discardcorrupt",
      "-err_detect",
      "aggressive",
      "-analyzeduration",
      "5M",
      "-probesize",
      "5M",
      "-max_delay",
      "5000000",
      "-buffer_size",
      "16M",
      "-reorder_queue_size",
      "32768",
      "-fec",
      "prompeg=l=10:d=40",
      "-f",
      "sdp",
      "-i",
      "pipe:0",
      "-map",
      "0:a:0",
      "-c:a",
      "pcm_s16le",
      "-ar",
      "16000",  // Sample rate
      "-ac",
      "1",      // Mono channel
      "-f",
      "wav",    // WAV format
      "pipe:1" 
    ];

    this._process = child_process.spawn("ffmpeg", commandArgs);

    if (this._process.stdout) {
      this._process.stdout.on("data", (buffer) => {
        this._callback(buffer);
      });
    }

    this._setupCommonEvents();
  }

  async kill() {
    console.log("kill() [pid:%d]", this._process.pid);

    if (!this._process || this._process.killed) return;

    const timestamp = new Date().getTime();
    const time = timestamp - this._startTime;

    if (this._process) {
      console.log("kill() | forcing kill process");
      this._process.kill("SIGKILL");
    }

    if (time < 5000) {
      // Delete file if recording is less than 5 seconds
      if (fs.existsSync(this._filePath)) {
        fs.unlink(this._filePath, (err) => {
          if (err) console.error("Error deleting file: ", err);
          else console.log("File deleted!");
        });
      }
    }
  }

  get _commandArgs() {
    let commandArgs = [
      "-loglevel",
      "panic",
      "-protocol_whitelist",
      "pipe,udp,rtp",
      "-fflags",
      "+genpts+igndts+discardcorrupt",
      "-err_detect",
      "aggressive",
      "-analyzeduration",
      "5M",
      "-probesize",
      "5M",
      "-max_delay",
      "5000000",
      "-buffer_size",
      "16M",
      "-reorder_queue_size",
      "32768",
      "-fec",
      "prompeg=l=10:d=40",
      "-f",
      "sdp",
      "-i",
      "pipe:0",
      "-movflags",
      "+faststart+frag_keyframe+empty_moov+default_base_moof",
      "-fflags",
      "+flush_packets",
      "-avoid_negative_ts",
      "make_zero",
    ];

    if (this._kind === "video") {
      commandArgs = commandArgs.concat(this._videoArgs);
    }
    if (this._kind === "audio") {
      commandArgs = commandArgs.concat(this._audioArgs);
    }

    const timestamp = new Date();
    const time = timestamp.getTime();
    const extension = this._kind === "audio" ? "mp3" : "mp4";
    this._filePath = process.env.RECORD_FILE_LOCATION_PATH
      ? path.join(
        process.env.RECORD_FILE_LOCATION_PATH,
        `${this._rtpParameters.folder}/${this._rtpParameters.peerId}_${this._source}_${time}.${extension}`
      )
      : `./files/${this._rtpParameters.folder}/${this._rtpParameters.peerId}_${this._source}_${time}.${extension}`;

    commandArgs = commandArgs.concat([`${this._filePath}`]);

    return commandArgs;
  }

  get _videoArgs() {
    // Add scale filter only for higher resolution sources
    const args = ["-map", "0:v:0"];

    if (this._source === "screen") {
      args.push("-vf");
      args.push("scale=1280:-2");
    }

    return args.concat([
      "-c:v",
      "libx264",
      "-preset",
      "ultrafast",
      "-crf",
      "28",
      "-profile:v",
      "main",
      "-pix_fmt",
      "yuv420p",
      "-r",
      "24",
      "-tune",
      "zerolatency",
    ]);
  }

  get _audioArgs() {
    return [
      "-map",
      "0:a:0",
      "-c:a",
      "libmp3lame",
      "-b:a",
      "128k",
      "-ac",
      "2",
    ];
  }
};
