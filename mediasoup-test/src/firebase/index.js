const admin = require("firebase-admin");
const { getAuth } = require("firebase-admin/auth");
const serviceAccount = require("../../certs/hyra-720a2-38c19f644b5e.json");

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://hyra-720a2-default-rtdb.asia-southeast1.firebasedatabase.app",
});

const auth = admin.auth();

function getUserByEmail(email) {
  return admin.auth().getUserByEmail(email);
}

async function getCustomToken(req, res) {
  try {
    const { uid } = req.body;
    const customToken = await getAuth().createCustomToken(uid);
    res.status(200).json({ success: true, token: customToken });
  } catch (error) {
    console.error("Error generating custom token: ", error);
    res.status(500).json({ success: false, error: error });
  }
}

async function storeInRealtimeDatabase(path, data) {
  const db = admin.database();
  const ref = db.ref(path);
  try {
    await ref.set(data);
    console.log("Data stored successfully in Firebase Realtime Database");
  } catch (error) {
    console.error("Failed to store data in Firebase Realtime Database:", error);
  }
}

async function getFromRealtimeDatabase(path) {
  const db = admin.database();
  const ref = db.ref(path);
  try {
    const snapshot = await ref.once("value");
    if (snapshot.exists()) {
      console.log("Data retrieved successfully from Firebase Realtime Database");
      return snapshot.val();
    } else {
      console.log("No data found at the specified path.");
      return null; // Or handle as you see fit for your application
    }
  } catch (error) {
    console.error("Failed to retrieve data from Firebase Realtime Database:", error);
    throw error; // Rethrow or handle error as appropriate for your app
  }
}

module.exports = {
  getUserByEmail,
  getCustomToken,
  storeInRealtimeDatabase,
  getFromRealtimeDatabase,
  auth,
};
