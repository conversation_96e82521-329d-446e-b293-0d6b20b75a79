process.on("uncaughtException", (err) => {
  console.error("Uncaught Exception:", err);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection:", reason);
});

const express = require("express");
const cors = require("cors");
const https = require("https");
const http = require("http");
const os = require("os");
const socketIo = require("socket.io");
const mediasoup = require("mediasoup");
const path = require("path");
const fs = require("fs");
const { workers } = require("./cache");
const { mediasoupConfig } = require("./config");
const socketHandler = require("./socket");
const checkConnection = require("./apis/checkConnection");
const disconnect = require("./apis/disconnect");
const { authMiddleware } = require("./middleware/authMiddleware");
const { consumeQueue, getVMRoutingKey } = require("./utils/rbmq");
const delayEndInterview = require("./utils/delayEndInterview");
const IntervalManager = require("./utils/intervalManager");

const originalLog = console.log;
const orignalError = console.error;

console.log = function (...args) {
  const timestamp = new Date().toISOString();
  originalLog.apply(console, [timestamp, ...args]);
};

console.error = function (...args) {
  const timestamp = new Date().toISOString();
  orignalError.apply(console, [timestamp, ...args]);
};

const options = {
  key: fs.readFileSync(path.join(__dirname, "../certs/server.key")),
  cert: fs.readFileSync(path.join(__dirname, "../certs/__hyrr_app.crt")),
};

const dotenv = require("dotenv");
const { getNodeEnv } = require("./utils/nodeEnvMap");

dotenv.config({
  path: path.resolve(
    process.cwd(),
    getNodeEnv(process.env.NODE_ENV || "development")
  ),
});

const isProduction = process.env.PORT == 443;

const app = express();
const server = isProduction
  ? https.createServer(options, app)
  : http.createServer(app);
const io = socketIo(server);

const startServer = () => {
  const PORT = process.env.PORT;

  server.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
  });

  app.use(cors());
  app.use(express.json());
  app.use(express.static("public"));
  app.use(authMiddleware);

  app.get("/", (req, res) => {
    res.send("MediaSoup Server");
  });
  app.use("/sfu", express.static(path.join(__dirname, "../public")));
  app.use("/sfu/:roomId", express.static(path.join(__dirname, "../public")));
  app.post("/check-connection", checkConnection);
  app.post("/disconnect", disconnect);
};

const createWorkers = async () => {
  const cpus = os.cpus();
  for (let i = 0; i < cpus.length; i++) {
    const newWorker = await mediasoup.createWorker(mediasoupConfig.worker);
    newWorker.on("died", () => {
      console.error(
        "mediasoup worker died, exiting in 2 seconds... [pid:%d]",
        newWorker.pid
      );
    });
    workers.push(newWorker);
  }
};

(async () => {
  await createWorkers();
  startServer();
  const end_interview_delay = process.env.END_INTERVIEW_DELAY_QUEUE;
  const routingKey = getVMRoutingKey(end_interview_delay);
  consumeQueue(end_interview_delay, routingKey, delayEndInterview);
  socketHandler(io);
  IntervalManager.start();
})();
