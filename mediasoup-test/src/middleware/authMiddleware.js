/*
    auth-middleware.js
*/
const { auth } = require("../firebase/index");
const { humanInterviewers, botId } = require("../constants/roomConstants");

function authMiddleware(request, response, next) {
  const headerToken = request.headers.authorization;
  if (!headerToken) {
    return response.status(401).send({ message: "No token provided" });
  }

  if (headerToken && headerToken.split(" ")[0] !== "Bearer") {
    return response.status(401).send({ message: "Invalid token" });
  }

  const token = headerToken.split(" ")[1];

  auth
    .verifyIdToken(token)
    .then((decodedToken) => {
      console.log("Middleware successful");
      const uid = decodedToken.uid;
      response.locals.uid = uid;
      response.locals.email = decodedToken.email;
      response.locals.role =
        decodedToken.interviewer || uid === botId
          ? "interviewer"
          : "interviewee";
      next();
    })
    .catch(() =>
      response
        .send({ success: false, message: "Could not authorize" })
        .status(403)
    );
}

function authMiddlewareSocket(socket, next) {
  const headerToken = socket.handshake.auth.token;

  if (!headerToken) {
    return next(new Error("Authentication error"));
  }

  if (headerToken && headerToken.split(" ")[0] !== "Bearer") {
    next(new Error("Invalid token"));
  }

  const token = headerToken.split(" ")[1];
  if (token === process.env.STATIC_TOKEN) {
    console.log("Socket Middleware successful for GazeAI");
    socket.uid = "gaze-ai-bot";
    socket.role = "interviewer";
    socket.name = "GazeAI";
    next();
    return;
  } else {
    auth
    .verifyIdToken(token)
    .then(async (decodedToken) => {
      socket.uid = decodedToken.uid;
      socket.role =
        decodedToken.interviewer || socket.uid === botId
          ? "interviewer"
          : "interviewee";
      socket.name =
        socket.uid === botId ? "Eval" : decodedToken.name || socket.role;
      next();
    })
    .catch(() => next(new Error("Could not authorize")));
  }
}

module.exports = { authMiddleware, authMiddlewareSocket };
