const redaxios = require("redaxios");
const { rooms } = require("../cache");

const joinGazeAI = async (roomId, companyId, intervieweeId) => {
  if (["a98faaef", "demoairtel1", "demoairtel2", "demoairtel3", "9ez2-lx49-oavz"].includes(roomId)) return;

  const endpoint = roomId.toLowerCase().includes("_tutorial")
    ? "internal/join-tutorial"
    : "internal/join-room/False";

  const config = {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.STATIC_TOKEN}`,
    },
  };

  let attempts = 0;
  const maxAttempts = 50;

  while (attempts < maxAttempts) {
    try {
      if (rooms[roomId]) {
        const data = await redaxios.post(
          `${process.env.PYTHON_SERVER}${endpoint}`,
          { roomId, companyId, intervieweeId },
          config
        );
        console.log("GazeAI bot joined to room", roomId, data.data);
        return;
      } else {
        console.log(`Room ${roomId} not found in cache`);
        return;
      }
    } catch (error) {
      attempts++;
      console.log(
        `Failed x${attempts} times to join GazeAI bot to room ${roomId}:`,
        error.message
      );

      await new Promise((resolve) => setTimeout(resolve, 10000));
      if (attempts === maxAttempts) {
        console.log(
          `Max retry attempts (${maxAttempts}) reached for room ${roomId}`
        );
        break;
      }
    }
  }
};

module.exports = joinGazeAI;
