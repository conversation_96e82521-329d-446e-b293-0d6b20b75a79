const { rooms } = require("../cache");
const { gazeAiBotId } = require("../constants/roomConstants");
const IntervalManager = require("./intervalManager");

const monitorProducerStats = (producer, roomId, peerId) => {
  const statWindow = [];
  const MAX_WINDOW_SIZE = 5;
  const EVALUATION_INTERVAL = 5;
  const RAW_EMIT_INTERVAL = 5 * 1000;

  let lastRawEmitTime = Date.now();
  let isCurrentlyBad = false;
  let badNetworkStart = null;

  const fetchStatsTaskId = IntervalManager.addTask(
    async () => {
      try {
        if (!producer || producer.closed) {
          console.log(
            `Producer ${producer?.id} is closed, stopping monitoring`
          );
          IntervalManager.stopTask(fetchStatsTaskId);
          IntervalManager.stopTask(evaluateStatsTaskId);
          return;
        }

        const stats = await producer.getStats();

        stats.forEach((stat) => {
          const { fractionLost = 0 } = stat;

          statWindow.push({ fractionLost });
          if (statWindow.length > MAX_WINDOW_SIZE) {
            statWindow.shift();
          }

          const now = Date.now();
          if (now - lastRawEmitTime >= RAW_EMIT_INTERVAL) {
            const room = rooms[roomId];
            const peer = room?.peers[peerId];
            if (peer?.role === "interviewee") {
              room.peers[gazeAiBotId]?.socket?.emit("producerStats", {
                producerIndex: stat.producerId || stat.ssrc,
                stats: stat,
              });
            }
            lastRawEmitTime = now;
          }
        });
      } catch (err) {
        console.error("Error fetching producer stats:", err);
      }
    },
    1,
    "producerStats"
  );

  const evaluateStatsTaskId = IntervalManager.addTask(
    () => {
      if (statWindow.length < MAX_WINDOW_SIZE) return;

      const avg = (arr) => arr.reduce((acc, val) => acc + val, 0) / arr.length;
      const avgFractionLost = avg(statWindow.map((s) => s.fractionLost));

      const isBadNetwork = avgFractionLost >= 150;

      const room = rooms[roomId];
      const peer = room?.peers[peerId];

      if (peer?.role === "interviewee") {
        const networkStatusPayload = {
          avgFractionLost: avgFractionLost.toFixed(2),
          status: isBadNetwork ? "Bad" : "Good",
        };

        room.peers[peerId]?.socket?.emit("networkStatus", networkStatusPayload);

        const botSocket = room.peers[gazeAiBotId]?.socket;

        if (isBadNetwork && !isCurrentlyBad) {
          badNetworkStart = Date.now();
          isCurrentlyBad = true;

          botSocket?.emit("badNetworkInterval", {
            type: "networkIssueStart",
            timestamp: badNetworkStart,
          });
        } else if (!isBadNetwork && isCurrentlyBad) {
          const badNetworkEnd = Date.now();
          isCurrentlyBad = false;

          botSocket?.emit("badNetworkInterval", {
            type: "networkIssueEnd",
            timestamp: badNetworkEnd,
          });

          badNetworkStart = null;
        }
      }
    },
    EVALUATION_INTERVAL,
    "evaluateProducerStats"
  );

  IntervalManager.startTask(fetchStatsTaskId);
  IntervalManager.startTask(evaluateStatsTaskId);

  if (!rooms[roomId].producers) {
    rooms[roomId].producers = {};
  }

  rooms[roomId].producers[producer.id] = {
    fetchTaskId: fetchStatsTaskId,
    evaluateTaskId: evaluateStatsTaskId,
  };
};

module.exports = monitorProducerStats;
