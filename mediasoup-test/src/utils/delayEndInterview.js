const axios = require("redaxios");
const endInterviewHandler = require("../socket/eventHandlers/endInterviewHandler");
const { sendDelayedMessage } = require("./rbmq");

const delayEndInterview = async (data) => {
  try {
    const { roomId, companyId, recordingDir, intervieweeId } = data;

    const baseUrl = process.env.BASE_URL;
    try {
      console.log("Delay end interview:", roomId);
      const response = await axios.post(
        `${baseUrl}internal/check-merging-status`,
        {
          roomId,
          companyId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.STATIC_TOKEN}`,
          },
        }
      );
      console.log(
        "Check merging status response:",
        roomId,
        companyId,
        response.data
      );
      if (response.data.success) {
        if (response.data.file_create_time) {
          console.log("Merging already done!!!", roomId, companyId);
        } else {
          console.log("Merging by DELAYED QUEUE!!!", roomId, companyId);
          await sendDelayedMessage(
            process.env.PYTHON_SERVER_PROCESS_QUEUE,
            {
              recordingDir: recordingDir,
              intervieweeId: intervieweeId,
              companyId: companyId || "",
            },
            5 * 60 * 1000 // 5 minute
          );
          await endInterviewHandler(
            { roomId, peerId: "", isDelayed: true },
            () => {}
          );
        }
      } else {
        console.error("Error checking merging status:", response || "");
      }
    } catch (error) {
      console.error("Error checking merging status:", error);
    }

    try {
      const response = await axios.post(
        `${baseUrl}internal/end-interview`,
        {
          roomId,
          companyId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.STATIC_TOKEN}`,
          },
        }
      );
      console.log(
        "delayed end interview evalai response:",
        roomId,
        companyId,
        response.data
      );
    } catch (e) {
      console.error("Error in delayed end interview evalai:", e);
    }
  } catch (error) {
    console.error("Error delay end interview:", error);
  }
};

module.exports = delayEndInterview;
