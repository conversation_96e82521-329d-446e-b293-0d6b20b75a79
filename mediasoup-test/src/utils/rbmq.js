const amqp = require("amqp-connection-manager");

let connection = null;

function connect(url) {
  return new Promise((resolve, reject) => {
    try {
      connection = amqp.connect(url || process.env.AMQP_URL);
      connection.on("connect", () => {
        console.log("Connected to RabbitMQ");
        resolve(connection);
      });
      connection.on("disconnect", (err) => {
        console.log("Disconnected from RabbitMQ", err);
        reject(err);
      });
    } catch (error) {
      console.error("Error connecting to RabbitMQ:", error);
      reject(error);
    }
  });
}

async function pushToQueue(queueName, message) {
  if (!connection) {
    try {
      await connect();
    } catch (error) {
      throw new Error(
        "Failed to establish connection to RabbitMQ: " + error.message
      );
    }
  }

  const channelWrapper = connection.createChannel({
    json: true,
    setup: function (channel) {
      return channel.assertQueue(queueName, { durable: true });
    },
  });

  try {
    await channelWrapper.sendToQueue(queueName, message, { persistent: true });
    console.log(`Message sent to queue: ${queueName}`, message);
  } catch (error) {
    console.error(
      `Error sending message to queue ${queueName}:`,
      message,
      error
    );
    throw error;
  } finally {
    await channelWrapper.close();
  }
}

async function sendDelayedMessage(
  queueName,
  message,
  delay = 3 * 60 * 60 * 1000,
  routingKey = null
) {
  if (!connection) {
    try {
      await connect();
    } catch (error) {
      throw new Error(
        "Failed to establish connection to RabbitMQ: " + error.message
      );
    }
  }

  if (!routingKey) {
    routingKey = queueName;
  }

  const channelWrapper = connection.createChannel({
    json: true,
    setup: function (channel) {
      channel.assertExchange(
        process.env.DELAYED_EXCHANGE,
        "x-delayed-message",
        {
          durable: true,
          arguments: {
            "x-delayed-type": "direct",
          },
        }
      );
      channel.assertQueue(queueName, { durable: true });
      channel.bindQueue(queueName, process.env.DELAYED_EXCHANGE, routingKey);
    },
  });

  await channelWrapper.publish(
    process.env.DELAYED_EXCHANGE,
    routingKey,
    message,
    {
      headers: {
        "x-delay": delay,
      },
      persistent: true,
    }
  );

  console.log(
    `Sent delayed message to ${queueName} with delay of ${
      delay / (1000 * 60)
    } minutes: ${JSON.stringify(message)}`
  );
}

async function consumeQueue(queueName, routingKey = null, processMessage) {
  try {
    if (!connection) {
      try {
        await connect();
      } catch (error) {
        throw new Error(
          "Failed to establish connection to RabbitMQ: " + error.message
        );
      }
    }

    if (!routingKey) {
      routingKey = queueName;
    }

    connection.createChannel({
      json: true,
      heartbeatIntervalInSeconds: process.env.HEARTBEAT_INTERVAL_IN_SECONDS,
      reconnectTimeInSeconds: process.env.RECONNECT_TIME_IN_SECONDS,
      setup: async (channel) => {
        await channel.assertQueue(queueName, { durable: true });
        await channel.bindQueue(
          queueName,
          process.env.DELAYED_EXCHANGE,
          routingKey
        );
        channel.consume(queueName, async (msg) => {
          const message = JSON.parse(msg.content);
          try {
            await processMessage(message);
            channel.ack(msg);
          } catch (error) {
            console.error("Error processing request:", error);
            channel.ack(msg);
          }
        });

        console.log(`Started consuming messages from queue: ${queueName}`);
      },
    });

    process.on("SIGINT", () => {
      console.log("Closing connection to RabbitMQ");
      RabbitMQConsumer.connection.close();
    });
  } catch (error) {
    console.error("Failed to set up RabbitMQ consumer:", error);
  }
}

function getVMRoutingKey(queueName) {
  return `${queueName}.${process.env.MEDIASOUP_ANNOUNCED_IP}`;
}

module.exports = {
  connect,
  pushToQueue,
  sendDelayedMessage,
  consumeQueue,
  getVMRoutingKey,
};
