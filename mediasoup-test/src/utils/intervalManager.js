const EventEmitter = require("events");

class IntervalManager {
  static tasks = [];
  static globalCounter = 0;
  static timer = null;
  static emitter = new EventEmitter();
  static taskIdCounter = 0;

  /**
   * Register a new task. By default, the task is disabled.
   *
   * @param {Function} fn - The function to execute (can be async).
   * @param {number} intervalInSec - Interval in seconds to run the function.
   * @param {string} [eventName='taskReturn'] - Custom event name to emit when the function returns.
   * @param {string} [id] - Optional task identifier. If omitted, a name will be derived.
   * @returns {string} The task identifier.
   */
  static addTask(fn, intervalInSec, eventName, id) {
    if (typeof fn !== "function") {
      throw new Error("The first parameter must be a function");
    }

    if (typeof intervalInSec !== "number" || intervalInSec <= 0) {
      throw new Error("The second parameter must be a positive number");
    }

    const taskId = id || (fn.name ? fn.name : `task_${++this.taskIdCounter}`);
    eventName = eventName || `taskReturn_${taskId}`;
    this.tasks.push({
      id: taskId,
      fn,
      interval: intervalInSec,
      counter: 0,
      enabled: false, // Tasks are disabled by default.
      eventName,
    });
    return taskId;
  }

  /**
   * Remove a task by its identifier.
   * @param {string} taskId - The task identifier.
   * @returns {boolean} True if the task was removed, false if not found.
   */
  static removeTask(taskId) {
    const index = this.tasks.findIndex((task) => task.id === taskId);
    if (index === -1) return false;
    this.tasks.splice(index, 1);
    return true;
  }

  /**
   * Attach an event listener.
   * @param {string} event - The event name.
   * @param {Function} listener - The callback function.
   */
  static on(event, listener) {
    this.emitter.on(event, listener);
  }

  /**
   * Remove an event listener.
   * @param {string} event - The event name.
   * @param {Function} listener - The callback to remove.
   */
  static off(event, listener) {
    this.emitter.off(event, listener);
  }

  /**
   * Enable (start) a specific task by its id.
   * @param {string} taskId - The task identifier.
   */
  static startTask(taskId) {
    const task = this.tasks.find((t) => t.id === taskId);
    if (task) {
      task.enabled = true;
      task.counter = 0; // Optionally reset the counter.
    }
  }

  /**
   * Disable (stop) a specific task by its id.
   * @param {string} taskId - The task identifier.
   */
  static stopTask(taskId) {
    const task = this.tasks.find((t) => t.id === taskId);
    if (task) {
      task.enabled = false;
    }
  }

  /**
   * Enable (start) all tasks.
   */
  static startAllTasks() {
    this.tasks.forEach((task) => {
      task.enabled = true;
      task.counter = 0;
    });
  }

  /**
   * Disable (stop) all tasks.
   */
  static stopAllTasks() {
    this.tasks.forEach((task) => {
      task.enabled = false;
    });
  }

  /**
   * Start the global timer that ticks every second.
   */
  static start() {
    if (this.timer) return; // Prevent multiple timers.
    this.timer = setInterval(() => {
      this.globalCounter++;

      this.tasks.forEach((task) => {
        if (!task.enabled) return;
        task.counter++;
        if (task.counter >= task.interval) {
          task.counter = 0;
          // Execute the task function (handles both sync and async).
          Promise.resolve(task.fn())
            .then((result) => {
              this.emitter.emit(task.eventName, {
                taskId: task.id,
                result,
                tick: this.globalCounter,
              });
            })
            .catch((error) => {
              this.emitter.emit(task.eventName, {
                taskId: task.id,
                error,
                tick: this.globalCounter,
              });
            });
        }
      });
    }, 1000);
  }

  /**
   * Stop the global timer.
   */
  static stop() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }
}

module.exports = IntervalManager;
