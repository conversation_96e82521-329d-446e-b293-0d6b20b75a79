const { rooms } = require("../cache");
const { SpeechToTextFactory } = require("../model/speechToText");

const setupSpeechToText = async ({ service, peerId, roomId }) => {
  console.log("Setting up speech to text", service, peerId, roomId);
  const speechToText = SpeechToTextFactory.createSpeechToTextService(service, peerId, roomId);

  await speechToText.initializeRecognizer();

  rooms[roomId].speechToText = speechToText;
}

module.exports = setupSpeechToText;