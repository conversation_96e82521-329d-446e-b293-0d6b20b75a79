GENAI_TO_USE="AzureOpenAI"
MAX_ALLOWED_TOKENS=1000000
VERTEXAI_PROJECT_ID="hyra-720a2"
VERTEXAI_LOCATION="us-central1"
VERTEXAI_MODEL="gemini-1.5-pro-001"
OPENAI_API_KEY="***************************************************"
OPENAI_MODEL="gpt-3.5-turbo"
AZURE_OPENAI_API_KEY="********************************"
AZURE_OPENAI_ENDPOINT="https://openaiinstance1508.openai.azure.com/"
AZURE_OPENAI_MODEL="2024-12-01-preview"
AZURE_OPENAI_DEPLOYMENT="gpt-4o"
AZURE_OPENAI_REASONING_EFFORT="high"
AZURE_DEVOPS_ORG="neusort"
AZURE_DEVOPS_PROJECT_ID="344153d0-8162-4039-89f8-64e6a4e5adaf"
AZURE_DEVOPS_TOKEN="****************************************************"
SPEECH_TO_TEXT="azure"
DEEPGRAM_API_KEY="****************************************"
AZURE_SPEECH_KEY="e2e78f3ef30b4016afe2add7671e5d8d"
AZURE_SPEECH_REGION="centralindia"
MYSQL_SERVER="hyrr.mysql.database.azure.com"
MYSQL_DB="interviews"
MYSQL_USER="hyrr_admin"
MYSQL_PASSWORD="Limitless_15"
MYSQL_CA="./certs/DigiCertGlobalRootCA.crt.pem"
AMQP_URL="amqp://Neusort_Test:Eval@9900@20.40.47.123:5672/?heartbeat=60&frameMax=8192"
INTERVIEW_INVITE_MAIL_QUEUE="interview-invite-mail-queue-staging"
QUEUE_NAME_PARSER= "roomsQueueParser-staging"
MEDIASOUP_SERVER_URL="https://streamingserver.hyrr.app/"
FIREBASE_API_KEY="AIzaSyA1bSN3fFdwtgpLtG_W_NcAd2WjUM2px-E"
QUEUE_NAME_ADMIN_INVITE="adminInvite-staging"
QUEUE_NAME_ADMIN_INVITATION="adminInvitationWithRole-staging"
QUEUE_NAME_ASSIGNMENT_GENERATOR="assignmentGeneratorQueue-staging"
QUEUE_NAME_CUSTOM_SKILLS_APPROVAL="custom-skills-mail-queue-staging"
QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA="download-candidate-data-queue-staging"
QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA_FOR_EMAIL="download-candidate-data-for-email-queue-staging"
HIRING_MANAGER_REVIEW_QUEUE="hiring-manager-review-queue-staging"
HIRING_EMAIL_QUEUE="hiring-email-queue-staging"
ASSIGNMENT_PACKAGE_QUEUE="assignment-package-queue-staging"
INTERVIEW_ASSIGNMENT_PARSE_QUEUE="interview-assignment-parse-queue-staging"
DELAYED_EXCHANGE="delayed_exchange"
DELETE_REPO_QUEUE="delete-cloned-repo-staging"
CANDIDATE_ADDITION_V2_QUEUE="candidate-addition-v2-queue-staging"
QUESTION_APPROVAL_V2_QUEUE="question-approval-v2-queue-staging"
CANDIDATE_ADDITION_V3_QUEUE="candidate-addition-v3-queue-staging"
QUESTION_APPROVAL_V3_QUEUE="question-approval-v3-queue-staging"
ASSIGNMENT_GENERATOR_V3_QUEUE="assignment-generator-v3-queue-staging"
CANDIDATE_ADDITION_V4_QUEUE="candidate-addition-v4-queue-staging"
QUESTION_APPROVAL_V4_QUEUE="question-approval-v4-queue-staging"
ASSIGNMENT_GENERATOR_V4_QUEUE="assignment-generator-v4-queue-staging"
INTERVIEW_INVITE_MAIL_QUEUE="interview-invite-mail-queue-staging"
CANDIDATE_ADDITION_V5_QUEUE="candidate-addition-v5-queue-staging"
QUESTION_APPROVAL_V5_QUEUE="question-approval-v5-queue-staging"
ASSIGNMENT_GENERATOR_V5_QUEUE="assignment-generator-v5-queue-staging"
SEND_JOB_FORM_CLOSED_QUEUE="send-job-form-closed-queue-staging"
SEND_ERROR_MAIL_QUEUE="send-error-mail-queue-staging"
ASSIGNMENT_GENERATOR_V3_QUEUE="assignment-generator-v3-queue-staging"
INTERVIEW_REPORT_MAIL_QUEUE="interview-report-mail-queue-staging"
TECHYRR_COMPANY_ID="tdb8c32bde588491"

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL="mysql://hyrr_admin:<EMAIL>:3306/interviews?connection_limit=7&sslmode=strict&sslcert=/opt/certs/DigiCertGlobalRootCA.crt.pem"
DEFAULT_DAY_START_TIME="00:00:00"
DEFAULT_DAY_END_TIME="23:00:00"

STATIC_TOKEN="w0321lud6t47h9x4lahuwiivh2yhisirno5hy3ekvx5ztp57lxcyman1w3mfsxdvhtik8tgt02bb29t6q8ot52sn0wi8lb0ir2uvl1ijm3bpwnqmom4he00p6oq7of7pxauhlqhsyj0wksyipq8gczxhxt791dgfe7uhs4nav9vedft85eecdtn8h4ejewsmviglu0cl"

EVAL_CLIENT_URL="https://evalai.staging.neusort.com/"
DATA_LAYER_URL="https://datalayer-staging.gentlebay-8bbb7147.centralindia.azurecontainerapps.io/"
DISCOVERY_SERVER_URL="https://discovery-service-staging.gentlebay-8bbb7147.centralindia.azurecontainerapps.io/"
REDIS_HOST="qa-server.redis.cache.windows.net"
REDIS_PORT="6380"
REDIS_PASSWORD="ZHj4dK5F1qZJ5XGfk4ki1Ge7hR02PIVOSAzCaCMfLf4="

#? Gaze Detection Server
PYTHON_SERVER="http://**************:3003/"
DATA_PROCESS_SERVER="http://**************:3004/"

#? Azure 
AZURE_ACCOUNT_NAME="hyrr"
AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=hyrr;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
AZURE_STORAGE_AUDIO_RECORDINGS_CONTAINER_NAME="audio-recordings"
AZURE_STORAGE_RESUMES_CONTAINER_NAME="resumes"
AZURE_ACCOUNT_KEY="****************************************************************************************"

AWS_ACCESS_KEY_ID="********************"
AWS_SECRET_ACCESS_KEY="EBLJIAeYRTfQREZ2mOrSSH3Xc9qdC8HNjUj1xpdR"
BEDROCK_MODEL="us.anthropic.claude-3-7-sonnet-********-v1:0"