FROM ubuntu:22.04

WORKDIR /usr/src/app

# Install essential tools and dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    python3 \
    python3-pip \
    build-essential \
    # libffi-dev \
    # openssl-dev \
    ffmpeg \
    # ffmpeg-dev \
    # libsrtp-dev \
    pkg-config \
    python3-dev \
    libopus-dev \
    libvpx-dev \
    ca-certificates

# Install aiortc
RUN pip3 install aiortc

# Install Node.js
RUN apt-get update && apt-get install -y --no-install-recommends  \
    curl  && \
    curl -fsSL https://deb.nodesource.com/setup_18.x |  bash - && \
    apt-get install -y nodejs

# Copy package files and install dependencies
COPY package*.json ./
RUN npm install

# Copy application files
COPY . .

# Generate prisma client
RUN npx prisma generate

# Configure Git
RUN git config --global user.email "<EMAIL>"
RUN git config --global user.name "Eval AI"

# Create and copy certificates
RUN mkdir /opt/certs
COPY certs/DigiCertGlobalRootCA.crt.pem /opt/certs/DigiCertGlobalRootCA.crt.pem

# Set google application credentials
ENV GOOGLE_APPLICATION_CREDENTIALS=/usr/src/app/certs/hyra-720a2-2f9c8fd85be7.json

# Expose port
EXPOSE 5005


# Command to run the application
CMD ["node", "src/index.js"]
