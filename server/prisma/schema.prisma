generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model applicant_skills {
  id                     BigInt                  @id @unique(map: "id") @default(autoincrement())
  candidate_form_id      BigInt?
  created_by             String?                 @db.VarChar(100)
  modified_by            String?                 @db.VarChar(100)
  created_at             DateTime?               @default(now())
  modified_at            DateTime?               @updatedAt
  language_id            Int?
  candidate_form_mapping candidate_form_mapping? @relation(fields: [candidate_form_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "applicant_skills_ibfk_1")
  programming_language   programming_language?   @relation(fields: [language_id], references: [language_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_language_id")

  @@index([language_id], map: "fk_language_id")
  @@index([candidate_form_id], map: "candidate_form_id")
}

model assignment_submissions {
  id                 Int       @id @default(autoincrement())
  uid                String    @db.VarChar(100)
  github_link        String    @db.VarChar(255)
  firebase_link      String?   @db.Var<PERSON>har(255)
  isParsed           Int?      @db.TinyInt
  score              Float?    @db.Float
  no_of_retries      Int?
  user_assignment_id String?   @db.VarChar(45)
  created_by         String?   @db.VarChar(255)
  modified_by        String?   @db.VarChar(255)
  created_at         DateTime  @default(now())
  modified_at        DateTime? @updatedAt
  was_ai_correct     String?   @db.VarChar(45)
  parse_error        String?   @db.Text
  branch             String?   @db.VarChar(32)
  rooms              rooms[]
}

model coding_conversation {
  roomid                      String                        @db.VarChar(15)
  id                          Int                           @id @default(autoincrement())
  question                    String                        @db.Text
  answer                      String?                       @db.Text
  created_at                  DateTime                      @default(now())
  modified_at                 DateTime?                     @updatedAt
  rooms                       rooms                         @relation(fields: [roomid], references: [roomid], onDelete: NoAction, onUpdate: NoAction, map: "coding_conversation_ibfk_1")
  coding_conversation_ratings coding_conversation_ratings[]
  coding_hints                coding_hints[]

  @@index([roomid], map: "idx_roomid")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model coding_conversation_ratings {
  id                     Int                  @id @default(autoincrement())
  coding_conversation_id Int?
  rating_id              Int?
  rating_value           Int?
  rating_reason          String?              @db.Text
  created_at             DateTime             @default(now())
  modified_at            DateTime?            @updatedAt
  coding_conversation    coding_conversation? @relation(fields: [coding_conversation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "coding_conversation_ratings_ibfk_1")
  ratings                ratings?             @relation(fields: [rating_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "coding_conversation_ratings_ibfk_2")

  @@index([coding_conversation_id], map: "coding_conversation_id")
  @@index([rating_id], map: "rating_id")
}

model coding_hints {
  id                     Int                  @id @default(autoincrement())
  coding_conversation_id Int?
  hint                   String?              @db.Text
  created_at             DateTime             @default(now())
  modified_at            DateTime?            @updatedAt
  query                  String?              @db.Text
  case_triggered         String?              @db.VarChar(255)
  coding_conversation    coding_conversation? @relation(fields: [coding_conversation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "coding_hints_ibfk_1")

  @@index([coding_conversation_id], map: "coding_conversation_id")
}

model consumption_error_logs {
  id          Int       @id @default(autoincrement())
  message     String    @db.Text
  error       String    @db.Text
  created_at  DateTime  @default(now())
  modified_at DateTime? @updatedAt
  expired     Boolean   @default(false)
}

model conversation {
  roomid               String                 @db.VarChar(15)
  id                   Int                    @id @default(autoincrement())
  text                 String?                @db.Text
  speaker              Boolean
  created_at           DateTime               @default(now())
  modified_at          DateTime?              @updatedAt
  rooms                rooms                  @relation(fields: [roomid], references: [roomid], onDelete: NoAction, onUpdate: NoAction, map: "conversation_ibfk_1")
  conversation_ratings conversation_ratings[]

  @@index([roomid], map: "roomid")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model conversation_ratings {
  id              Int           @id @default(autoincrement())
  conversation_id Int?
  rating_id       Int?
  rating_value    Int?
  rating_reason   String?       @db.Text
  conversation    conversation? @relation(fields: [conversation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_ratings_ibfk_1")
  ratings         ratings?      @relation(fields: [rating_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_ratings_ibfk_2")

  @@index([conversation_id], map: "conversation_id")
  @@index([rating_id], map: "rating_id")
}

model experience {
  uid             String   @db.VarChar(50)
  name_of_company String   @db.VarChar(255)
  job_desc        String   @db.VarChar(255)
  deleted         Boolean  @default(false)
  start_date      DateTime @db.Date
  end_date        DateTime @db.Date
  modified_time   DateTime @updatedAt
  created_by      String?  @default("\"system\"") @db.VarChar(45)

  @@id([uid, name_of_company, job_desc])
}

model hired {
  id                Int       @id @default(autoincrement())
  uid               String    @db.VarChar(100)
  created_date_time DateTime? @default(now()) @db.Timestamp(0)
}

model initial_questions {
  id          Int       @id @default(autoincrement())
  question    String    @db.VarChar(255)
  created_at  DateTime  @default(now())
  modified_at DateTime? @updatedAt
  expired     Boolean   @default(false)
}

model job_applications {
  id                 BigInt    @id @unique(map: "id") @default(autoincrement()) @db.UnsignedBigInt
  uid                String    @unique(map: "uid") @db.VarChar(255)
  full_name          String    @db.VarChar(255)
  email              String    @db.VarChar(255)
  phone_no           String    @db.VarChar(10)
  current_ctc        Int?
  expected_ctc       Int?
  notice_period      Int?
  open_to_relocate   Boolean
  preferred_location String    @db.VarChar(255)
  created_by         String?   @db.VarChar(100)
  modified_by        String?   @db.VarChar(100)
  created_at         DateTime? @default(now())
  modified_at        DateTime? @updatedAt
}

model languages {
  id          Int       @id @default(autoincrement())
  language    String    @db.VarChar(10)
  expired     Boolean   @default(false)
  created_by  String    @db.VarChar(255)
  modified_by String?   @db.VarChar(255)
  created_at  DateTime  @default(now())
  modified_at DateTime? @updatedAt
  rooms       rooms[]
}

model locations_db {
  uid           String        @db.VarChar(50)
  location_id   Int
  deleted       Boolean       @default(false)
  created_time  DateTime      @default(now())
  modified_time DateTime      @updatedAt
  created_by    String?       @db.VarChar(255)
  modified_by   String?       @db.VarChar(255)
  worklocations worklocations @relation(fields: [location_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "locations_db_ibfk_1")

  @@id([uid, location_id])
  @@index([location_id], map: "location_id")
}

model metrics {
  id            Int     @id @default(autoincrement())
  firebase_link String? @db.VarChar(200)
  model_used    String? @db.VarChar(45)
  k_value       String? @db.VarChar(45)
  p_value       String? @db.VarChar(45)
  no_of_retries Int?
  submission_id Int?
}

model programming_language {
  language_id                             Int                                       @id @default(autoincrement())
  programming                             String                                    @unique(map: "programming_UNIQUE") @db.VarChar(255)
  created_by                              String                                    @db.VarChar(255)
  modified_by                             String?                                   @db.VarChar(255)
  created_at                              DateTime                                  @default(now()) @db.Timestamp(0)
  modified_at                             DateTime?                                 @updatedAt
  deleted                                 Boolean?                                  @default(false)
  is_evaluable                            Int?                                      @default(1) @db.TinyInt
  applicant_skills                        applicant_skills[]
  assignment_language_combinations        assignment_language_combinations[]
  assignment_programming_language_mapping assignment_programming_language_mapping[]
  custom_skills                           custom_skills[]
  job_forms                               form_language_mapping[]
  programming_custom_skills               programming_custom_skills[]
  programming_language_group_mapping      programming_language_group_mapping[]
  programming_skills                      programming_skills[]
  skills_db                               skills_db[]
}

model programming_language_group {
  id                                 Int                                  @id @default(autoincrement())
  name                               String?                              @db.VarChar(100)
  created_by                         String                               @db.VarChar(255)
  modified_by                        String?                              @db.VarChar(255)
  created_at                         DateTime                             @default(now()) @db.Timestamp(0)
  modified_at                        DateTime?                            @updatedAt
  programming_language_group_mapping programming_language_group_mapping[]
}

model programming_language_group_mapping {
  id                            Int                         @id @default(autoincrement())
  programming_language_id       Int?
  programming_language_group_id Int?
  created_by                    String                      @db.VarChar(255)
  modified_by                   String?                     @db.VarChar(255)
  created_at                    DateTime                    @default(now()) @db.Timestamp(0)
  modified_at                   DateTime?                   @updatedAt
  programming_language          programming_language?       @relation(fields: [programming_language_id], references: [language_id], onDelete: NoAction, onUpdate: NoAction, map: "programming_language_fk_1")
  programming_language_group    programming_language_group? @relation(fields: [programming_language_group_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "programming_language_group_fk_2")

  @@index([programming_language_id], map: "programming_language_fk_1")
  @@index([programming_language_group_id], map: "programming_language_group_fk_2")
}

model ratings {
  id                          Int                           @id @default(autoincrement())
  rating_title                String                        @db.VarChar(255)
  created_at                  DateTime                      @default(now()) @db.Timestamp(0)
  modified_at                 DateTime?                     @updatedAt
  expired                     Boolean                       @default(false)
  coding_conversation_ratings coding_conversation_ratings[]
  conversation_ratings        conversation_ratings[]
  response_ratings            response_ratings[]
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model response_ratings {
  id            Int        @id @default(autoincrement())
  response_id   Int?
  rating_id     Int?
  rating_value  Int?
  rating_reason String?    @db.Text
  responses     responses? @relation(fields: [response_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "response_ratings_ibfk_1")
  ratings       ratings?   @relation(fields: [rating_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "response_ratings_ibfk_2")

  @@index([rating_id], map: "rating_id")
  @@index([response_id], map: "response_id")
}

model responses {
  roomid           String             @db.VarChar(15)
  id               Int                @id @default(autoincrement())
  question         String?            @db.Text
  answer           String?            @db.Text
  error_type       String?            @db.Text
  error_details    String?            @db.Text
  created_at       DateTime           @default(now()) @db.Timestamp(0)
  modified_at      DateTime?          @updatedAt
  response_ratings response_ratings[]
  rooms            rooms              @relation(fields: [roomid], references: [roomid], onDelete: NoAction, onUpdate: NoAction, map: "responses_ibfk_1")

  @@index([roomid], map: "roomid")
}

model rooms {
  id                     Int                     @id @default(autoincrement())
  is_demo                Boolean                 @default(false)
  demo_length            Int                     @default(0)
  roomid                 String                  @unique @db.VarChar(15)
  user_email             String                  @db.VarChar(255)
  created_by             String                  @db.VarChar(255)
  modified_by            String?                 @db.VarChar(255)
  created_at             DateTime                @default(now()) @db.Timestamp(0)
  modified_at            DateTime?               @updatedAt
  language_id            Int
  language_type          Int                     @default(1)
  submission_id          Int?
  interview_taken        Boolean?                @default(false)
  file_create_time       String?                 @db.VarChar(20)
  coding_conversation    coding_conversation[]
  conversation           conversation[]
  feedbacks              feedbacks[]
  feedbacks2             feedbacks2[]
  responses              responses[]
  languages              languages               @relation(fields: [language_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "rooms_ibfk_1")
  assignment_submissions assignment_submissions? @relation(fields: [submission_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "rooms_ibfk_2")
  summary                summary[]
  time_slots             time_slots[]

  @@index([roomid], map: "idx_roomid")
  @@index([language_id], map: "language_id")
  @@index([submission_id], map: "submission_id")
}

model skills_db {
  uid                  String               @db.VarChar(50)
  skill_id             Int
  rating               Int
  deleted              Boolean              @default(false)
  modified_time        DateTime             @updatedAt
  created_time         DateTime             @default(now()) @db.Timestamp(0)
  created_by           String?              @db.VarChar(255)
  modified_by          String?              @db.VarChar(255)
  programming_language programming_language @relation(fields: [skill_id], references: [language_id], onDelete: NoAction, onUpdate: NoAction, map: "skills_db_ibfk_1")

  @@id([uid, skill_id])
  @@index([skill_id], map: "skill_id")
}

model summary {
  id          Int       @id @default(autoincrement())
  roomid      String    @db.VarChar(15)
  summary     String    @db.Text
  created_at  DateTime  @default(now()) @db.Timestamp(0)
  modified_at DateTime? @updatedAt
  other_info  Json?
  rooms       rooms     @relation(fields: [roomid], references: [roomid], onDelete: NoAction, onUpdate: NoAction, map: "summary_ibfk_1")

  @@index([roomid], map: "roomid")
}

model time_slots {
  id                         Int       @id @default(autoincrement())
  roomid                     String    @db.VarChar(15)
  start_time                 DateTime  @default(now()) @db.Timestamp(0)
  end_time                   DateTime  @default(now()) @db.Timestamp(0)
  preferred_time_range_start DateTime  @default(dbgenerated("'09:00:00'")) @db.Time(0)
  preferred_time_range_end   DateTime  @default(dbgenerated("'21:00:00'")) @db.Time(0)
  created_at                 DateTime  @default(now())
  modified_at                DateTime? @updatedAt
  rooms                      rooms     @relation(fields: [roomid], references: [roomid], onDelete: NoAction, onUpdate: NoAction, map: "time_slots_ibfk_1")

  @@index([end_time], map: "idx_end_time")
  @@index([start_time], map: "idx_start_time")
  @@index([roomid], map: "roomid")
}

model time_slots_availability {
  id             Int       @id @default(autoincrement())
  date           DateTime  @db.Date
  start_time     DateTime  @db.Time(0)
  end_time       DateTime  @db.Time(0)
  occupied_count Int       @default(0)
  created_at     DateTime  @default(now())
  modified_at    DateTime? @updatedAt
}

model user_assignments {
  id                       Int                     @id @default(autoincrement())
  applicationid            BigInt?
  deadline                 DateTime?               @db.DateTime(0)
  created_by               String?                 @db.VarChar(255)
  modified_by              String?                 @db.VarChar(50)
  deleted                  Int?                    @default(0) @db.TinyInt
  problemStatement         String?                 @db.Text
  base_assignment_exp      Int?
  other_assignments        Json?
  programming_language_ids String?                 @db.VarChar(255)
  assignment_hash          String?                 @db.VarChar(255)
  created_at               DateTime?               @default(now()) @db.Timestamp(0)
  modified_at              DateTime?               @default(now()) @db.Timestamp(0)
  extension_reason         Json?
  candidate                candidate_form_mapping? @relation(fields: [applicationid], references: [id])

  @@index([applicationid], map: "user_assignments_applicationid_fkey")
}

model work_experience {
  id                     BigInt                  @id @unique(map: "id") @default(autoincrement())
  candidate_form_id      BigInt?
  company_name           String                  @db.VarChar(255)
  job_title              String                  @db.VarChar(255)
  exp_in_years           Float?                  @db.Float
  created_by             String?                 @db.VarChar(100)
  modified_by            String?                 @db.VarChar(100)
  created_at             DateTime?               @default(now())
  modified_at            DateTime?               @updatedAt
  candidate_form_mapping candidate_form_mapping? @relation(fields: [candidate_form_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "work_experience_ibfk_1")

  @@index([candidate_form_id], map: "candidate_form_id")
}

model worklocations {
  id           Int            @id @default(autoincrement())
  location     String         @db.VarChar(255)
  created_by   String?        @db.VarChar(255)
  modified_by  String?        @db.VarChar(255)
  created_at   DateTime       @default(now())
  modified_at  DateTime?      @updatedAt
  deleted      Boolean?
  locations_db locations_db[]
}

model user_invitation {
  id          Int       @id @default(autoincrement())
  user_name   String    @db.VarChar(255)
  user_email  String    @unique
  invite_id   String    @unique
  created_at  DateTime  @default(now())
  modified_at DateTime? @updatedAt
  licences    Json?

  @@unique([user_email, invite_id])
}

model applicant_education {
  id                     BigInt                  @id @unique(map: "id") @default(autoincrement())
  candidate_form_id      BigInt?
  degree                 String                  @db.VarChar(255)
  graduation_year        String                  @db.VarChar(50)
  percentage             Decimal                 @db.Decimal(5, 2)
  created_by             String?                 @db.VarChar(100)
  modified_by            String?                 @db.VarChar(100)
  created_at             DateTime?               @default(now()) @db.Timestamp(0)
  modified_at            DateTime?               @default(now()) @db.Timestamp(0)
  college                String                  @db.VarChar(255)
  candidate_form_mapping candidate_form_mapping? @relation(fields: [candidate_form_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "applicant_education_ibfk_1")

  @@index([candidate_form_id], map: "candidate_form_id")
}

model assignment_keywords {
  id              Int       @id @default(autoincrement())
  word            String    @db.VarChar(100)
  count           BigInt?
  assignment_hash String    @db.VarChar(100)
  created_by      String    @db.VarChar(255)
  modified_by     String?   @db.VarChar(255)
  created_at      DateTime  @default(now()) @db.Timestamp(0)
  modified_at     DateTime? @default(now()) @db.Timestamp(0)

  @@unique([word, assignment_hash], map: "unique_word")
}

model programming_skills {
  id                                   Int                                    @id @default(autoincrement())
  programming_language_id              Int?
  experience                           Int
  skill                                String?                                @db.Text
  created_by                           String                                 @default("brij") @db.VarChar(100)
  modified_by                          String?                                @default("brij") @db.VarChar(100)
  created_at                           DateTime                               @default(now()) @db.Timestamp(0)
  modified_at                          DateTime?                              @default(now()) @db.Timestamp(0)
  assignment_programming_skill_mapping assignment_programming_skill_mapping[]
  form_custom_skill_mapping            form_custom_skill_mapping[]
  form_skill_mapping                   form_skill_mapping[]
  programming_language                 programming_language?                  @relation(fields: [programming_language_id], references: [language_id], onDelete: NoAction, onUpdate: NoAction, map: "programming_skills_ibfk_1")

  @@index([programming_language_id], map: "programming_language_id")
}

model candidate {
  id          BigInt                   @id @default(autoincrement())
  name        String
  email       String                   @unique
  created_at  DateTime                 @default(now())
  modified_at DateTime?                @updatedAt
  uid         String?                  @unique @db.VarChar(255)
  jobForms    candidate_form_mapping[]
}

model job_form {
  id                        BigInt                      @id @default(autoincrement())
  adminId                   String
  otherSkills               String
  experience                String
  companyId                 BigInt
  created_at                DateTime                    @default(now())
  modified_at               DateTime?                   @updatedAt
  adminName                 String?
  jobRole                   String?
  jobDescription            String?                     @db.Text
  fromTechyrr               Int?                        @default(0) @db.TinyInt
  isDeleted                 Boolean?                    @default(false)
  isDraft                   Boolean?                    @default(false)
  licence                   String?                     @default("v1") @db.VarChar(30)
  candidate_form_mapping    candidate_form_mapping[]
  custom_skills             custom_skills[]
  form_custom_skill_mapping form_custom_skill_mapping[]
  languages                 form_language_mapping[]
  form_skill_mapping        form_skill_mapping[]
  jd_extracted_data         jd_extracted_data[]
  company                   company                     @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@index([companyId], map: "job_form_companyId_fkey")
}

model company {
  id                       BigInt                     @id @default(autoincrement())
  name                     String                     @unique
  created_at               DateTime                   @default(now())
  modified_at              DateTime?                  @updatedAt
  numberOfCoins            Int?                       @default(100)
  company_techyrr          Int?                       @default(0) @db.TinyInt
  database_company_mapping database_company_mapping[]
  forms                    job_form[]
}

model candidate_form_mapping {
  candidateId         BigInt
  formId              BigInt
  created_at          DateTime              @default(now())
  modified_at         DateTime?             @updatedAt
  candidate_phoneNo   String?
  id                  BigInt                @id @default(autoincrement())
  current_ctc         Int?
  expected_ctc        Int?
  notice_period       Int?
  open_to_relocate    Boolean?
  preferred_location  String?               @db.VarChar(255)
  partner_code        String?               @db.VarChar(255)
  num_of_assignment   Int?
  applicant_education applicant_education[]
  applicant_skills    applicant_skills[]
  candidate           candidate             @relation(fields: [candidateId], references: [id], onDelete: Cascade)
  form                job_form              @relation(fields: [formId], references: [id], onDelete: Cascade)
  assignment          user_assignments[]
  work_experience     work_experience[]

  @@index([formId], map: "candidate_form_mapping_formId_fkey")
  @@index([candidateId], map: "idx_candidateId")
}

model form_language_mapping {
  formId      BigInt
  languageId  Int
  created_at  DateTime             @default(now())
  modified_at DateTime?            @updatedAt
  isDeleted   Boolean              @default(false)
  form        job_form             @relation(fields: [formId], references: [id], onDelete: Cascade)
  language    programming_language @relation(fields: [languageId], references: [language_id], onDelete: Cascade)

  @@id([formId, languageId])
  @@index([languageId], map: "form_language_mapping_languageId_fkey")
  @@index([formId, languageId], map: "idx_form_language")
}

model generic_assignments {
  id                       Int     @id @default(autoincrement())
  problem_statement        String? @db.Text
  alias                    String  @unique(map: "alias") @db.VarChar(255)
  programming_language_ids String? @default("") @db.VarChar(255)
}

model interviewee_voice_embeddings {
  id           BigInt    @id @unique(map: "id") @default(autoincrement()) @db.UnsignedBigInt
  uid          String    @db.VarChar(50)
  interview_id String    @db.VarChar(50)
  embeddings   Json
  created_by   String?   @db.VarChar(100)
  modified_by  String?   @db.VarChar(100)
  created_at   DateTime? @default(now()) @db.Timestamp(0)
  modified_at  DateTime? @default(now()) @db.Timestamp(0)
}

model assignment_language_combinations {
  id                   Int                   @id @default(autoincrement())
  group_id             Int?
  language_id          Int?
  created_by           String?               @db.VarChar(255)
  modified_by          String?               @db.VarChar(255)
  created_at           DateTime?             @default(now()) @db.Timestamp(0)
  modified_at          DateTime?             @default(now()) @db.Timestamp(0)
  deleted              Boolean?              @default(false)
  programming_language programming_language? @relation(fields: [language_id], references: [language_id], onDelete: NoAction, onUpdate: NoAction, map: "fk1_language_id")

  @@index([language_id], map: "fk1_language_id")
}

model feedbacks {
  id          Int       @id @default(autoincrement())
  roomid      String?   @db.VarChar(15)
  rating      Int
  suggestion  String?   @db.Text
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  modified_at DateTime? @default(now()) @db.Timestamp(0)
  uid         String?   @db.VarChar(255)
  rooms       rooms?    @relation(fields: [roomid], references: [roomid], onDelete: NoAction, onUpdate: NoAction, map: "feedbacks_ibfk_2")

  @@index([roomid], map: "roomid")
}

model edges {
  id                           Int     @id @default(autoincrement())
  source_id                    Int
  target_id                    Int
  description                  String? @db.VarChar(255)
  nodes_edges_source_idTonodes nodes   @relation("edges_source_idTonodes", fields: [source_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "edges_ibfk_1")
  nodes_edges_target_idTonodes nodes   @relation("edges_target_idTonodes", fields: [target_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "edges_ibfk_2")

  @@index([source_id], map: "source_id")
  @@index([target_id], map: "target_id")
}

model nodes {
  id                           Int     @id @default(autoincrement())
  name                         String  @db.VarChar(255)
  edges_edges_source_idTonodes edges[] @relation("edges_source_idTonodes")
  edges_edges_target_idTonodes edges[] @relation("edges_target_idTonodes")
}

model form_skill_mapping {
  id          Int                @id @default(autoincrement())
  formId      BigInt
  skillId     Int
  created_at  DateTime?          @default(now()) @db.Timestamp(0)
  modified_at DateTime?          @default(now()) @db.Timestamp(0)
  isDeleted   Boolean            @default(false)
  form        job_form           @relation(fields: [formId], references: [id])
  skill       programming_skills @relation(fields: [skillId], references: [id])

  @@index([formId], map: "form_skill_mapping_formId_fkey")
  @@index([skillId], map: "form_skill_mapping_skillId_fkey")
}

model custom_skills {
  id          Int                  @id @default(autoincrement())
  formId      BigInt
  skillName   String
  languageId  Int
  created_at  DateTime?            @default(now()) @db.Timestamp(0)
  modified_at DateTime?            @default(now()) @db.Timestamp(0)
  isDeleted   Boolean              @default(false)
  form        job_form             @relation(fields: [formId], references: [id])
  language    programming_language @relation(fields: [languageId], references: [language_id])

  @@index([formId], map: "custom_skills_formId_fkey")
  @@index([languageId], map: "custom_skills_languageId_fkey")
}

model company_databases {
  database_name            String                     @id @db.Char(16)
  available                Int                        @default(1) @db.TinyInt
  created_at               DateTime?                  @default(now()) @db.Timestamp(0)
  modified_at              DateTime?                  @default(now()) @db.Timestamp(0)
  company_count            Int?                       @default(0)
  database_company_mapping database_company_mapping[]
}

model database_company_mapping {
  id                BigInt             @id @default(autoincrement())
  database_name     String?            @db.Char(16)
  company_id        BigInt?
  created_at        DateTime?          @default(now()) @db.Timestamp(0)
  modified_at       DateTime?          @default(now()) @db.Timestamp(0)
  company_databases company_databases? @relation(fields: [database_name], references: [database_name], onDelete: NoAction, onUpdate: NoAction, map: "database_company_mapping_ibfk_1")
  company           company?           @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "database_company_mapping_ibfk_2")

  @@index([company_id], map: "company_id")
  @@index([database_name], map: "database_name")
}

model techyrr_order {
  id          Int       @id @default(autoincrement())
  uid         String    @db.VarChar(255)
  order_id    String    @unique @db.VarChar(255)
  signature   String?   @unique @db.VarChar(255)
  payment_id  String?   @unique @db.VarChar(255)
  amount      Int
  hasPaid     Int       @default(0) @db.TinyInt
  created_at  DateTime? @default(now())
  modified_at DateTime?
}

model contact_message {
  id          Int       @id @default(autoincrement())
  email       String    @db.VarChar(255)
  name        String    @db.VarChar(255)
  company     String    @db.VarChar(255)
  message     String    @db.Text
  created_at  DateTime  @default(now())
  modified_at DateTime?
}

model programming_custom_skills {
  id                                          Int                                           @id @default(autoincrement())
  programming_language_id                     Int?
  experience                                  Int
  skill                                       String?                                       @db.Text
  approved                                    Int?                                          @default(0) @db.TinyInt
  created_by                                  String                                        @default("brij") @db.VarChar(100)
  modified_by                                 String?                                       @default("brij") @db.VarChar(100)
  created_at                                  DateTime                                      @default(now()) @db.Timestamp(0)
  modified_at                                 DateTime?                                     @default(now()) @db.Timestamp(0)
  assignment_custom_programming_skill_mapping assignment_custom_programming_skill_mapping[]
  programming_language                        programming_language?                         @relation(fields: [programming_language_id], references: [language_id], onDelete: NoAction, onUpdate: NoAction, map: "programming_custom_skills_ibfk_1")

  @@index([programming_language_id], map: "programming_language_id")
}

model form_custom_skill_mapping {
  id                 Int                @id @default(autoincrement())
  formId             BigInt
  skillId            Int
  created_at         DateTime?          @default(now()) @db.Timestamp(0)
  modified_at        DateTime?          @default(now()) @db.Timestamp(0)
  isDeleted          Boolean            @default(false)
  job_form           job_form           @relation(fields: [formId], references: [id], map: "form_custom_skill_mapping_ibfk_1")
  programming_skills programming_skills @relation(fields: [skillId], references: [id], map: "form_custom_skill_mapping_ibfk_2")

  @@index([formId], map: "formId")
  @@index([skillId], map: "skillId")
}

model assignment_branch {
  id              Int             @id @default(autoincrement())
  branch          String          @db.VarChar(32)
  yoe             Int
  assignment_id   Int
  created_at      DateTime?       @default(now()) @db.Timestamp(0)
  modified_at     DateTime?       @default(now()) @db.Timestamp(0)
  created_by      String?         @db.VarChar(15)
  modified_by     String?         @db.VarChar(15)
  assignment_pool assignment_pool @relation(fields: [assignment_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "assignment_branch_ibfk_1")

  @@index([assignment_id], map: "assignment_id")
}

model assignment_custom_programming_skill_mapping {
  id                          Int                       @id @default(autoincrement())
  assignment_id               Int
  custom_programming_skill_id Int
  created_at                  DateTime?                 @default(now()) @db.Timestamp(0)
  modified_at                 DateTime?                 @default(now()) @db.Timestamp(0)
  created_by                  String?                   @db.VarChar(15)
  modified_by                 String?                   @db.VarChar(15)
  assignment_pool             assignment_pool           @relation(fields: [assignment_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "assignment_custom_programming_skill_mapping_ibfk_1")
  programming_custom_skills   programming_custom_skills @relation(fields: [custom_programming_skill_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "assignment_custom_programming_skill_mapping_ibfk_2")

  @@index([assignment_id], map: "assignment_id")
  @@index([custom_programming_skill_id], map: "custom_programming_skill_id")
}

model assignment_pool {
  id                                          Int                                           @id @default(autoincrement())
  questions_link                              String?                                       @db.VarChar(255)
  problem_statement                           String                                        @db.Text
  repo_link                                   String?                                       @db.VarChar(255)
  created_at                                  DateTime?                                     @default(now()) @db.Timestamp(0)
  modified_at                                 DateTime?                                     @default(now()) @db.Timestamp(0)
  last_used                                   DateTime?                                     @default(now()) @db.Timestamp(0)
  created_by                                  String?                                       @db.VarChar(15)
  modified_by                                 String?                                       @db.VarChar(15)
  used_by                                     Json?
  assignment_hash                             String?                                       @db.Text
  assignment_branch                           assignment_branch[]
  assignment_custom_programming_skill_mapping assignment_custom_programming_skill_mapping[]
  assignment_programming_language_mapping     assignment_programming_language_mapping[]
  assignment_programming_skill_mapping        assignment_programming_skill_mapping[]
}

model assignment_programming_language_mapping {
  id                      Int                  @id @default(autoincrement())
  assignment_id           Int
  programming_language_id Int
  created_at              DateTime?            @default(now()) @db.Timestamp(0)
  modified_at             DateTime?            @default(now()) @db.Timestamp(0)
  created_by              String?              @db.VarChar(15)
  modified_by             String?              @db.VarChar(15)
  assignment_pool         assignment_pool      @relation(fields: [assignment_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "assignment_programming_language_mapping_ibfk_1")
  programming_language    programming_language @relation(fields: [programming_language_id], references: [language_id], onDelete: NoAction, onUpdate: NoAction, map: "assignment_programming_language_mapping_ibfk_2")

  @@index([assignment_id], map: "assignment_id")
  @@index([programming_language_id], map: "programming_language_id")
}

model assignment_programming_skill_mapping {
  id                   Int                @id @default(autoincrement())
  assignment_id        Int
  programming_skill_id Int
  created_at           DateTime?          @default(now()) @db.Timestamp(0)
  modified_at          DateTime?          @default(now()) @db.Timestamp(0)
  created_by           String?            @db.VarChar(15)
  modified_by          String?            @db.VarChar(15)
  assignment_pool      assignment_pool    @relation(fields: [assignment_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "assignment_programming_skill_mapping_ibfk_1")
  programming_skills   programming_skills @relation(fields: [programming_skill_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "assignment_programming_skill_mapping_ibfk_2")

  @@index([assignment_id], map: "assignment_id")
  @@index([programming_skill_id], map: "programming_skill_id")
}

model feedbackquestion {
  id         Int                 @id @default(autoincrement())
  feedbackId Int?
  answer     String?             @db.Text
  questionId Int?
  feedbacks  feedbacks2?         @relation(fields: [feedbackId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "feedbackquestion_ibfk_1")
  questions  feedback_questions? @relation(fields: [questionId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_questionId")

  @@index([feedbackId], map: "feedbackId")
  @@index([questionId], map: "fk_questionId")
}

model feedbacks2 {
  id               Int                @id @default(autoincrement())
  roomid           String?            @db.VarChar(15)
  created_at       DateTime?          @default(now()) @db.Timestamp(0)
  modified_at      DateTime?          @default(now()) @db.Timestamp(0)
  uid              String?            @db.VarChar(255)
  feedbackquestion feedbackquestion[]
  rooms            rooms?             @relation(fields: [roomid], references: [roomid], onDelete: NoAction, onUpdate: NoAction, map: "feedbacks2_ibfk_1")

  @@index([roomid], map: "roomid")
}

model jd_extracted_data {
  id              Int       @id @default(autoincrement())
  job_form_id     BigInt?
  adminId         String    @db.VarChar(50)
  job_description String?   @db.Text
  gpt_response    Json?
  job_form        job_form? @relation(fields: [job_form_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "jd_extracted_data_ibfk_1")

  @@index([job_form_id], map: "job_form_id")
}

model feedback_questions {
  id               Int                @id @default(autoincrement())
  text             String             @db.VarChar(255)
  type             String             @db.VarChar(50)
  created_at       DateTime?          @default(now()) @db.Timestamp(0)
  updated_at       DateTime?          @default(now()) @db.Timestamp(0)
  created_by       String             @db.VarChar(20)
  modified_by      String?            @db.VarChar(20)
  deleted          Boolean?           @default(false)
  feedbackquestion feedbackquestion[]
}
