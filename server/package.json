{"name": "server", "version": "1.0.0", "description": "SocketIO Server", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/index.js", "dev": "nodemon src/index.js"}, "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@aws-sdk/client-bedrock-runtime": "^3.812.0", "@azure/storage-blob": "^12.25.0", "@deepgram/sdk": "^3.2.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/vertexai": "^1.7.0", "@prisma/client": "5.15.0", "@socket.io/cluster-adapter": "^0.2.2", "@socket.io/sticky": "^1.0.4", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.4", "archiver": "^7.0.1", "axios": "^1.8.2", "busboy": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.4.2", "express": "^4.18.2", "firebase-admin": "^12.0.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.2", "ioredis": "^5.4.1", "mediasoup-client": "^3.7.8", "mediasoup-client-aiortc": "3.10.7", "microsoft-cognitiveservices-speech-sdk": "^1.35.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mysql2": "^3.9.2", "neusortlib": "https://<EMAIL>/neusort/neusortlib/tarball/b623bb012928836daec3a44503a41317899fc779", "openai": "^4.86.2", "pdf-parse": "^1.1.1", "pdf-to-img": "^4.3.0", "simple-git": "^3.24.0", "socket.io": "^4.7.4", "socket.io-client": "^4.7.5", "tesseract.js": "^6.0.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.3", "prisma": "5.15.0"}}