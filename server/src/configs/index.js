const path = require("path");
const fs = require("fs");
const readFileSync = (filePath) =>
  fs.readFileSync(path.join(__dirname, filePath));

const corsOptions = {
  origin: "*",
  methods: ["GET", "POST"],
};

const caCert = fs.readFileSync(
  path.resolve(__dirname, "../../certs/__hyrr_app.ca-bundle")
);
const cert = fs.readFileSync(
  path.resolve(__dirname, "../../certs/__hyrr_app.crt")
);
const key = fs.readFileSync(path.resolve(__dirname, "../../certs/server.key"));

module.exports = {
  corsOptions,
  readFileSync,
  caCert,
  cert,
  key,
};
