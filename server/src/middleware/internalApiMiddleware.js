function internalApiMiddleware(request, response, next) {
  const headerToken = request.headers.authorization;
  if (!headerToken) {
    return response.status(401).send({ message: "No token provided" });
  }

  if (headerToken && headerToken.split(" ")[0] !== "Bearer") {
    return response.status(401).send({ message: "Invalid token" });
  }

  const token = headerToken.split(" ")[1];
  if (token === process.env.STATIC_TOKEN) {
    console.log("internalApiMiddleware successful");
    next();
  } else {
    return response.status(403).send({ success: false, message: "Could not authorize" });
  }
}

module.exports = { internalApiMiddleware };