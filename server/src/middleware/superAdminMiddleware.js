const app = require("firebase-admin");

async function superAdminMiddleware(req, res, next) {
  const headerToken = req.headers.authorization;

  if (!headerToken) {
    return res.status(401).send({ message: "No token provided" });
  }

  if (headerToken && headerToken.split(" ")[0] !== "Bearer") {
    return res.status(401).send({ message: "Invalid token" });
  }

  const token = headerToken.split(" ")[1];

  try {
    const claims = await app.auth().verifyIdToken(token);

    if (claims.superAdmin === true) {
      console.log("Super Admin Middleware successful");
      next();
    } else {
      return res.status(403).send({ message: "Access denied" });
    }
  } catch (error) {
    console.error("Error verifying token:", error);
    return res.status(500).send({ message: "Internal server error" });
  }
}

module.exports = superAdminMiddleware;
