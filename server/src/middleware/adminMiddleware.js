const app = require("firebase-admin");

async function adminMiddleware(req, res, next) {
  if (req.path === "/signup") return next();
  const headerToken = req.headers.authorization;
  if (!headerToken) {
    return res.status(401).send({ message: "No token provided" });
  }
  if (headerToken && headerToken.split(" ")[0] !== "Bearer") {
    return res.status(401).send({ message: "Invalid token" });
  }
  const token = headerToken.split(" ")[1];
  const claims = await app.auth().verifyIdToken(token);
  if (claims.admin === true) {
    res.locals.companyId = claims.companyId;
    next();
  } else {
    res.status(403).send("Access denied");
  }
}

module.exports = adminMiddleware;
