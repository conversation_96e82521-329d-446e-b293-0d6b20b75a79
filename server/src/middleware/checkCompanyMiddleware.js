const prisma = require("../utils/prisma");

async function checkCompanyMiddleware(req, res, next) {
	const { formId, adminId } = req.body;
	const { companyId } = res.locals;

	if (companyId) return next();

  const companyOfAdmin = await prisma.job_form.findFirst({
		where: {
			adminId: adminId,
			id: formId,
		},
		select: {
			company: {
				select: {
					name: true,
				},
			},
		},
	});

	if (companyOfAdmin && companyOfAdmin.company.name) {
		console.log("Company Middleware successful");
		next();
	} else {
		return res.status(403).send("Access denied");
	}
}

module.exports = checkCompanyMiddleware;
