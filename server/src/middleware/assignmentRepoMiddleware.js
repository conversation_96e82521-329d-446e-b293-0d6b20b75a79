const firebase = require("firebase-admin");
const { botId } = require("../constants/roomConstants");

const assignmentRepoMiddleware = (request, response, next) => {
  const cookieHeader = request.headers.cookie || '';

  request.cookies = cookieHeader.split(';').reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split('=');
    acc[key] = value;
    return acc;
  }, {});

  const token = request.cookies.authToken;

  if (!token) {
    return response
      .status(403)
      .send({ success: false, message: "Provide token to access assignment" })
  }

  firebase
    .auth()
    .verifyIdToken(token)
    .then((decodedToken) => {
      response.locals.uid = decodedToken.uid;
      response.locals.email = decodedToken.email;
      response.locals.name = decodedToken.name;
      response.locals.role =
        decodedToken.interviewer || decodedToken.uid === botId
          ? "interviewer"
          : "interviewee";
      next();
    })
    .catch(() =>
      response
        .status(403)
        .send({ success: false, message: "Could not authorize" })
    );
};

module.exports = assignmentRepoMiddleware;