/*
    auth-middleware.js
*/
const firebase = require("firebase-admin");
const { botId } = require("../constants/roomConstants");

function authMiddleware(request, response, next) {
  if (
    request.path === "/admin/signup" ||
    request.path === "/techyrr/check-company" ||
    request.path.startsWith("/internal") ||
    request.path.startsWith("/techhyrr-admin") ||
    request.path.startsWith("/assignment-repo") ||
    request.path.startsWith("/public")
  ) {
    return next();
  }
  const headerToken = request.headers.authorization;
  if (!headerToken) {
    return response.status(401).send({ message: "No token provided" });
  }

  const companyId = request.headers.companyid;
  if (companyId) response.locals.companyId = companyId;

  if (headerToken && headerToken.split(" ")[0] !== "Bearer") {
    response.status(401).send({ message: "Invalid token" });
  }

  const token = headerToken.split(" ")[1];
  firebase
    .auth()
    .verifyIdToken(token)
    .then((decodedToken) => {
      response.locals.uid = decodedToken.uid;
      response.locals.email = decodedToken.email;
      response.locals.name = decodedToken.name;
      response.locals.role =
        decodedToken.interviewer || decodedToken.uid === botId
          ? "interviewer"
          : "interviewee";
      next();
    })
    .catch(() =>
      response
        .status(403)
        .send({ success: false, message: "Could not authorize" })
    );
}

function authMiddlewareSocket(socket, next) {
  const headerToken = socket.handshake.auth.token;

  if (!headerToken) {
    return next(new Error("Authentication error"));
  }

  if (headerToken && headerToken.split(" ")[0] !== "Bearer") {
    next(new Error("Invalid token"));
  }

  const token = headerToken.split(" ")[1];
  if (token === process.env.STATIC_TOKEN) {
    console.log("Socket Middleware successful for GazeAI");
    socket.uid = "gaze-ai-bot";
    socket.email = "<EMAIL>"
    socket.role = "interviewer";
    next();
    return;
  } else {
    firebase
      .auth()
      .verifyIdToken(token)
      .then((decodedToken) => {
        const uid = decodedToken.uid;
        socket.uid = uid;
        socket.email = decodedToken.email;
        socket.role =
          decodedToken.interviewer || uid === botId
            ? "interviewer"
            : "interviewee";
        next();
      })
      .catch(() => next(new Error("Could not authorize")));
  }
}
module.exports = { authMiddleware, authMiddlewareSocket };
