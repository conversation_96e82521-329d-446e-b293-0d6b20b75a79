const callDataLayer = require("../utils/callDataLayer");

const authorisationMiddleware = async (req, res, next) => {
    try {
        if (req.method !== "POST") {
            return next();
        }

        if (
            req.path === "/signup" ||
            req.path === "/techyrr/check-company" ||
            req.path.startsWith("/internal") ||
            req.path.startsWith("/techhyrr-admin") ||
            req.path.startsWith("/assignment-repo")
        ) {
            return next();
        }

        const adminId = res.locals.uid;
        const companyId = res.locals.companyId;

        if (!adminId || !companyId) {
            return res.status(403).send({ message: "Admin ID or Company ID missing." });
        }

        const permissionsResponse = await callDataLayer(
            `/admin/permissions/${adminId}`,
            companyId
        );

        if (!permissionsResponse) {
            return res.status(500).send({ message: "Failed to retrieve permissions." });
        }

        const allowedApis = (permissionsResponse.allowedApis || [])
            .map((url) => url.trim().replace(/[`'"]/g, ""));

        if (req.path === "/getPagesForAdmin" && allowedApis.length > 0) {
            return next();
        }

        if (allowedApis.length === 0) {
            console.warn(`Admin ${adminId} has unrestricted API access.`);
            return next();
        }

        const currentApiPath = req.path.toLowerCase().trim();
        
        const isAuthorized = allowedApis.some((url) => url.toLowerCase() === currentApiPath);

        if (!isAuthorized) {
            console.warn(`Access denied: Admin ${adminId} tried to access ${currentApiPath}`);
            return res.status(403).send({ message: "Access denied to this API." });
        }

        next();
    } catch (error) {
        console.error("Authorization error:", error);
        return res.status(500).send({ message: "Internal server error." });
    }
};

module.exports = authorisationMiddleware;
