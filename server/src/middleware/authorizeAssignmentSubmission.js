const { AuthorizeAssignmentSubmission } = require("neusortlib/models");
const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");

async function authorizeAssignmentSubmission(req, res, next) {
  const { uid, email, companyId } = res.locals;
  let assignmentId = req.params.assignmentId;

  try {
    const result =
      companyId && companyId !== "undefined"
        ? await callDataLayer(
            "/authorizeAssignmentSubmission",
            companyId,
            "POST",
            { uid, email, assignmentId }
          )
        : await prisma.$transaction(async (tx) => {
            let assignmentDetails;

            if (!Number.isNaN(Number.parseInt(assignmentId))) {
              assignmentDetails = await tx.user_assignments.findFirst({
                where: {
                  id: parseInt(assignmentId),
                  deleted: 0,
                },
              });

              if (!assignmentDetails) {
                return {
                  success: false,
                  showData: false,
                  data: "",
                  code: "assignmentNotFound",
                  message:
                    "No assignment found with that ID or alias. Please check your URL.",
                };
              }

              const applicationId = assignmentDetails.applicationid;
              const candidateData = await tx.candidate_form_mapping.findUnique({
                where: {
                  id: applicationId,
                },
                select: {
                  candidateId: true,
                  candidate: true,
                },
              });

              const {
                candidateId,
                candidate: { uid: storedUid, email: storedEmail },
              } = candidateData;

              if (!storedUid && storedEmail === email) {
                await tx.candidate.update({
                  where: { id: candidateId },
                  data: { uid },
                });
              }

              if (storedEmail !== email) {
                return {
                  success: false,
                  showData: false,
                  data: "",
                  code: "unauthorized",
                  message: "You are not authorized to submit this assignment.",
                };
              }

              const submission = await tx.assignment_submissions.findFirst({
                where: { user_assignment_id: assignmentId },
              });

              if (submission) {
                return {
                  success: false,
                  showData: true,
                  data: assignmentDetails,
                  code: "alreadySubmitted",
                  message: "Assignment already submitted.",
                };
              }

              if (new Date(assignmentDetails.deadline) < new Date()) {
                return {
                  success: false,
                  showData: true,
                  data: assignmentDetails,
                  code: "deadlinePassed",
                  message: "Assignment submission deadline has passed.",
                };
              }
            } else {
              assignmentId = assignmentId.toString().toLowerCase();
              assignmentDetails = await tx.generic_assignments.findFirst({
                where: { alias: assignmentId },
              });

              if (!assignmentDetails) {
                return {
                  success: false,
                  showData: false,
                  data: "",
                  code: "assignmentNotFound",
                  message:
                    "No assignment found with that ID or alias. Please check your URL.",
                };
              }

              const submission = await tx.assignment_submissions.findFirst({
                where: { user_assignment_id: assignmentId, uid },
              });

              if (submission) {
                return {
                  success: false,
                  showData: true,
                  data: assignmentDetails,
                  code: "alreadySubmitted",
                  message: "Assignment already submitted.",
                };
              }
            }

            return { success: true, showData: true, data: assignmentDetails };
          });

    if (result.success) {
      const data = new AuthorizeAssignmentSubmission(result.data);
      res.locals.assignmentDetails = data.returnAuthorizeAssignmentSubmission();
      next();
    } else {
      if (result.showData) {
        return res
          .status(400)
          .send({
            success: false,
            code: result.code,
            data: result.data,
            message: result.message,
          });
      }
      throw new Error("fail");
    }
  } catch (err) {
    console.error(
      `Error occurred while authenticating assignment submission request for assignment ID or alias: ${assignmentId}`,
      "\nError: ",
      err
    );
    res.status(400).send({
      success: false,
      error:
        "Something went wrong. Could not fetch assignment submission details.",
    });
  }
}

module.exports = { authorizeAssignmentSubmission };
