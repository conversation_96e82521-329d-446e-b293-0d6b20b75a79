const app = require("firebase-admin");

async function techyrrAdminMiddleware(req, res, next) {
  const headerToken = req.headers.authorization;
  if (!headerToken) {
    return res.status(401).send({ message: "Techyrr Admin - No token provided" });
  }
  if (headerToken && headerToken.split(" ")[0] !== "Bearer") {
    return res.status(401).send({ message: "Techyrr Admin - Invalid token" });
  }
  const token = headerToken.split(" ")[1];
  const claims = await app.auth().verifyIdToken(token);
  if (claims.techyrrAdmin === true) {
    console.log("Techyrr Admin - Middleware successful");
    next();
  } else {
    res.status(403).send("Techyrr Admin - Access denied");
  }
}

module.exports = techyrrAdminMiddleware;
