const roomStates = {
  introduction: 0,
  english_conversation: 1,
  coding_conversation: 2,
  end: 3,
  dsa_coding_conversation: 4,
};

const botStates = {
  IDLE: "idle",
  THINKING: "thinking...",
  LISTENING: "listening...",
};

const modes = {
  text: "text",
  voice: "voice",
};

const botId = "eval-ai-bot";

const humanInterviewers = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

const nonRepeatingCases = [
  "proceed",
  "out_of_context",
  "irrelevant",
  "concept_explained",
  "acknowledge",
  "complete_solution",
];

const tutorialDuration = 10;

const redisRoomPrefix = "evalai-room:";

module.exports = {
  roomStates,
  botId,
  humanInterviewers,
  nonRepeatingCases,
  botStates,
  modes,
  tutorialDuration,
  redisRoomPrefix,
};
