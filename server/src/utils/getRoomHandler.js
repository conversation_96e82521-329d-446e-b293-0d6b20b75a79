const {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} = require("../models/roomHandler");
const { roomStates } = require("../constants/roomConstants");

const getRoomHandler = (roomId, room_state) => {
  switch (room_state) {
    case roomStates.introduction:
      return new <PERSON><PERSON>and<PERSON>(roomId);

    case roomStates.english_conversation:
      return new EnglishRoomHandler(roomId);

    case roomStates.coding_conversation:
    case roomStates.dsa_coding_conversation:
      return new CodeRoomHandler(roomId);

    default:
      return null;
  }
};

module.exports = { getRoomHandler };
