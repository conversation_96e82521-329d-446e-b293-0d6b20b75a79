const fs = require("fs");
const path = require("path");
const { defaultAssignmentsDir, adminViewAssignmentDir } = require("../constants/fileStructureConstants");

// Function to recursively read files
function readFilesRecursively(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach((file) => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      readFilesRecursively(filePath, fileList);
    } else if (!filePath.startsWith(".")) {
      fileList.push(filePath);
    }
  });
  return fileList;
}

function getTargetDirForUid(uid, github_link, is_admin) {
  const repoName = github_link?.split("/").pop().replace(".git", "");
  return (is_admin ? adminViewAssignmentDir : defaultAssignmentsDir) + uid + "/" + repoName;
}

function getRepoPath(uid, data, is_admin) {
  return is_admin ? adminViewAssignmentDir : defaultAssignmentsDir + uid;
}

module.exports = { readFilesRecursively, getTargetDirForUid, getRepoPath };
