const streamAIAudio = require("./streamAIAudio");
const sendMessage = require("./udp");
const queue = require("./queue");
const fetchWithRetry = require("./fetchWithRetry");
const prompts = require("./prompts");
const parseJsonWithErrorHandling = require("./parseJSON");
const { insertAssignmentIntoRabbitMQ } = require("./insertIntoRabbitMQ");
const getGitDiff = require("./getGitDiff");
const getJsonFromPromptWithRetries = require("./getResultFromPromptWithRetries");
const updateFiles = require("./updateFiles");
const createMediasoupWorker = require("./createMediasoupWorker");
const copyFile = require("./copyFile");

module.exports = {
  copyFile,
  updateFiles,
  getGitDiff,
  streamAIAudio,
  sendMessage,
  fetchWithRetry, 
  queue,
  prompts,
  parseJsonWithErrorHandling,
  getJsonFromPromptWithRetries,
  insertAssignmentIntoRabbitMQ,
  createMediasoupWorker,
};
