const parseJsonWithErrorHandling = require("./parseJSON");

const getJsonFromPromptWithRetries = async (
  GENAI,
  prompt,
  type = "getResultFromPrompt",
  retries = 5,
  roomId,
  initialChar = "{",
  endChar = "}"
) => {
  if (!prompt) {
    console.log("Prompt is empty");
    return null;
  }

  let response, jsonData;
  while (retries--) {
    try {
      if (type === "getResultFromPrompt") {
        response = await GENAI.getResultFromPrompt(roomId, prompt, true);
      } else if (type === "getCompletion") {
        response = await GENAI.getCompletion(roomId, prompt, true);
      }
      jsonData = parseJsonWithErrorHandling(
        response.slice(response.indexOf(initialChar), response.lastIndexOf(endChar) + 1)
      );
      return jsonData;
    } catch (error) {
      console.log("Error while generating data - ", error, "\nNumber of retries left:", retries);
    }
  }
};

module.exports = getJsonFromPromptWithRetries;
