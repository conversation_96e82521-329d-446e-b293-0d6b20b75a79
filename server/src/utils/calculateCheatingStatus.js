function calculateCheatingStatus(candidates) {
    return candidates.map(candidate => {
        const { cheatingTimestamps } = candidate;

        if (cheatingTimestamps === null) {
            return {
                ...candidate,
                derivedAttributes: { cheatingStatus: null }
            };
        }

        if (cheatingTimestamps.length === 0) {
            return {
                ...candidate,
                derivedAttributes: { cheatingStatus: "not cheated" }
            };
        }

        let totalCheatingTime = 0;
        const stack = [];

        cheatingTimestamps.forEach(event => {
            if (event.type === "start") {
                stack.push(new Date(event.timestamp));
            } else if (event.type === "end" && stack.length > 0) {
                const startTime = stack.pop();
                const endTime = new Date(event.timestamp);
                totalCheatingTime += (endTime - startTime) / 1000;
            }
        });

        const cheatingStatus = totalCheatingTime > 600 ? "cheated" : "not cheated";

        return {
            ...candidate,
            derivedAttributes: { cheatingStatus }
        };
    });
}

module.exports = {
    calculateCheatingStatus
};
