const sdk = require("microsoft-cognitiveservices-speech-sdk");
const { ROOMS } = require("../cache/index.js");
const executeAfterDelay = require("./executeAfterDelay");
const escapeXml = require("../utils/escapeXml.js");

const speechConfig = sdk.SpeechConfig.fromSubscription(
  process.env.AZURE_SPEECH_KEY,
  process.env.AZURE_SPEECH_REGION
);

speechConfig.speechSynthesisOutputFormat =
  sdk.SpeechSynthesisOutputFormat.Webm16Khz16BitMonoOpus;
// speechConfig.speechSynthesisLanguage = "en-US";
// speechConfig.speechSynthesisVoiceName = "en-US-AndrewNeural";

const streamAIAudio = (roomId = "", text = "") => {
  return new Promise((resolve, reject) => {
    var synthesizer = new sdk.SpeechSynthesizer(speechConfig, null);

    const escapedText = escapeXml(text);

    synthesizer.synthesisStarted = function (s, e) {
      console.log("synthesis started", roomId, escapedText);
    };

    //ssml docs: https://learn.microsoft.com/en-gb/azure/ai-services/speech-service/speech-synthesis-markup-structure
    synthesizer.speakSsmlAsync(
      `<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis"
          xmlns:mstts="http://www.w3.org/2001/mstts"
          xml:lang="en-US">
        <voice name="en-US-AndrewNeural">
          <prosody rate="0.8">${escapedText}</prosody>
        </voice>
      </speak>
      `,
      async function (result) {
        if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
          try {
            console.log("synthesis finished.", roomId);

            const duration = result.audioDuration * 0.0001;
            console.log("audio duration", duration);

            await ROOMS.pushNewAudioToBot(roomId, result.audioData);

            await ROOMS.playBotAudio(roomId);

            await executeAfterDelay(duration);
            resolve();
          } catch (e) {
            console.error("Error in playing audio", e);
            reject();
          }
        } else {
          console.error(
            "Speech synthesis canceled, " +
              result.errorDetails +
              "\nDid you set the speech resource key and region values?"
          );
        }
        synthesizer.close();
        synthesizer = null;
        reject();
      },
      function (err) {
        console.error("Error synthesizing: " + err);
        synthesizer.close();
        synthesizer = null;
        reject();
      }
    );
  });
};

module.exports = streamAIAudio;
