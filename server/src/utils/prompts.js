const { QUESTIONS, SKILLS, DSA_SKILLS } = require("../cache/index.js");

function OpenAIEnglishPrompt(userData, questions) {
  const { name } = userData;
  const allQuestions = QUESTIONS.getQuestions();
  const question =
    allQuestions[Math.floor(Math.random() * allQuestions.length)];

  return `Play the role of an Interviewer. Conduct an interview with the interviewee named "${name}" and you have to conduct a interview to test for basic english language. Whatever you ask will be converted to voice, so ask questions which can be understood over voice call.

  ========================================================================================
    
  Instructions:-
  
  1. Start the interview by introducing yourself and asking the interviewee to introduce themselves. After getting to know the interviewee begin the interview by asking:- "${question}". Wait for the interviewee to respond after asking each question and then proceed to the next question.
  
  2. The Interviewee's response would look like this:-
      
      Interviewee's response: <Interviewee's answer>

  ========================================================================================
  
  Structure your response in the following format:-
  
  {
    "response": "<interviewer's response>"
  }

  `;
}

function OpenAICodingPrompt(userData) {
  return ``;
}

function VertexAIEnglishPrompt(userData) {
  // const { name } = userData;
  const allQuestions = QUESTIONS.getQuestions();
  const question =
    allQuestions[Math.floor(Math.random() * allQuestions.length)];

  return `The following is an Interview between the Interviewer and the Interviewee for testing the English speaking skills of the Interviewee, your task is to generate the next Interviewer response. 

  Please follow these guidelines for the interview:-

  > The messages quoted between -- -- indicates an event that the Interviewer has to take into consideration for the next response.
  > You may ask these questions:- "${question}". Wait for the interviewee to respond after asking each question and then proceed to the next question.
  > Do not include '<Interviewer>' in your response. The response should be in the form of a question or a statement.
  > Kindly refuse to answer any questions that are not related to the interview.
  > Refrain from asking similar types of questions.

  RESPONSE FORMAT:

  {
    "response": "<interviewer's response>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.

  Conversation:
  
  --Interview Started--
  `;
}

function VertexAICodingPrompt(languages) {
  return `The following is an Interview between the Interviewer and the Interviewee for the role of developer who needs to be skilled in ${languages}, your task is to generate the next Interviewer response.
  
  Please follow these guidelines for the interview:-

  > The messages quoted between -- -- indicates an event that the Interviewer has to take into consideration for the next response.
  > Do not include '<Interviewer>' in your response. The response should be in the form of a question or a statement.
  > Kindly refuse to answer any questions that are not related to the interview.
  > Refrain from asking similar types of questions.

  RESPONSE FORMAT:

  {
    "response": "<interviewer's response>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.

  Conversation:
  
  --Interview Started--
  `;
}

const introductionPrompt =
  "Hi my name is Eval and I will be taking your interview today. Before we begin, Can you please introduce yourself?";

function introductionScorePrompt(ans) {
  //role: system
  return `You are Eval- An AI bot that takes interview. You asked user to introduce themselves.
  If the interviewee has told you his name, you should rate his response above 5.

  Question asked: "${introductionPrompt}"
  
  Rate the interviewee's response on a scale of 1-10. 1 being the lowest and 10 being the highest.

  Interviewee's response: "${ans}"
  
  CASE 1: If the rating is less than 5, again ask the interviewer to introduce themself properly (do not include your own introduction again),
  
  RESPONSE FORMAT:

  {
    "rating": <your rating>,
    "askAgain": true,
    "response": "<ask interviewee to reintroduce themselves>"
  }
  
  CASE 2: If the rating is more than 5,
  
  RESPONSE FORMAT:

  {
    "rating": <your rating>,
    "askAgain": false
  }
  
  Note: Do not respond with anything else apart from the given RESPONSE FORMAT.
  `;
}

// X X NOT BEING USED X X
function nextCodingQuestionPrompt(languages, question, prevCorrect) {
  //can be made static
  return `You are EvalAI - An AI bot that takes interview.
  
  You are an Interviewer taking the interview for the role of developer who needs to be skilled in ${languages}.

  The interviewee has ${
    prevCorrect ? "correctly answered" : "skipped"
  } the previous question. Include this information in your response and ask the next question.

  "${question}"

  Do not explain the question. Just ask the question and follow up asking if he has any doubts or questions, he can ask you.
  
  RESPONSE FORMAT:

  {
    "response": "<your response>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.
`;
}

// X X X NOT BEING USED X X X
function repeatLastCodingQuestionPrompt(languages, question) {
  return `You are EvalAI - An AI bot that takes interview.
      
      You are an Interviewer taking the interview for the role of developer who needs to be skilled in ${languages}
    
      The interviewee got disconnected before answering the last question. Let the interviewee know that you acknowledge this and continue the interview by repeating the last question.
      
      Question: "${question}"
      
      RESPONSE FORMAT:

      {
        "response": "<response>"
      }

      Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.
      `;
}

const hintPrompt = (
  languages,
  query,
  question,
  previousHints,
  codeChanges,
  casesToExclude,
  example,
  timeRemaining,
  remainingRecommendedQuestionsCount,
  totalRecommendedQuestionsCount
) => {
  const previousHintsString = previousHints
    .slice(-3)
    .reduce(
      (acc, hint) =>
        acc + `Interviewee: ${hint.query}\nInterviewer: ${hint.hint}` + "\n",
      "\n"
    );

  const allCases = {
    meta_question:
      "If the interviewee is asking a question about the interview question itself, clarify the question for them.",

    // ...(!exampleGiven &&
    //   example.length && {
    //     example: `If the interviewee did not understand the question, using below example explain in two sentences how it uses the feature required by the question.: ${example}`,
    //   }),

    concept:
      "If the interviewee is asking about a specific concept related to technology, briefly explain the concept without giving away the complete solution.",

    acknowledge_true:
      "If the interviewee's response is correct, acknowledge it with a short affirmation.",

    acknowledge_false:
      "If the interviewee's response is incorrect, acknowledge it and provide a brief correction.",

    hint: `If the interviewee did not understand the question, or is asking for a hint, provide a hint which is relevant to the question.`,

    code_location:
      "If the interviewee is asking where they should code this, tell them to code in the editor provided to them.",

    skip_question:
      "If the interviewee is asking to skip the question, tell them to press the skip button.",

    irrelevant:
      "If the interviewee is saying something out of context, tell them to stick to the question.",

    repeat:
      "If the interviewee is asking to repeat what you just said, repeat your last response.",

    proceed:
      "If the interviewee has understood the question or is willing to try to code, just tell him to proceed with the code.",
  };

  const cases = Object.entries(allCases)
    .filter(([key]) => !casesToExclude.includes(key))
    .map(([key, value]) => `CASE "${key}": "${value}"`)
    .join("\n");

  return `You are an Interviewer taking the interview for the role of a developer developer who needs to be skilled in ${languages} and the context of the interview is given below. 

  Some things to consider:

  - The interview is being conducted in an environment where the interviewee is solving a coding problem, the environment only includes a text editor, a file tree and no terminal. The interviewee can't run the code, so in case the interviewee asks where to run the code, tell them they can use the "PUBLISH" button ( they won't actually be able to run the code, or see the output but upon publishing the changes its on you to judge them ).
  - The interviewee has to solve three questions and if they chose to skip a question they cannot go back to the previous question
  - Time remaining in the interview is: "${timeRemaining} minutes."
  - Questions remaining in the interview are: "${remainingRecommendedQuestionsCount} after this question out of ${totalRecommendedQuestionsCount} questions." and the interviewee is at the question number ${
    totalRecommendedQuestionsCount - remainingRecommendedQuestionsCount
  }.

  Note: do not provide the complete solution or the approach to the problem. As an interviewer, dont say the same thing more than once unless you are asked to repeat.

  The question asked was: ${question}

  ${previousHints.length ? `Previous Conversation: ${previousHintsString}` : ""}

  The Git Diff of the changes made by the interviewee till now:

  ${codeChanges}
  

  Current Interviewee's Statement: ${query} 

  Using interviewee's statement, identify the correct case and respond as an interviewer would respond.
  ${cases}

  Review all the cases and pick the most appropriate case according to the context.

  RESPONSE FORMAT:

  {
    "response": "<interviewer's response>",
    "case": "<case_name>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.
  `;
};

const giveHintOnSubmissionPrompt = (
  languages,
  question,
  previousHints,
  codingAnswer
) => {
  const previousHintsString = previousHints.slice(-3).reduce((acc, hint) => {
    let result = acc;
    if (hint.query && hint.query.length > 0) {
      result += `Interviewee: ${hint.query}\n`;
    }
    if (hint.hint && hint.hint.length > 0) {
      result += `Interviewer: ${hint.hint}\n`;
    }
    return result;
  }, "");

  return `You are an Interviewer taking the interview for the role of developer who needs to be skilled in ${languages}.

  Provide a hint for this question.
  Note: 
  -> Do not provide the complete solution or the approach to the problem.
  -> If theres a mistake in the syntax, ask the interviewee to use the "Search Syntax" button.

  ${previousHints.length ? `Previous Conversation: ${previousHintsString}` : ""}

  Question asked to the interviewee: ${question}

  Git diff of solution given by interviewee: ${codingAnswer}

  RESPONSE FORMAT:

  {
    "response": "<response>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.
  `;
};

const askForHintPrompt = (
  languages,
  timeIntervalInMinutes,
  question,
  previousDiff,
  currentDiff,
  previousHints
) => {
  const previousHintsString = previousHints.slice(-3).reduce((acc, hint) => {
    let result = acc;
    if (hint.query && hint.query.length > 0) {
      result += `Interviewee: ${hint.query}\n`;
    }
    if (hint.hint && hint.hint.length > 0) {
      result += `Interviewer: ${hint.hint}\n`;
    }
    return result;
  }, "");

  const allCases = {
    right_track:
      "If the interviewee's response is correct, acknowledge it with a short affirmation.",

    syntax_error:
      "If the interviewee's response is incorrect, acknowledge it and provide a brief correction without giving any code.",

    wrong_track: `If the interviewee did not understand the question, or is asking for a hint, provide a hint which is relevant to the question.`,

    correct_code:
      "If the interviewee is asking where they should code this, tell them to code in the editor provided to them.",

    no_progress:
      "If the interviewee is asking to skip the question, tell them to press the skip button.",
  };

  const cases = Object.entries(allCases)
    .map(([key, value]) => `CASE "${key}": "${value}"`)
    .join("\n");

  return `You are an Interviewer taking the interview for the role of developer who needs to be skilled in ${languages}.

  The Interviewee is currently solving the question you asked.

  Question asked to the interviewee: ${question}

  ------------------------------------------------

  ${previousHints.length ? `Previous Conversation: ${previousHintsString}` : ""}

  ------------------------------------------------

  The progress of the interviewee is as follows:

  Changes made by the interviewee  ${timeIntervalInMinutes} minutes ago:

  ${previousDiff}

  Current changes made by the interviewee:

  ${currentDiff}

  ------------------------------------------------

  Based on the given information, give the interviewee a brief explanation based on what they have done so far, whether they are on the right track or not.
 
  Do not repeat same things again and again.

  identify the correct case and respond as an interviewer would respond.
  ${cases}

  Review all the cases and pick the most appropriate case according to the context.

  RESPONSE FORMAT:

  {
    "response": "<interviewer's response>",
    "case": "<case_name>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.
  `;
};

const taskEvaluationPrompt = (languages, question, diff, skills) => {
  return `# Developer Interview Evaluation

  ## Background
  The candidate is being interviewed for a role requiring expertise in ${languages}. A starter code is already provided in the interview.

  ## Interview Question
  \`\`\`
  ${question}
  \`\`\`

  ## Candidate's Response
  Below is the git diff showing the changes the candidate made to solve the problem:

  ### !!IMPORTANT!!
    - Only the changes marked with \"-\" for deletions and \"+\" for additions are made to the code, rest of the boilerplate code was already provided in the starter code
    - Make sure the candidate make changes to all the relevant files mentioned in the question.

  \`\`\`diff
  ${diff}
  \`\`\`

  ## Skills to Evaluate
  ${skills
      ?.map((skill, index) => `${index + 1}. ${JSON.stringify(skill)}`)
      .join("\n")}

  ## Your Evaluation Tasks
  1. Evaluate each of the listed skills that can be assessed from the candidate's changes. The "Overall" skill (id: 1) must always be evaluated.
  2. For each skill, critically rate the candidate's performance on the skill based on the following criteria: 
    - For partial solutions, provide a rating between 1-5.
    - For good/complete solutions, provide a rating between 6-10.
  3. If the diff is empty or only contains trivial changes (like comment modifications), give a rating of 1.

  ## Response Format Requirements
  Respond strictly with a valid JSON object following this structure:

  \`\`\`json
  {
    "ratings": [
      {
        "id": <skill_id>,
        "skill": "<skill_name>",
        "rating_value": <integer_between_1_and_10>,
        "rating_reason": "<detailed_explanation_for_rating>",
        "is_custom_skill": <boolean>
      },
      ...
    ],
    "hint": "<hint to the interviewee without revealing the answer>"
  }
  \`\`\`

  Only provide the JSON response with no additional text before or after.
`;
};

const hintDSAPrompt = (
  query,
  question,
  plantUMLDiagram,
  previousHints,
  codeChanges,
  casesToExclude,
  expectedComplexity,
  constraints,
  timeRemaining,
  remainingRecommendedQuestionsCount,
  totalRecommendedQuestionsCount,
  questionNumber
) => {
  const previousHintsString = previousHints.slice(-3).reduce((acc, hint) => acc + `Interviewee: ${hint.query}\nInterviewer: ${hint.hint}` + "\n", "\n");

  const commonCases = {
    meta_question: "If the interviewee is asking a question about the problem statement itself, clarify the question for them.",
    acknowledge_true: "If the interviewee's response is correct, acknowledge it with a short affirmation.",
    acknowledge_false: "If the interviewee's response is incorrect, acknowledge it and provide a brief correction.",
    code_location: "If the interviewee is asking where they should implement this, tell them to code in the editor provided to them.",
    skip_question: "If the interviewee is asking to skip the question, inform them that skipping is not possible in DSA interviews as questions build upon each other. Encourage them to attempt the current question, even with a basic solution.",
    irrelevant: "If the interviewee is saying something out of context, tell them to stick to the problem.",
    repeat: "If the interviewee is asking to repeat what you just said, repeat your last response.",
    proceed: "If the interviewee has understood the problem or is willing to try to implement, just tell them to proceed with the implementation."
  };

  const question1SpecificCases = {
    concept: "If the interviewee is asking about a general programming concept, briefly explain the concept without revealing any specific data structure or implementation approach.",
    hint: "If the interviewee is asking for a hint, encourage them to carefully examine the diagram relationships and think about how elements connect to each other. Focus on the structure's properties without naming specific data structures."
  };

  const question23SpecificCases = {
    concept: "If the interviewee is asking about a general programming concept, briefly explain the concept without revealing specific optimization techniques.",
    hint: "If the interviewee is asking for a hint, guide them to think about efficiency and optimization without revealing specific algorithms or data structures.",
    encourage_simpler: "If the interviewee is struggling with optimal complexity, encourage them to implement a working solution, even if it's not optimal. Emphasize that a correct implementation is more valuable than an incomplete optimal one"
  };

  const allCases = {
    ...commonCases,
    ...(questionNumber === 1 ? question1SpecificCases : question23SpecificCases)
  };

  const cases = Object.entries(allCases)
    .filter(([key]) => !casesToExclude.includes(key))
    .map(([key, value]) => `CASE "${key}": "${value}"`)
    .join("\n");

  const complexitySection =
    questionNumber === 1
      ? ""
      : `Expected Time Complexity: ${expectedComplexity.time}
  Expected Space Complexity: ${expectedComplexity.space}
  Constraints: ${constraints.join(", ")}`;

  return `You are an Interviewer conducting a Data Structures and Algorithms interview. The context of the interview is given below.

  **Interview Environment:**
  - The interviewee solves DSA problems using a text editor (no terminal)
  - They cannot run code; use "PUBLISH" button to submit solutions for evaluation
  - 3 questions total: Question 1 (data structure implementation), Questions 2-3 (optimization extensions)
  - Time remaining: "${timeRemaining} minutes"
  - Questions remaining: "${remainingRecommendedQuestionsCount} after this question out of ${totalRecommendedQuestionsCount}"
  - Current question: ${questionNumber}

  ${complexitySection}

  **CRITICAL GUIDELINES:**
  - NEVER reveal specific data structures, algorithms, or implementation details
  ${questionNumber === 1 ?
    `- Guide candidates to analyze the diagram structure and relationships without naming specific data structures
      - Focus on the visual patterns and connections shown in the diagram
      - Encourage understanding of the problem requirements through diagram analysis`
      : `
      - Guide towards efficiency thinking without revealing specific optimization techniques
      - If candidate struggles with optimal complexity, encourage them to implement a working solution, so candidate can move to next question
      - A correct implementation with suboptimal complexity is acceptable for progression to next question`
    }

  Note: Do not provide the complete solution or implementation approach. As an interviewer, don't repeat the same information unless asked to repeat.

  **Problem Statement:** ${question}

  **Diagram (PlantUML):**
  \`\`\`
  ${plantUMLDiagram}
  \`\`\`

  ${previousHints.length ? `Previous Conversation: ${previousHintsString}` : ""}

  **Git Diff of Interviewee's Changes:**
  ${codeChanges}

  **Current Interviewee's Statement:** ${query} 

  Using the interviewee's statement, identify the correct case and respond as an interviewer would respond.
  ${cases}

  Review all cases and pick the most appropriate case according to the context.

  **RESPONSE FORMAT:**

  {
    "response": "<interviewer's response>",
    "case": "<case_name>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.
  `;
};

const giveHintOnSubmissionDSAPrompt = (
  question,
  plantUMLDiagram,
  previousHints,
  codingAnswer,
  expectedComplexity,
  constraints,
  questionNumber
) => {
  const previousHintsString = previousHints.slice(-3).reduce((acc, hint) => {
    let result = acc;
    if (hint.query && hint.query.length > 0) {
      result += `Interviewee: ${hint.query}\n`;
    }
    if (hint.hint && hint.hint.length > 0) {
      result += `Interviewer: ${hint.hint}\n`;
    }
    return result;
  }, "");

  const complexityNote = questionNumber === 1
    ? "Focus on correct implementation matching the diagram structure. Do not reveal specific data structures or implementation details."
    : "Guide toward efficiency thinking without revealing specific optimization techniques or data structures.";

  const complexitySection =
    questionNumber === 1
      ? ""
      : `Expected Time Complexity: ${expectedComplexity.time}
    Expected Space Complexity: ${expectedComplexity.space}
    Constraints: ${constraints.join(", ")}`;

  return `You are an Interviewer conducting a Data Structures and Algorithms interview.

  Provide a hint for this DSA problem based on the submitted solution.

  **CRITICAL GUIDELINES:**
  - NEVER reveal specific data structures, algorithms, or implementation details
  - Guide the candidate's thinking process rather than providing solutions
  ${questionNumber === 1 ?
    `- Guide candidates to analyze the diagram structure and relationships without naming specific data structures
      - Focus on the visual patterns and connections shown in the diagram
      - Encourage understanding of the problem requirements through diagram analysis`
      : `
      - Guide towards efficiency thinking without revealing specific optimization techniques
      - If candidate struggles with optimal complexity, encourage them to implement a working solution, so candidate can move to next question
      - A correct implementation with suboptimal complexity is acceptable for progression to next question`
    }
  
  **Note:**
  - Avoid providing complete solutions or implementation approaches
  - If there are logical errors, guide them through reasoning without giving code
  - ${complexityNote}

  ${previousHints.length ? `Previous Conversation: ${previousHintsString}` : ""}

  **Problem Statement:** ${question}

  **Diagram (PlantUML):**
  \`\`\`
  ${plantUMLDiagram}
  \`\`\`

  ${complexitySection}

  **Git diff of submitted solution:**
  ${codingAnswer}

  **RESPONSE FORMAT:**

  {
    "response": "<response>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.
  `;
};

const askForHintDSAPrompt = (
  timeIntervalInMinutes,
  question,
  plantUMLDiagram,
  previousDiff,
  currentDiff,
  previousHints,
  expectedComplexity,
  constraints,
  questionNumber
) => {
  const previousHintsString = previousHints.slice(-3).reduce((acc, hint) => {
    let result = acc;
    if (hint.query && hint.query.length > 0) {
      result += `Interviewee: ${hint.query}\n`;
    }
    if (hint.hint && hint.hint.length > 0) {
      result += `Interviewer: ${hint.hint}\n`;
    }
    return result;
  }, "");

  const commonCases = {
    right_track: "If the interviewee's implementation approach is correct, acknowledge it with a short affirmation.",
    implementation_error: "If there's an error in the implementation, acknowledge it and provide a brief correction without giving code.",
    no_progress: "If there's no meaningful progress, encourage them to start with basic structure implementation."
  };

  const question1SpecificCases = {
    wrong_approach: "If the interviewee is not following the diagram structure, guide them to examine the diagram more carefully and understand the relationships between elements without revealing specific implementation details.",
    structure_mismatch: "If the implementation doesn't match the diagram structure, encourage them to trace through the diagram patterns and understand how elements should connect without giving the solution."
  };

  const question23SpecificCases = {
    wrong_approach: "If the interviewee is using an inefficient approach, guide them to think about optimization patterns and efficiency considerations without revealing specific algorithms or data structures.",
    complexity_issue: "If the implementation doesn't meet expected complexity requirements, encourage thinking about more efficient approaches while emphasizing that a working solution is better than an incomplete optimal one."
  };

  const allCases = {
    ...commonCases,
    ...(questionNumber === 1 ? question1SpecificCases : question23SpecificCases)
  };

  const cases = Object.entries(allCases)
    .map(([key, value]) => `CASE "${key}": "${value}"`)
    .join("\n");

  const complexitySection =
    questionNumber === 1
      ? "Focus: Correct implementation matching the diagram structure."
      : `Expected Time Complexity: ${expectedComplexity.time}
      Expected Space Complexity: ${expectedComplexity.space}
      Constraints: ${constraints.join(", ")}`;

  return `You are an Interviewer conducting a Data Structures and Algorithms interview.

  The interviewee is currently solving the DSA problem you presented.

  CRITICAL GUIDELINES:
  - NEVER reveal or suggest specific data structures, algorithms, or implementation details
  ${questionNumber === 1 ?
    `- Guide candidates to analyze the diagram structure and relationships without naming specific data structures
      - Focus on the visual patterns and connections shown in the diagram
      - Encourage understanding of the problem requirements through diagram analysis`
      : `
      - Guide towards efficiency thinking without revealing specific optimization techniques
      - If candidate struggles with optimal complexity, encourage them to implement a working solution, so candidate can move to next question
      - A correct implementation with suboptimal complexity is acceptable for progression to next question`
    }

  Problem Statement: ${question}

  Diagram (PlantUML):
  \`\`\`
  ${plantUMLDiagram}
  \`\`\`

  ${complexitySection}

  ------------------------------------------------

  ${previousHints.length ? `Previous Conversation: ${previousHintsString}` : ""}

  ------------------------------------------------

  The progress of the interviewee is as follows:

  Changes made ${timeIntervalInMinutes} minutes ago:
  ${previousDiff}

  Current changes:
  ${currentDiff}

  ------------------------------------------------

  Based on the given information, provide brief feedback on their implementation progress and whether they're on the right track.
 
  Do not repeat the same feedback.

  Identify the correct case and respond as an interviewer would respond.
  ${cases}

  Review all cases and pick the most appropriate case according to the context.

  RESPONSE FORMAT:

  {
    "response": "<interviewer's response>",
    "case": "<case_name>"
  }

  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation of your response.
  `;
};

const taskEvaluationDSAPrompt = (
  question,
  plantUMLDiagram,
  diff,
  expectedComplexity,
  constraints,
  questionNumber
) => {
  const skillsToEvaluate = questionNumber === 1 ? DSA_SKILLS.getQuestion1DSASkills() : DSA_SKILLS.getQuestion2And3DSASkills();

  const complexityEvaluation =
    questionNumber === 1
      ? `
      1. **Data Structure Implementation** (Special Criteria):
      - Correct working solution (7-10): Any implementation that correctly solves the problem and matches the diagram structure, regardless of complexity or optimization
      - Partially working solution (4-6): Implementation has the right approach but contains bugs or incomplete functionality
      - Non-functional or incorrect (1-3): Implementation doesn't work or completely misses the diagram structure
      
      2. **Other Skills** (Problem Solving, Edge Cases, Code Readability):
      - Excellent (8-10): Demonstrates strong understanding with clean, well-structured code and good edge case handling
      - Good (6-7): Solid approach with minor issues in structure or edge case coverage
      - Fair (4-5): Basic understanding but lacking in organization or thoroughness
      - Poor (1-3): Limited understanding or poorly structured approach`
      : `
      1. **Data Structure Implementation** (Special Criteria):
      - Correct working solution (7-10): Any implementation that correctly solves the problem, regardless of complexity or optimization (even brute force solutions)
      - Partially working solution (4-6): Implementation has the right approach but contains bugs or incomplete functionality
      - Non-functional or incorrect (1-3): Implementation doesn't work or completely misses the problem requirements

      2. **Complexity-based Skills** (Time/Space Complexity Analysis, Algorithm Design, Code Optimization):
      - Optimal complexity achieved (8-10): Time and space complexity meet or exceed expected requirements with clean implementation
      - Suboptimal but functional (4-7): Working solution with higher than expected complexity but correct logic
      - Poor complexity or non-functional (1-3): Significantly inefficient approach or implementation doesn't work correctly
      
      3. **Other Skills** (Problem Solving, Edge Cases, Code Readability):
      - Excellent (8-10): Demonstrates strong understanding with clean, optimized code and comprehensive edge case handling
      - Good (6-7): Solid approach with minor optimization issues or edge case gaps
      - Fair (4-5): Basic understanding but lacking in optimization or thoroughness
      - Poor (1-3): Limited understanding or poorly structured approach`;

  const complexitySection =
    questionNumber === 1
      ? "**Note**: Focus on correct data structure implementation matching the diagram."
      : `## Expected Complexity Requirements
      - **Expected Time Complexity**: ${expectedComplexity.time}
      - **Expected Space Complexity**: ${expectedComplexity.space}
      - **Constraints**: ${constraints.join(", ")}`;

  const evaluationFocus = questionNumber === 1 ? "- Focus on whether the candidate correctly implemented the data structure as shown in the diagram" : "- Focus on both structural correctness and complexity optimization";

  return `# Data Structures and Algorithms Interview Evaluation

  ## Background
  The candidate is being interviewed for DSA skills.

  ## Problem Statement
  \`\`\`
  ${question}
  \`\`\`

  ## Diagram Reference
  The following PlantUML diagram was provided to the candidate:
  \`\`\`
  ${plantUMLDiagram}
  \`\`\`

  ${complexitySection}

  ## Candidate's Implementation
  Below is the git diff showing the changes the candidate made:

  ### !!IMPORTANT!!
    - Only changes marked with "-" for deletions and "+" for additions are made by the candidate
    - Rest of the boilerplate code was already provided
    - Evaluate if the implementation matches the diagram structure
    ${evaluationFocus}

  \`\`\`diff
  ${diff}
  \`\`\`

  ## Skills to Evaluate
  ${skillsToEvaluate?.map((skill, index) => `${index + 1}. ${JSON.stringify(skill)}`).join("\n")}

  ## Evaluation Criteria
  ${complexityEvaluation}

  ## **Evaluation Guidelines**
  ${questionNumber === 1
    ? `**Implementation:**
  - **Primary Assessment**: Does the candidate correctly implement the data structure as shown in the diagram?
  - **Code Quality**: Is the implementation logical, readable, and well-organized?
  - **Edge Cases**: Are basic error conditions and boundary cases handled appropriately?
  - **Structural Understanding**: Does the candidate demonstrate comprehension of the diagram's relationships?`
    : `**Algorithm Design & Optimization:**
  - **Complexity Requirements**: Does the implementation meet expected time and space complexity?
  - **Algorithm Efficiency**: Is the algorithmic approach efficient and well-designed?
  - **Optimization Techniques**: Are appropriate optimization strategies correctly implemented?
  - **Trade-off Understanding**: Does the solution demonstrate understanding of algorithmic trade-offs?`
    }

  ## Response Format Requirements
  Respond strictly with a valid JSON object:

  \`\`\`json
  {
    "ratings": [
      {
        "id": <skill_id>,
        "skill": "<skill_name>",
        "rating_value": <integer_between_1_and_10>,
        "rating_reason": "<detailed_explanation_for_rating>",
        "is_custom_skill": false
      },
      ...
    ],
    "hint": "<${questionNumber === 1
    ? "constructive feedback focusing on diagram analysis and structural understanding without revealing specific data structures or solutions"
    : "constructive feedback encouraging optimization thinking and efficiency considerations without revealing specific algorithms or implementation details"}>"
  }
  \`\`\`

  Only provide the JSON response with no additional text.
  `;
};

const conversationRatingPrompt = (ratings, conversation, question, answer) => {
  return `The Interview conversation is as follows:
  ${conversation.join("\n")}

  Your tasks:->
  1. Judge the interviewee based on his/her responses against the questions asked. Do take into account the conversation history to get a better understanding of the interviewee's responses.

  The last question asked was: ${question}
  The response to the last question was: ${answer}

  Rate the interviewee based on his/her response to the last question. The ratings to be used are as follows:->

  2. Judging criteria:
  \t${ratings.join("\n\t")}

  Structure your response in the following format: (Use single quotes inside rating_reason to escape double quotes)

  {
    "ratings": [
      {
        "rating_id": <rating_id>,
        "rating_value": <0-10>,
        "rating_reason": "<Full explanation for the rating that is given>"
      }
    ]
  }
  Strictly follow the format and provide the response in the same format. Do not add any additional information or explanation in your response.`;
};

const generateSyntaxPrompt = (language, query) => {
  return `You are an Interviewer taking a technical interview. The interviewee has asked for the syntax in ${language}. Help them without additional explanations or logic. Assume that the interviewee has a clear understanding of the context and is only seeking the precise syntax they requested.

  Interviewee Syntax Request: ${query}

  Structure your response in the following format:

  {
    "response": "<syntax>"
  }
 `;
};

const extractDataFromJD = (jobDescription) => {
  const skills = SKILLS.getSkills().join(", ");

  return `Your task is to extract details from a job description and match only the relevant skills from a predefined list **with exact matching**.

  **Instructions**:
  1. Extract the following details if available:
     - Job Role
     - Company Name
     - Founder Name
     - Location
     - Experience (in numeric form only, e.g., "2" if "2-5 years" is mentioned)
  2. Extract skills **only if they match exactly with the predefined skills list**.
  3. Do not include skills that are related but not exactly listed. 
  4. Variations in names (e.g., "React" instead of "React.js") should be included only if they are universally recognized abbreviations.
  5. Return the output in the specified JSON format.

  **Predefined Skills List** (Standardized Names): ${skills}

  **Output Format**:
  {
    "job_role": "<job role>",
    "skills": ["<standardized_skill1>", "<standardized_skill2>", ...],
    "experience": "<experience>",
    "company_name": "<company name>",
    "founder_name": "<founder name>",
    "location": "<location>"
  }

  **Job Description Provided for Extraction**:

  --Job Description Starts--
  ${jobDescription}
  --Job Description Ends--
  `;
};

const prompts = {
  introductionPrompt,
  introductionScorePrompt,
  OpenAIEnglishPrompt,
  VertexAIEnglishPrompt,
  OpenAICodingPrompt,
  VertexAICodingPrompt,
  nextCodingQuestionPrompt,
  repeatLastCodingQuestionPrompt,
  conversationRatingPrompt,
  userRejoinAssistantLast:
    "While you were asking the last question, the interviewee got disconnected. Let the interviewee know that you acknowledge this and continue the interview by repeating the last question.",

  userRejoinUserLast:
    "The interviewee got disconnected before answering the last question. Let the interviewee know that you acknowledge this and continue the interview by repeating the last question.",

  userRejoinWhileAnswering:
    "The interviewee got disconnected while answering the last question. Let the interviewee know that you acknowledge this and continue the interview by repeating the last question.",

  interviewEnded:
    "The interview has ended. Respond to the interviewee's last answer, don't ask any more questions and provide a farewell message. End the interview.",

  taskEvaluationPrompt,
  taskEvaluationDSAPrompt,
  hintPrompt,
  hintDSAPrompt,
  giveHintOnSubmissionPrompt,
  giveHintOnSubmissionDSAPrompt,
  askForHintPrompt,
  askForHintDSAPrompt,
  generateSyntaxPrompt,
  extractDataFromJD,
};

module.exports = prompts;
