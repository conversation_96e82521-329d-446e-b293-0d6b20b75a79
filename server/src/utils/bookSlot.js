const { moment, toTimeString, toTimeStamp } = require("./moment");
const { BOOKING_SLOT_CACHE, REDIS } = require("../cache");

const callDataLayer = require("./callDataLayer");

const numberOfInterviewsOverlap = process.env.NUMBER_OF_INTERVIEWS_OVERLAP || 1;

async function checkIfCanBook(data, startTimesUTC = []) {
  const {
    submissionId,
    dateUserTz,
    startTimeUserTz,
    endTimeUserTz,
    companyId,
  } = data;

  const bookingSlotCache = await BOOKING_SLOT_CACHE.get(submissionId);

  console.log(
    "bookingslotcache for submissionid",
    submissionId,
    bookingSlotCache
  );

  if (!bookingSlotCache) {
    const error = new Error("The time slot is not valid");
    error.code = "invalid-time-slot";
    throw error;
  }

  const validSlots = JSON.parse(bookingSlotCache.get("validSlots") || "[]");
  const validDates = JSON.parse(bookingSlotCache.get("validDates") || "[]");
  const isCurrentDate = JSON.parse(
    bookingSlotCache.get("isCurrentDate") || false
  );

  if (!validDates.includes(dateUserTz)) {
    const error = new Error("The date is not valid");
    error.code = "invalid-time-slot";
    throw error;
  }

  const slotFound = validSlots.some((slot) => {
    if (slot.from == startTimeUserTz && slot.to == endTimeUserTz) {
      return true;
    }

    return false;
  });

  if (!slotFound) {
    const error = new Error("The time slot is not valid");
    error.code = "invalid-time-slot";
    throw error;
  }

  const currentDateNumberOfInterviewsOverlap = await REDIS.getMaxRoomCount();

  const { canBook } = await callDataLayer(
    `/checkIfCanBook`,
    companyId,
    "POST",
    {
      startTimesUTC: startTimesUTC,
      maxOccupiedCount: isCurrentDate
        ? currentDateNumberOfInterviewsOverlap
        : numberOfInterviewsOverlap,
    }
  );

  console.log("canBook", canBook);

  if (!canBook) {
    const error = new Error("The time slot already exists");
    error.code = "already-booked-slot";
    throw error;
  }
}

function convertSlotsToTimezone(
  slotsData,
  initialTimezone = "UTC",
  targetTimezone
) {
  return slotsData.reduce((acc, slot) => {
    const startConverted = moment
      .tz(slot.slot_start, "YYYY-MM-DD HH:mm:ss", initialTimezone)
      .tz(targetTimezone);
    const endConverted = moment
      .tz(slot.slot_end, "YYYY-MM-DD HH:mm:ss", initialTimezone)
      .tz(targetTimezone);
    const date = startConverted.format("YYYY-MM-DD");

    const convertedSlot = {
      from: startConverted.format("HH:mm:ss"),
      to: endConverted.format("HH:mm:ss"),
    };

    if (startConverted.date() == endConverted.date()) {
      acc.push(convertedSlot);
    }

    return acc;
  }, []);
}

module.exports = {
  checkIfCanBook,
  convertSlotsToTimezone,
};
