function codingAfterIntroPrompt(question) {
  //moving to coding round
  //did you understand the question should be asked after 1 seconds.
  return `Thank you for the introduction. This round will be focussed on your technical skills and I’ll be asking you few questions to implement in the real time during the interview round.
  These questions will be around the assignment that you submitted.
  With this let’s start the interview. "${question}".
  If you have any questions, please feel free to ask.`;
}

function codingWithoutIntroPrompt(question, questionHasExample, licence) {
  switch (licence) {
    case "v1":
      return `Hello, I'm <PERSON><PERSON>, your interviewer for this technical round. We'll implement solutions related to your submitted assignment.
              The coding question is now displayed on your screen.
              If you have any doubts or questions about the problem, please feel free to ask. 
              But remember to pause me if you want to think out loud using the mute bot button.`;

    case "v2":
    case "v3":
    case "v4":
    case "v5":
      return `Hello, I'm <PERSON><PERSON>, your interviewer for this technical round. We'll implement solutions related to the coding project sent to you.
              I'm able to see any malicious activity that happens during the interview such as cheating or the candidate taking help from someone. 
              So I would request you to not indulge in it as it would lead to cancellation of your candidature. 
              In case if something of that sort happens during the interview I'll share my thoughts with the hiring team with proof but will not stop the candidate.
              The coding question is now displayed on your screen.
              If you have any doubts or questions about the problem, please feel free to ask. 
              But remember to pause me if you want to think out loud using the mute bot button.`;
    case "b2c":
      return `Hello, I'm Eval, your interviewer for this technical round. We'll be working on implementing a solution based on the coding project shared with you. The coding question is now displayed on your screen. If you have any doubts or questions about the problem, please feel free to ask. But remember to pause me if you want to think out loud using the mute bot button.`;
  }
}

function moveToNextCodingQuestion(answeredPrev) {
  return answeredPrev
    ? "Thank you for your response! The next question is now displayed on your screen. If you have any doubts, don't hesitate to ask."
    : "You've chosen to skip to the next question, which is now displayed on your screen. If you have any doubts, please feel free to ask.";
}

function codingReconnectedPrompt() {
  return "Hello! It seems you were disconnected. Let's continue from where we left off. The last question is on your screen.";
}

function codingEndPrompt() {
  return "Thank you for your time. Your responses have been recorded, and we will review them shortly. We appreciate your effort and look forward to following up with you soon. You may now leave this interview. Best regards.";
}

const errorResponse =
  "I am not able to currently process this conversation. Please contact tech support";

const PROMPT_LIMIT_EXCEEDED_MSG1 =
  "Please hold on for a moment while I process the information you've shared.";
const PROMPT_LIMIT_EXCEEDED_MSG2 =
  "Sorry for the interruption. Let's resume the interview";

const publishInterruptPrompt = `I am still evaluating your code. Please wait while I provide you with my feedback.`;

module.exports = {
  codingAfterIntroPrompt,
  codingWithoutIntroPrompt,
  errorResponse,
  PROMPT_LIMIT_EXCEEDED_MSG1,
  PROMPT_LIMIT_EXCEEDED_MSG2,
  publishInterruptPrompt,
  codingReconnectedPrompt,
  codingEndPrompt,
  moveToNextCodingQuestion,
};
