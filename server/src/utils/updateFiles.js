const fsPromises = require("fs").promises;
const fs = require("fs");
const { getRepoPath } = require("./directoryUtils");
const { ROOMS } = require("../cache");
const { cloneRepo } = require("../apis/cloneRepo");
const { findSubmittedAssignmentById } = require("../database");

const updateFiles = async (roomId, uid, changes) => {
  try {
    const submissionId = ROOMS.getRoom(roomId).submission_id;
    const repoPath = getRepoPath(uid);

    for (let i = 0; i < changes.length; i++) {
      let fileChangeDetails = changes[i];
      const filePath = fileChangeDetails.path;
      const { content, change, type } = fileChangeDetails;
      const fullPath = repoPath + filePath;

      if (!fs.existsSync(repoPath)) {
        console.log("Repo doesnt exist in file system, cloning it");
        if (!submissionId) throw new Error("Submission ID not found");
        const dbData = await findSubmittedAssignmentById(submissionId);
        if (!dbData.length) throw new Error("Assignment not found");
        await cloneRepo(dbData[0]);
      }

      if (type === 0) {
        if (change === "create") {
          await fsPromises.mkdir(fullPath, { recursive: true });
          console.log("directory created", fullPath);
        } else if (change === "delete") {
          if (fs.existsSync(fullPath)) {
            await fsPromises.rm(fullPath, { recursive: true });
            console.log("directory deleted", fullPath);
          }
        }
      } else if (type === 1) {
        if (change === "delete") {
          if (fs.existsSync(fullPath)) {
            await fsPromises.unlink(fullPath);
            console.log("file deleted", fullPath);
          }
        } else if (change === "create" || change === "update") {
          await fsPromises.writeFile(fullPath, content);
          console.log("file written", fullPath);
        }
      }
    }
  } catch (err) {
    console.log("error updating files", err);
    // throw err;
  }
};

module.exports = updateFiles;
