const { v4: uuidv4 } = require("uuid");

function generateRoomId() {
  const uuid = uuidv4().replace(/-/g, "");

  const charset = "abcdefghijklmnopqrstuvwxyz0123456789";

  const mapChar = (char) => {
    const hexValue = parseInt(char, 16);
    const mappedValue = Math.floor((hexValue / 15) * (charset.length - 1));
    return charset[mappedValue];
  };

  let roomId = "";
  for (let i = 0; i < 12; i++) {
    roomId += mapChar(uuid[i]);
  }

  const formattedRoomId = `${roomId.slice(0, 4)}-${roomId.slice(4, 8)}-${roomId.slice(8, 12)}`;

  return formattedRoomId;
}

module.exports = generateRoomId;
