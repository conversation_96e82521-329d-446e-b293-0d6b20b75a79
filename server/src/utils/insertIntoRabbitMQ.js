const amqp = require("amqplib");

var channel, connection;

//======use producer class=====

async function connectQueue() {
  try {
    connection = await amqp.connect(process.env.AMQP_URL);

    channel = await connection.createChannel({
      heartbeatIntervalInSeconds: process.env.HEARTBEAT_INTERVAL_IN_SECONDS,
      reconnectTimeInSeconds: process.env.RECONNECT_TIME_IN_SECONDS,
    });

    await channel.assertQueue(process.env.QUEUE_NAME_PARSER);
    await channel.assertQueue(process.env.QUEUE_NAME_ADMIN_INVITE);
    await channel.assertQueue(process.env.QUEUE_NAME_ASSIGNMENT_GENERATOR);
    await channel.assertQueue(process.env.QUEUE_NAME_CUSTOM_SKILLS_APPROVAL);
    await channel.assertQueue(process.env.SEND_JOB_FORM_CLOSED_QUEUE);

    if (process.env.NODE_ENV === "production") {
      await channel.assertQueue(process.env.SEND_ERROR_MAIL_QUEUE);
    }
  } catch (error) {
    console.log(error);
  }
}

async function insertFormDataToRabbitMQ(candidateData) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.QUEUE_NAME_ASSIGNMENT_GENERATOR,
    Buffer.from(JSON.stringify(candidateData))
  );
  console.log(
    "Candidate details sent",
    process.env.QUEUE_NAME_ASSIGNMENT_GENERATOR
  );
}

async function approveFormV1(candidateData) {
  await insertFormDataToRabbitMQ(candidateData);
}

async function approveFormV2(candidateData) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.ASSIGNMENT_PACKAGE_QUEUE,
    Buffer.from(JSON.stringify(candidateData))
  );
  console.log("Candidate details sent", process.env.ASSIGNMENT_PACKAGE_QUEUE);
}

async function insertV2FormDataToRabbitMQ(candidateData) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.ASSIGNMENT_PACKAGE_QUEUE,
    Buffer.from(JSON.stringify(candidateData))
  );
  console.log("Candidate details sent", process.env.ASSIGNMENT_PACKAGE_QUEUE);
}

async function insertV3FormDataToRabbitMQ(candidateData) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.ASSIGNMENT_GENERATOR_V3_QUEUE,
    Buffer.from(JSON.stringify(candidateData))
  );
  console.log(
    "Candidate details sent",
    process.env.ASSIGNMENT_GENERATOR_V3_QUEUE
  );
}

async function insertV4FormDataToRabbitMQ(candidateData) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.ASSIGNMENT_GENERATOR_V4_QUEUE,
    Buffer.from(JSON.stringify(candidateData))
  );
  console.log(
    "Candidate details sent",
    process.env.ASSIGNMENT_GENERATOR_V4_QUEUE
  );
}

async function insertV5FormDataToRabbitMQ(candidateData) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.ASSIGNMENT_GENERATOR_V5_QUEUE,
    Buffer.from(JSON.stringify(candidateData))
  );
  console.log(
    "Candidate details sent",
    process.env.ASSIGNMENT_GENERATOR_V5_QUEUE
  );
}

async function insertCandidatesFormDataToRabbitMQ(candidateData, licence) {
  if (!channel) {
    await connectQueue();
  }
  switch (licence) {
    case "v2":
      await channel.sendToQueue(
        process.env.CANDIDATE_ADDITION_V2_QUEUE,
        Buffer.from(JSON.stringify(candidateData))
      );
      break;
    case "v3":
      await channel.sendToQueue(
        process.env.CANDIDATE_ADDITION_V3_QUEUE,
        Buffer.from(JSON.stringify(candidateData))
      );
      break;
    case "v4":
      await channel.sendToQueue(
        process.env.CANDIDATE_ADDITION_V4_QUEUE,
        Buffer.from(JSON.stringify(candidateData))
      );
      break;
    case "v5":
      await channel.sendToQueue(
        process.env.CANDIDATE_ADDITION_V5_QUEUE,
        Buffer.from(JSON.stringify(candidateData))
      );
      break;
    default:
      console.log("Licence not found in insertCandidatesFormDataToRabbitMQ");
      return;
  }

  console.log("Candidate Addition details sent");
}

async function insertInviteIdIntoRabbitMQ(email, inviteId, name) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.QUEUE_NAME_ADMIN_INVITE,
    Buffer.from(
      JSON.stringify({ inviteId: inviteId, email: email, name: name })
    )
  );
  console.log("Invitation mail sent to ", email);
}

async function insertEmailIntoRabbitMQ(emailData) {
  try {
    if (!channel) {
      await connectQueue();
    }

    const { hireEmail, candidateProfileLink } = emailData;

    if (!hireEmail || !candidateProfileLink) {
      throw new Error("Both hireEmail and candidateProfileLink are required.");
    }

    await channel.sendToQueue(
      process.env.HIRING_EMAIL_QUEUE,
      Buffer.from(JSON.stringify({ email: hireEmail, candidateProfileLink }))
    );

    console.log(
      `Hiring email queued successfully: ${hireEmail}, Link: ${candidateProfileLink}`
    );
  } catch (error) {
    console.error("Error in insertEmailIntoRabbitMQ:", error);
    throw new Error("Failed to queue email.");
  }
}

async function insertAdminInviteIntoRabbitMQ(inviteId, companyId, email) {
  try {
    if (!channel) {
      await connectQueue();
    }
    const message = JSON.stringify({
      inviteId,
      companyId,
      email,
    });
    await channel.sendToQueue(
      process.env.QUEUE_NAME_ADMIN_INVITATION,
      Buffer.from(message)
    );
    console.log("Admin invitation data sent to RabbitMQ:", message);
  } catch (error) {
    console.error("Error sending admin invite to RabbitMQ:", error);
    throw new Error("Failed to send admin invite to RabbitMQ");
  }
}

async function insertAssignmentIntoRabbitMQ(submissionId, companyId) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.QUEUE_NAME_PARSER,
    Buffer.from(JSON.stringify({ id: submissionId, companyId: companyId }))
  );
  console.log(
    "Assignment successfully queued for parsing with submission id: ",
    submissionId
  );
}

async function insertCustomSkillsIntoRabbitMQ(skills) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.QUEUE_NAME_CUSTOM_SKILLS_APPROVAL,
    Buffer.from(JSON.stringify(skills))
  );
  console.log("Custom skills sent: ", skills);
}

async function insertInterviewAssignmentIntoRabbitMQ(
  companyId,
  formId,
  assignmentPoolId,
  assignmentLink,
  branch
) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.INTERVIEW_ASSIGNMENT_PARSE_QUEUE,
    Buffer.from(
      JSON.stringify({
        companyId,
        formId,
        assignmentPoolId,
        assignmentLink,
        branch,
      })
    )
  );
  console.log("Interview assignment sent for parsing");
}

async function insertSendCloseJobForm({
  jobRole,
  companyName,
  closeJobFormTemplate,
  closeJobFormSubject,
  name,
  email,
}) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.SEND_JOB_FORM_CLOSED_QUEUE,
    Buffer.from(
      JSON.stringify({
        jobRole,
        companyName,
        closeJobFormTemplate,
        closeJobFormSubject,
        name,
        email,
      })
    )
  );
}

async function sendDownloadCandidateDataToRabbitMQ(data) {
  try {
    if (!channel) {
      await connectQueue();
    }

    await channel.sendToQueue(
      process.env.QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA,
      Buffer.from(JSON.stringify(data))
    );

    console.log("Download candidate data request queued:", data);
  } catch (error) {
    console.error("Error queuing download candidate data request:", error);
    throw new Error("Failed to queue download candidate data request.");
  }
}

async function sendDownloadCandidateDataForEmailToRabbitMQ(
  data,
  receiverEmail,
  dashboardType
) {
  try {
    if (!channel) {
      await connectQueue();
    }

    const payload = {
      candidates: data, // Candidate data
      receiverEmail, // Include the receiver's email
      dashboardType,
    };

    console.log("Sending data to the email queue:", payload);

    await channel.sendToQueue(
      process.env.QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA_FOR_EMAIL,
      Buffer.from(JSON.stringify(payload))
    );

    console.log("Download candidate data for email request queued");
  } catch (error) {
    console.error(
      "Error queuing download candidate data for email request:",
      error
    );
    throw new Error(
      "Failed to queue download candidate data for email request."
    );
  }
}

async function sendToHiringManagerThroughRabbitMQ(data) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.HIRING_MANAGER_REVIEW_QUEUE,
    Buffer.from(JSON.stringify(data))
  );
  console.log("Data sent to hiring manager");
}

async function sendErrorEmail(data) {
  if (!channel) {
    await connectQueue();
  }
  if (process.env.NODE_ENV !== "production") return;
  await channel.sendToQueue(
    process.env.SEND_ERROR_MAIL_QUEUE,
    Buffer.from(JSON.stringify(data))
  );
  console.log("Error email sent to RabbitMQ", JSON.stringify(data));
}

async function sendQuestionApprovalResponse(data, licence) {
  if (!channel) {
    await connectQueue();
  }

  switch (licence) {
    case "v2":
      await channel.sendToQueue(
        process.env.QUESTION_APPROVAL_V2_QUEUE,
        Buffer.from(JSON.stringify(data))
      );
      break;
    case "v3":
      await channel.sendToQueue(
        process.env.QUESTION_APPROVAL_V3_QUEUE,
        Buffer.from(JSON.stringify(data))
      );
      break;
    case "v4":
      await channel.sendToQueue(
        process.env.QUESTION_APPROVAL_V4_QUEUE,
        Buffer.from(JSON.stringify(data))
      );
      break;
    case "v5":
      await channel.sendToQueue(
        process.env.QUESTION_APPROVAL_V5_QUEUE,
        Buffer.from(JSON.stringify(data))
      );
      break;
    default:
      throw new Error("Invalid licence version");
  }

  console.log("Question approval response sent", licence, data);
}
async function sendInterviewReport(data) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.INTERVIEW_REPORT_MAIL_QUEUE,
    Buffer.from(JSON.stringify(data))
  );
}

async function sendInterviewInviteMail(data) {
  if (!channel) {
    await connectQueue();
  }
  await channel.sendToQueue(
    process.env.INTERVIEW_INVITE_MAIL_QUEUE,
    Buffer.from(JSON.stringify(data))
  );
}

module.exports = {
  insertAssignmentIntoRabbitMQ,
  insertInviteIdIntoRabbitMQ,
  insertFormDataToRabbitMQ,
  approveFormV1,
  approveFormV2,
  insertCustomSkillsIntoRabbitMQ,
  insertV2FormDataToRabbitMQ,
  insertV3FormDataToRabbitMQ,
  insertV4FormDataToRabbitMQ,
  insertV5FormDataToRabbitMQ,
  insertInterviewAssignmentIntoRabbitMQ,
  insertCandidatesFormDataToRabbitMQ,
  insertSendCloseJobForm,
  sendToHiringManagerThroughRabbitMQ,
  insertEmailIntoRabbitMQ,
  insertAdminInviteIntoRabbitMQ,
  sendErrorEmail,
  sendQuestionApprovalResponse,
  sendInterviewReport,
  sendDownloadCandidateDataToRabbitMQ,
  sendDownloadCandidateDataForEmailToRabbitMQ,
  sendInterviewInviteMail,
};
