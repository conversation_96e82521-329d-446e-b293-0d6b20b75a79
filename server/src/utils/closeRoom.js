const { ROOMS } = require("../cache/index");
const {
  generateInterviewSummary,
  insertSummaryAndUpdateInterview,
} = require("../database/index");
const callDataLayer = require("./callDataLayer");
const isCompanyTechyrr = require("./checkTechyrrCompany");
const { sendInterviewReport } = require("./insertIntoRabbitMQ");

const closeRoom = async (roomId) => {
  try {
    if (await ROOMS.hasRoom(roomId)) {
      ROOMS.clearAskForHelpTimeout(roomId);

      const room = ROOMS.getRoom(roomId);
      if (!room) {
        console.log("Room not found", roomId);
        return;
      }

      const { cheatingTimestamps = [], cheatingActive } = room;

      const cheatingData = {
        roomId,
        cheatingTimestamps,
      };

      if (isCompanyTechyrr(ROOMS.getCompanyId(roomId))) {
        await sendInterviewReport({
          name: ROOMS.getUserName(roomId),
          email: ROOMS.getUserEmail(roomId),
          reportLink: `https://techyrr.com/admin/v2/dashboard/form/${ROOMS.getFormId(
            roomId
          )}/candidate/${ROOMS.getCandidateId(roomId)}`,
        });
      }

      const companyId = ROOMS.getCompanyId(roomId);
      if (companyId) {
        const cheatingResponse = await callDataLayer(
          "/sendCheatingTimestamps",
          companyId,
          "POST",
          cheatingData
        );

        if (!cheatingResponse.success) {
          console.error(
            "Failed to send cheating timestamps to the data layer:",
            cheatingResponse
          );
        } else {
          console.log("Cheating timestamps sent successfully.");
        }
      } else {
        console.warn("No companyId found for room:", roomId);
      }

      // Generate and update interview summary
      const summaryData = await generateInterviewSummary(roomId);

      if (summaryData) {
        if (companyId) {
          await callDataLayer(
            "insertSummaryAndUpdateInterview",
            companyId,
            "POST",
            { roomId, ...summaryData }
          );
        } else {
          await insertSummaryAndUpdateInterview(roomId, summaryData);
        }
      }

      // Close and delete the room
      await ROOMS.closeBot(roomId);
      await ROOMS.deleteRoom(roomId);
    } else {
      console.log("Room not found", roomId);
    }
  } catch (error) {
    console.error("Error closing room:", error);
  }
};

module.exports = closeRoom;
