const { createWorker } = require("mediasoup-client-aiortc");

const createMediasoupWorker = async () => {
  const worker = await createWorker({
    logLevel: "debug",
  });
  worker.on("died", (e) => console.error("mediasoup Worker has died", e));
  worker.on("subprocessclose", () => console.error("mediasoup Worker subprocess has closed"));
  worker.on("@success", () => console.log("mediasoup Worker has started successfully"));
  worker.on("@failure", (e) => console.error("mediasoup Worker failed to start", e));

  return worker;
};

module.exports = createMediasoupWorker;
