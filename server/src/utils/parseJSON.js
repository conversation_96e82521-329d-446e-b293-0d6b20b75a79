function parseJsonWithErrorHandling(jsonString) {
  let i = 0;
  while (i++ < 50) {
    try {
      const parsedJSON = JSON.parse(jsonString);
      return parsedJSON;
    } catch (error) {
      const errorPosition = getErrorPosition(error.message, jsonString);
      if (errorPosition !== -1) {
        jsonString =
          jsonString.substring(0, errorPosition - 1) +
          "" +
          jsonString.substring(errorPosition);
      } else {
        console.log("Non-parsable string: ", jsonString);
        throw error;
      }
    }
  }
  throw new Error("Error parsing JSON: too many iterations");
}

function getErrorPosition(errorMessage, jsonString) {
  let match = /at position (\d+)/.exec(errorMessage);
  if (match) return parseInt(match[1], 10);
  match = /at line (\d+) column (\d+)/.exec(errorMessage);
  if (match) {
    const line = parseInt(match[1], 10);
    const column = parseInt(match[2], 10);
    const position = getPositionFromLineAndColumn(jsonString, line, column);
    return position;
  }
  return -1;
}

function getPositionFromLineAndColumn(str, line, column) {
  let position = 0;
  const lines = str.split("\n");

  for (let i = 0; i < line - 1; i++) {
    position += lines[i].length + 1; // +1 for the newline character
  }

  position += column - 1; // Column is 1-based, but position is 0-based
  return position;
}

module.exports = parseJsonWithErrorHandling;
