const axios = require("axios");
const { exec, execSync } = require("child_process");
const fsPromises = require("fs").promises;

const util = require("util");
const execPromise = util.promisify(exec);
const { insertIntoAssignmentSubmissions } = require("../database");
const { insertAssignmentIntoRabbitMQ } = require("./insertIntoRabbitMQ");
const callDataLayer = require("./callDataLayer");

const githubRepoUrlRegex =
  /^(https:\/\/github.com\/[a-zA-Z0-9_-]+\/[a-zA-Z0-9_-]+)(\.git)?(\/tree\/[^/]+.*)?$/;

const getRepoAndBranchFromUrl = (github_link) => {
  const treeIndex = github_link.indexOf("/tree/");
  if (treeIndex === -1) {
    return {
      repoUrl: github_link,
      branch: "",
    };
  }

  const branchPart = github_link.substring(treeIndex + 6);
  const nextSlashIndex = branchPart.indexOf("/");
  const branch =
    nextSlashIndex === -1
      ? branchPart
      : branchPart.substring(0, nextSlashIndex);

  const repoUrl = github_link.substring(0, treeIndex);

  return {
    repoUrl,
    branch,
  };
};

const getCurrentBranch = (repoDir) => {
  try {
    const branch = execSync("git branch --show-current", { cwd: repoDir })
      .toString()
      .trim();
    return branch;
  } catch (error) {
    console.error("Error fetching branch:", error);
    return null;
  }
};

const isGitRepo = async (dir) => {
  try {
    await execPromise("git rev-parse --is-inside-work-tree", { cwd: dir });
    return true;
  } catch (error) {
    return false;
  }
};

const initGitRepo = async (dir) => {
  try {
    await execPromise("git init", { cwd: dir });
    await execPromise("git branch -M main", { cwd: dir });
    await execPromise("git add .", { cwd: dir });
    await execPromise('git commit -m "Initial commit"', { cwd: dir });
  } catch (error) {
    console.error("Error initializing Git repo:", error);
    throw error;
  }
};

const createAzureDevopsRepo = async (repoName) => {
  try {
    const { AZURE_DEVOPS_ORG, AZURE_DEVOPS_PROJECT_ID, AZURE_DEVOPS_TOKEN } =
      process.env;
    const apiUrl = `https://dev.azure.com/${AZURE_DEVOPS_ORG}/_apis/git/repositories?api-version=7.1-preview.1`;

    const data = {
      name: repoName,
      project: {
        id: AZURE_DEVOPS_PROJECT_ID,
      },
    };

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${Buffer.from(
          `PAT:${AZURE_DEVOPS_TOKEN}`
        ).toString("base64")}`,
      },
    };

    const response = await axios.post(apiUrl, data, config);

    console.log("Repository created successfully:", response.data);

    return response.data;
  } catch (error) {
    console.error("Error creating Azure DevOps repo:");
    throw error;
  }
};

const pushGitRepo = async (
  remoteUrl,
  assignmentDir,
  localBranch = "main",
  remoteBranch = "main"
) => {
  const origin = remoteUrl.replace(
    "https://" + process.env.AZURE_DEVOPS_ORG,
    "https://" + process.env.AZURE_DEVOPS_TOKEN
  );

  try {
    await execPromise("git remote remove origin", {
      cwd: assignmentDir,
    });
  } catch (error) {
    console.log("error removing origin", error);
  }

  await execPromise(`git remote add origin ${origin}`, {
    cwd: assignmentDir,
  });
  await execPromise(`git push -u origin ${localBranch}:${remoteBranch}`, {
    cwd: assignmentDir,
  });
};

const deleteGitRepo = async (repoId) => {
  try {
    const { AZURE_DEVOPS_ORG, AZURE_DEVOPS_TOKEN } = process.env;
    const apiUrl = `https://dev.azure.com/${AZURE_DEVOPS_ORG}/_apis/git/repositories/${repoId}?api-version=7.1-preview.1`;

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${Buffer.from(
          `PAT:${AZURE_DEVOPS_TOKEN}`
        ).toString("base64")}`,
      },
    };

    await axios.delete(apiUrl, config);

    console.log("Repository deleted successfully:", repoId);
  } catch (error) {
    console.error("Error deleting Azure DevOps repo:", error);
  }
};

const cleanup = async (dir) => {
  try {
    await fsPromises.rm(dir, { recursive: true, force: true });
    console.log(`Cleaned up directory: ${dir}`);
  } catch (error) {
    console.error(`Error cleaning up directory ${dir}:`, error);
  }
};

const cleanupOnDisconnect = async (assignmentDir, res, onDisconnect) => {
  console.log("Client disconnected, cleaning up...");
  onDisconnect();
  await cleanup(assignmentDir);
  if (!res.headersSent) {
    res.status(499).send("upload-cancelled");
  }
};

const insertIntoDB = async (link, branch, assignmentId, companyId) => {
  const insertId = companyId
    ? (
        await callDataLayer("/assignmentSubmissions", companyId, "POST", {
          github_link: link,
          user_assignment_id: assignmentId,
          branch: branch,
        })
      ).data.id
    : (await insertIntoAssignmentSubmissions(link, branch, assignmentId))
        .insertId;

  await insertAssignmentIntoRabbitMQ(insertId, companyId);
};

function makeRepoLink(repoName) {
  const org = process.env.AZURE_DEVOPS_ORG;

  return `https://${org}@dev.azure.com/${org}/assignments/_git/${repoName}`;
}

module.exports = {
  githubRepoUrlRegex,
  isGitRepo,
  initGitRepo,
  createAzureDevopsRepo,
  pushGitRepo,
  cleanup,
  cleanupOnDisconnect,
  insertIntoDB,
  deleteGitRepo,
  getRepoAndBranchFromUrl,
  getCurrentBranch,
  makeRepoLink,
};
