const { ROOMS } = require("../cache/index");

const closeTutorialRoom = async (roomId) => {
  try {
    if (await ROOMS.hasRoom(roomId)) {
      ROOMS.clearTutorialInterval(roomId);
      await ROOMS.closeBot(roomId);
      await ROOMS.deleteTutorialRoom(roomId);
    } else {
      console.log("Room not found", roomId);
    }
  } catch (error) {
    console.error("close tutorial room", error);
  }
};

module.exports = closeTutorialRoom;
