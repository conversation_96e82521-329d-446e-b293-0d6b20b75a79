const archiver = require('archiver');
const fs = require('fs');

const createZip = (folderPath, outputZipPath) => {
  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(outputZipPath);
    const archive = archiver('zip', {
      zlib: { level: 1 }
    });

    output.on('close', () => {
      console.log(`${archive.pointer()} total bytes written to ${outputZipPath}`);
      resolve();
    });

    archive.on('error', (err) => {
      console.error('Error during archive creation:', err);
      reject(err);
    });

    archive.on("warning", function (err) {
      if (err.code === "ENOENT") {
        console.log(err)
      } else {
        throw err;
      }
    });

    archive.on('finish', () => console.log('Archiving finished.'));
    archive.on('end', () => console.log('Data stream has been drained.'));

    archive.pipe(output);

    archive.directory(folderPath, false);

    archive.finalize().then(() => {
      console.log('Archiving finalized successfully.');
    }).catch((err) => {
      console.error('Error finalizing archive:', err);
      reject(err);
    });
  });
};

module.exports = createZip;