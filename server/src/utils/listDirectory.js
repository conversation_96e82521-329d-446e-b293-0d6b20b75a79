const fs = require('fs');
const path = require('path');

const listDirectory = (dirPath, reqPath) => {
  const items = fs.readdirSync(dirPath, { withFileTypes: true });
  return generateHTML(reqPath, items);
};

const displayFileContent = (filePath, reqPath, is_img, is_svg) => {
  let fileContent = "";
  let imgSrc = "";

  if (is_img) {
    const imageBuffer = fs.readFileSync(filePath);
    const base64Image = imageBuffer.toString('base64');
    imgSrc = `data:image/${path.extname(filePath).slice(1)};base64,${base64Image}`;
  } else {
    fileContent = fs.readFileSync(filePath, 'utf-8');
  }

  return generateHTML(reqPath, null, { fileContent, imgSrc, is_img, is_svg });
};

const generateHTML = (reqPath, items = null, fileData = null) => {
  const isFileView = fileData !== null;

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${isFileView ? 'File View' : 'Directory'} - ${reqPath}</title>
        <style>
          :root {
            --color-bg: #0d1117;
            --color-border: #30363d;
            --color-text: #c9d1d9;
            --color-text-secondary: #8b949e;
            --color-link: #58a6ff;
            --color-hover-bg: #161b22;
            --color-breadcrumb-bg: #161b22;
            --color-file-icon: #8b949e;
            --color-header-bg: #161b22;
            --color-code-bg: #0d1117;
            --color-file-hover: #1f6feb;
            --color-scrollbar: #30363d;
            --color-scrollbar-hover: #6e7681;
          }

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
          }

          ::-webkit-scrollbar-track {
            background: var(--color-bg);
          }

          ::-webkit-scrollbar-thumb {
            background: var(--color-scrollbar);
            border-radius: 5px;
          }

          ::-webkit-scrollbar-thumb:hover {
            background: var(--color-scrollbar-hover);
          }

          body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            font-size: 16px;
            line-height: 1.5;
            color: var(--color-text);
            background-color: var(--color-bg);
          }

          .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
          }

          .header {
            margin-bottom: 24px;
            border-bottom: 1px solid var(--color-border);
            padding: 16px 24px;
            background: var(--color-header-bg);
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
          }

          .header h1 {
            font-size: 20px;
            font-weight: 600;
            color: var(--color-text);
          }

          .breadcrumb {
            background: var(--color-breadcrumb-bg);
            border: 1px solid var(--color-border);
            border-radius: 6px;
            padding: 12px 20px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            display: flex;
            flex-wrap: wrap;
          }

          .breadcrumb a {
            color: var(--color-link);
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
          }

          .breadcrumb a:hover {
            background: var(--color-hover-bg);
            text-decoration: none;
          }

          .breadcrumb span {
            color: var(--color-text-secondary);
            margin: 0 8px;
          }

          .content {
            background: var(--color-hover-bg);
            border: 1px solid var(--color-border);
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
          }

          .files-list {
            list-style: none;
          }

          .files-list li {
            border-bottom: 1px solid var(--color-border);
            transition: all 0.2s ease;
          }

          .files-list li:last-child {
            border-bottom: none;
          }

          .files-list a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--color-text);
            text-decoration: none;
            transition: all 0.2s ease;
          }

          .files-list a:hover {
            background: var(--color-hover-bg);
            color: var(--color-file-hover);
          }

          .files-list a:hover .icon {
            color: var(--color-file-hover);
          }

          .icon {
            color: var(--color-file-icon);
            margin-right: 12px;
            width: 20px;
            text-align: center;
            transition: all 0.2s ease;
          }

          .file-content {
            padding: 24px;
          }

          .file-content pre {
            background: var(--color-code-bg);
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
            border: 1px solid var(--color-border);
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.12);
            line-height: 1.6;
          }

          .file-content pre code {
            color: var(--color-text);
          }

          .file-content img{
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
          }

          .file-content svg{
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
          }

          @media (max-width: 768px) {
            .container {
              padding: 16px;
            }
            
            .header {
              padding: 12px 16px;
            }
            
            .breadcrumb {
              padding: 8px 12px;
            }
            
            .files-list a {
              padding: 8px 12px;
            }
          }
        </style>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
      </head>
      <body>
        <div class="container">
          ${generateBreadcrumbs(reqPath)}
          <div class="content">
            ${isFileView
      ? generateFileView(fileData)
      : generateDirectoryView(items, reqPath)}
          </div>
        </div>
      </body>
    </html>
  `;
};

const generateBreadcrumbs = (reqPath) => {
  const parts = reqPath.split('/').filter(Boolean);
  let accumulatedPath = '';

  const breadcrumbs = ['<div><a href="" title="Home"><i class="bi bi-house-fill"></i></a></div>'];

  parts.forEach((part, index) => {
    accumulatedPath += `${part}/`;
    breadcrumbs.push(
      `<div><a href="/assignment-repo/${accumulatedPath}" title="${part}">${part}</a><span>/</span></div>`
    );
  });

  return `
    <div class="breadcrumb">
      ${breadcrumbs.join(' ')}
    </div>
  `;
};

const generateDirectoryView = (items, reqPath) => {
  const sortedItems = items.filter(items => items.name !== '.git').sort((a, b) => {
    if (a.isDirectory() && !b.isDirectory()) return -1;
    if (!a.isDirectory() && b.isDirectory()) return 1;
    return a.name.localeCompare(b.name);
  });

  return `
    <ul class="files-list">
      ${sortedItems.map(item => {
    const isDirectory = item.isDirectory();
    const icon = isDirectory
      ? '<i class="bi bi-folder-fill icon"></i>'
      : '<i class="bi bi-file-earmark-fill icon"></i>'
    const itemPath = path.join(reqPath, item.name);

    return `
          <li>
            <a href="/assignment-repo${itemPath}" title="${item.name}">
              ${icon}
              ${item.name}
            </a>
          </li>
        `;
  }).join('')}
    </ul>
  `;
};

const escapeHTML = (str) => {
  return str
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
};

const generateFileView = ({ fileContent, imgSrc, is_img, is_svg }) => {
  if (is_img) {
    return `
      <div class="file-content">
        <img src="${imgSrc}" alt="File preview" />
      </div>
    `;
  } else if (is_svg) {
    return `<div class="file-content">${fileContent}</div>`
  }

  const escapedContent = escapeHTML(fileContent);

  return `
  <div class="file-content">
    <pre><code style="font-size: 13px;">${escapedContent}</code></pre>
  </div>
  <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/styles/github-dark.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js"></script>
  <script>hljs.highlightAll();</script>
`;
};

module.exports = { listDirectory, displayFileContent };