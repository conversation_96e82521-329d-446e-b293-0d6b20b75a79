const path = require("path");
const axios = require("axios");

const callDataLayer = async (
  route,
  companyId,
  method = "GET",
  data = null,
  additionalConfig = {}
) => {
  const routePath = route.startsWith('/') ? route.slice(1) : route;
  const url = `${process.env.DATA_LAYER_URL}${routePath}`;

  const baseConfig = {
    headers: {
      "Content-Type": "application/json",
      companyid: companyId,
    },
  };

  const mergeConfigs = (target, source) => {
    if (typeof target !== "object") {
      return source;
    }
    return {
      ...target,
      ...Object.fromEntries(
        Object.entries(source).map(([key, value]) =>
          typeof value === "object" && key in target
            ? [key, mergeConfigs(target[key], value)]
            : [key, value]
        )
      ),
    };
  };

  const mergedConfig = mergeConfigs(baseConfig, additionalConfig);

  switch (method) {
    case "GET":
      return (await axios.get(url, mergedConfig)).data;
    case "POST":
      return (await axios.post(url, data, mergedConfig)).data;
    case "PUT":
      return (await axios.put(url, data, mergedConfig)).data;
    case "DELETE":
      return (await axios.delete(url, mergedConfig)).data;
    default:
      return (await axios.get(url, mergedConfig)).data;
  }
};

module.exports = callDataLayer;
