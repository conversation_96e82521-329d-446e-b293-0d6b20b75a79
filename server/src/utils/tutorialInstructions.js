const tutorialInstructions = {
  1: {
    main: "Hello, I’m <PERSON><PERSON>. I’ll be guiding you through a brief tutorial before we begin your interview. Can you find the timer on the screen? This is where you will see your remaining time. Please click the blinking indicator to move on to the next step.",
    repeat:
      "Please check the timer on the screen. Can you see the blinker on it? Click the blinking indicator to proceed.",
    time: 30,
  },
  2: {
    main: "Here you can see the search syntax button. You can use this box to search for syntax. Click the blinking indicator to move on to the next step.",
    repeat:
      "Please look for the search syntax button on the screen. Did you find it? Click the blinking indicator to proceed.",
    time: 30,
  },
  3: {
    main: "Here you can see the question that you need to answer. Click the blinking indicator to move on to the next step.",
    repeat:
      "Please check the question on the screen. Can you see it? Click the blinking indicator to proceed.",
    time: 30,
  },
  4: {
    main: "This is the switch button. You can use this button to switch between voice and text mode. Click the blinking indicator to move on to the next step.",
    repeat:
      "Please look for the switch button on the screen. Did you find it? Click the blinking indicator to proceed.",
    time: 30,
  },

  5: {
    main: "Did you find the skip question button? This button allows you to skip a question if needed. But remember, once you skip a question, you cannot go back to it. Click the blinking indicator to move on to the next step.",
    repeat:
      "Please look for the skip question button on the screen. Did you find it? Click the blinking indicator to proceed.",
    time: 30,
  },
  6: {
    main: `Open the "Tutorial" folder on the left side directory. You'll find a file named "Intro.txt". Click on it to open, then write a brief introduction about yourself in one line and click the publish button.`,
    repeat:
      `Please open the "Tutorial" folder on the left side directory, find and click on "Intro.txt". Write a brief introduction about yourself in one line and click the publish button. Click the blinking indicator to proceed.`,
    time: 70,
  },
  9: {
    main: "Congratulations! You are good to go. You can start the interview. Do not use external audio devices or attempt to cheat. I’m a computer vision AI—I won’t stop you, but your candidature will be canceled with proof sent to the hiring team. Click the blinking indicator to end the tutorial.",
    repeat: "Please click the blinking indicator to end the tutorial.",
    time: 50,
  },
};

module.exports = tutorialInstructions;
