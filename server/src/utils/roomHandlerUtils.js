const { updateFiles, getGitDiff } = require("./index");
const { ROOMS, GENAI } = require("../cache/index.js");
const { insertCodingHint } = require("../database/index.js");
const { askForHintPrompt, askForHintDSAPrompt } = require("./prompts.js");
const streamAIAudio = require("./streamAIAudio.js");
const getJsonFromPromptWithRetries = require("./getResultFromPromptWithRetries.js");
const callDataLayer = require("./callDataLayer.js");
const { botStates, modes, roomStates } = require("../constants/roomConstants.js");

function startAskForHelpTimeout(roomId) {
  ROOMS.clearAskForHelpTimeout(roomId);

  const timeoutId = setTimeout(async () => {
    if (!(await ROOMS.hasRoom(roomId))) {
      clearTimeout(timeoutId);
      console.log(
        "No room found for roomId:",
        roomId,
        "while executing ask for help timeout"
      );
      return;
    }

    ROOMS.getMessenger(roomId)?.emit("getChanges", {}, ({ changes }) => {
      ROOMS.pushPromiseToResponseQueue(
        roomId,
        (async () => {
          try {
            const { id, question } = ROOMS.getLastCodingQuestionAnswer(roomId);
            const languages = ROOMS.getProgrammingLanguages(roomId);
            const allHints = ROOMS.getHints(roomId);
            const previousHints = allHints.filter((hint) => hint.id === id);

            const uid = ROOMS.getUID(roomId);
            await updateFiles(roomId, uid, changes);
            const currentDiff = await getGitDiff(uid, changes);
            const previousDiff = ROOMS.getLastGitDiff(roomId);
            await ROOMS.setLastGitDiff(roomId, currentDiff);

            let prompt = "";

            switch (ROOMS.getRoomState(roomId)) {
              case roomStates.coding_conversation:
                prompt = askForHintPrompt(
                  languages,
                  5,
                  question,
                  previousDiff,
                  currentDiff,
                  previousHints
                );
                break;
              case roomStates.dsa_coding_conversation:
                const plantUMLDiagram = ROOMS.getPlantUMLDiagram(roomId);
                const currentQuestionIndex = ROOMS.getCurrentQuestionIndex(roomId);
                const { expectedComplexity = "", constraints = "" } = ROOMS.getRecommendedQuestions(roomId)[currentQuestionIndex];
                prompt = askForHintDSAPrompt(
                  5,
                  question,
                  plantUMLDiagram,
                  previousDiff,
                  currentDiff,
                  previousHints,
                  expectedComplexity,
                  constraints,
                  currentQuestionIndex + 1,
                )
                break;
            }

            const result = await getJsonFromPromptWithRetries(
              GENAI,
              prompt,
              "getResultFromPrompt",
              5,
              roomId
            );

            if (!result?.response) {
              throw new Error(`Invalid Response ${result}`);
            }

            const companyId = ROOMS.getCompanyId(roomId);

            if (companyId) {
              callDataLayer("insertCodingHint", companyId, "POST", {
                roomId: roomId,
                coding_conversation_id: id,
                query: "",
                hint: result.response,
                case_triggered: "hint_timeout",
              }).catch((err) =>
                console.log("codeRoomHandler startAskForHelpTimeout", err)
              );
            } else {
              insertCodingHint(roomId, id, "", result.response, "hint_timeout");
            }

            await ROOMS.pushToHints(roomId, {
              query: "",
              hint: result.response,
              id,
            });

            if (
              result.case === "right_track" ||
              result.case === "correct_code"
            ) {
              ROOMS.getMessenger(roomId)?.emit("canPublish", {
                canPublish: true,
              });
            }

            await ROOMS.pushToConversation(roomId, {
              role: "assistant",
              content: result.response,
            });

            const mode = ROOMS.getMode(roomId);
            ROOMS.changeBotState(roomId, botStates.IDLE);
            ROOMS.sendChatMessage(roomId, result.response);
            if (mode === modes.voice) {
              await streamAIAudio(roomId, result.response);
            }
            ROOMS.changeBotState(roomId, botStates.LISTENING);
            startAskForHelpTimeout(roomId);
          } catch (error) {
            console.log("Error in startAskForHelpTimeout:", error);
            ROOMS.changeBotState(roomId, botStates.LISTENING);
          }
        })()
      );
    });
  }, 300000);

  ROOMS.setAskForHelpTimeout(roomId, timeoutId);
}

module.exports = { startAskForHelpTimeout };
