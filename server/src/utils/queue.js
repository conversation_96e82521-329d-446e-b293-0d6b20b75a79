const DataSenderFactory = require("../models/sendData.js");
const os = require("os");
class Node {
  constructor(data) {
    this.data = data;
    this.next = null;
  }
}

class Queue {
  constructor(interval) {
    this.head = null;
    this.tail = null;
    this.interval = interval;
    this.intervalId = null;
    this.maxSize = 0.3 * os.totalmem();
    this.currentSize = 0;
  }

  enqueue(item) {
    let itemSize = Buffer.byteLength(JSON.stringify(item), "utf8");

    if (this.currentSize + itemSize > this.maxSize) {
      console.log("queue is full");
      return;
    }

    let node = new Node(item);
    if (this.head === null) {
      this.head = node;
      this.tail = node;
    } else {
      this.tail.next = node;
      this.tail = node;
    }
    this.currentSize += itemSize;
    if (this.intervalId === null) {
      console.log("queue was empty, starting interval");
      this.startInterval();
    }
  }

  dequeue() {
    if (this.head !== null) {
      let firstItem = this.head;
      this.head = this.head.next;
      this.currentSize -= Buffer.byteLength(JSON.stringify(firstItem.data), "utf8");
      return firstItem.data;
    } else {
      console.log("queue is empty, stopping interval");
      this.stopInterval();
      return null;
    }
  }

  isEmpty() {
    return this.head === null;
  }

  startInterval() {
    this.intervalId = setInterval(() => {
      let data = this.dequeue();
      if (data !== null) {
        this.sendDataToServer(data);
      }
    }, this.interval);
  }

  stopInterval() {
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  sendDataToServer(data) {
    const sender = DataSenderFactory.createDataSender(data.type);
    sender.sendData(data);
  }
}

module.exports = new Queue(50);
