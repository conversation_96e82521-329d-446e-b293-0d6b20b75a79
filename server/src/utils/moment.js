const moment = require("moment-timezone");

function toTimeString(time) {
  return moment(time, "HH:mm:ss").format("HH:mm:ss");
}

function toTimeStamp(date, time) {
  return moment
    .tz(`${date} ${time}`, "Asia/Calcutta")
    .format("YYYY-MM-DD HH:mm:ss");
}

function getDifferenceInMS(start, end) {
  return moment.duration(moment(end).diff(moment(start))).asMilliseconds();
}

function subtractMinutes(time, minutes) {
  return moment(time, "HH:mm:ss").subtract(minutes, "minutes");
}

const convertToUTC = (timeString, timezoneOffset) => {
  const localTimeString = timeString.replace("Z", "");

  const localTime = moment
    .parseZone(localTimeString)
    .utcOffset(timezoneOffset, true);

  const utcTime = localTime.clone().utc();

  return {
    utcString: utcTime.toISOString(),
    utcMs: utcTime.valueOf(),
  };
};

module.exports = {
  moment,
  toTimeString,
  toTimeStamp,
  getDifferenceInMS,
  subtractMinutes,
  convertToUTC,
};
