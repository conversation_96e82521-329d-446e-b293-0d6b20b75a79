const pdfParse = require("pdf-parse");
const Tesseract = require("tesseract.js");
const fs = require("fs");
const path = require("path");

const parseResume = async (filePath) => {
  let resumeContent;
  try {
    resumeContent = await pdfParser(filePath);
    if (!resumeContent.trim()) {
      throw new Error("Empty PDF content");
    }
  } catch (error) {
    console.log("PDF parsing failed, attempting OCR. Error: ", error);
    resumeContent = await performOCR(filePath);
  }

  return resumeContent;
};

const pdfParser = async (filePath) => {
  const buffer = fs.readFileSync(filePath);
  return pdfParse(buffer).then((data) => data.text);
};

const performOCR = async (filePath) => {
  try {
    const baseDir = path.join(path.dirname(filePath), path.basename(filePath).split(".")[0]);
    if (!fs.existsSync(baseDir)) {
      fs.mkdirSync(baseDir, { recursive: true });
    }
    const { pdf } = await import("pdf-to-img");

    const images = await pdf(filePath, {
      disableTransparency: false,
      scale: 2,
    });

    let ocrText = "";

    let counter = 0;
    for await (const image of images) {
      const imagePath = path.join(baseDir, `${counter}.png`);

      fs.writeFileSync(imagePath, image);
      const result = await Tesseract.recognize(imagePath, "eng");
      ocrText += result.data.text + "\n";
      counter++;
    }

    return ocrText;
  } catch (error) {
    console.error("OCR Failed:", error);
    return "";
  }
};

const cleanUpResumeFiles = async (filePath) => {
  try {
    if (!filePath) return;

    await fs.promises.unlink(filePath);

    const fileDir = path.dirname(filePath);

    const fileNameWithoutExt = path.basename(filePath, path.extname(filePath));
    const fileNamedDir = path.join(fileDir, fileNameWithoutExt);

    if (fs.existsSync(fileNamedDir)) {
      await fs.promises.rm(fileNamedDir, { recursive: true, force: true });
    }

    const remainingFiles = await fs.promises.readdir(fileDir);
    if (remainingFiles.length === 0) {
      await fs.promises.rmdir(fileDir);
    }
  } catch (error) {
    console.error("Error while cleaning up resume files:", error);
  }
};

module.exports = { parseResume, pdfParser, performOCR, cleanUpResumeFiles };
