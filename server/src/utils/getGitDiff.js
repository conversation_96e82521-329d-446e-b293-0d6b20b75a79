const { getRepoPath } = require("./directoryUtils");
const { exec } = require("child_process");
const path = require("path");
const util = require("util");
const execPromise = util.promisify(exec);
const fs = require("fs");

const getGitDiff = async (
  uid,
  changes,
  relativeRelevantFilePaths = [],
  questionNumber = null
) => {
  if (changes.length == 0) {
    return "";
  }
  const pathStart = changes[0].path.split("/")[1];
  const repoPath = path.join(getRepoPath(uid), pathStart);

  await execPromise("git add -N .", { cwd: repoPath });

  if (relativeRelevantFilePaths.length === 0) {
    console.log("GIT DIFF: no relevant files");
    const { stdout: gitDiff } = await execPromise("git diff", {
      cwd: repoPath,
    });
    return gitDiff;
  }

  let customDiff = "";
  const questionMarker = questionNumber ? `question-${questionNumber}` : null;

  for (const relativePath of relativeRelevantFilePaths) {
    const absolutePath = path.join(repoPath, relativePath);

    try {
      const fileContent = await fs.promises.readFile(absolutePath, "utf8");
      const originalContent = await getOriginalFileContent(
        repoPath,
        relativePath
      );

      if (questionMarker) {
        const combinedDiff = generateCombinedDiff(
          fileContent,
          originalContent,
          relativePath,
          questionMarker
        );
        customDiff += combinedDiff;
      }
    } catch (error) {
      console.error(`Error processing file ${absolutePath}:`, error);
    }
  }

  if (customDiff === "") {
    console.log("GIT DIFF: fallback");
    const { stdout: gitDiff } = await execPromise("git diff", {
      cwd: repoPath,
    });
    return gitDiff;
  }

  return customDiff;
};

const generateCombinedDiff = (
  currentContent,
  originalContent,
  relativePath,
  questionMarker
) => {
  const currentLines = currentContent.split("\n");
  const originalLines = originalContent.split("\n");

  const originalBlocks = extractQuestionBlocks(originalContent, questionMarker);
  const currentBlocks = extractQuestionBlocks(currentContent, questionMarker);

  let combinedDiff = generateFileHeader(relativePath);

  // diff for the entire file
  const normalDiff = generateDiff(originalLines, currentLines);

  // filter normal diff to exclude question block lines and add question block content
  const filteredDiff = [];
  let inQuestionBlock = false;

  for (const diffLine of normalDiff) {
    if (diffLine.includes(`<<<<<< ${questionMarker}`)) {
      inQuestionBlock = true;

      const currentBlock = currentBlocks.find(
        (b) => b.marker === questionMarker
      );
      const originalBlock = originalBlocks.find(
        (b) => b.marker === questionMarker
      );

      if (currentBlock) {
        filteredDiff.push(`<<<<<< ${questionMarker}`);

        const blockDiff = generateQuestionBlockDiff(
          currentBlock,
          originalBlock
        );
        filteredDiff.push(...blockDiff);

        filteredDiff.push(`>>>>>> ${questionMarker}`);
      }
      continue;
    }

    if (diffLine.includes(`>>>>>> ${questionMarker}`)) {
      inQuestionBlock = false;
      continue;
    }

    // Skip lines that are part of question blocks (except the markers we already handled)
    if (inQuestionBlock) {
      continue;
    }

    // Add non-question-block diff lines
    filteredDiff.push(diffLine);
  }

  // Add line numbers context (simplified version)
  if (filteredDiff.length > 0) {
    combinedDiff += `@@ -1,${originalLines.length} +1,${currentLines.length} @@\n`;
    combinedDiff += filteredDiff.join("\n");
    combinedDiff += "\n";
  }

  return combinedDiff;
};

const extractQuestionBlocks = (fileContent, specificMarker = null) => {
  if (!fileContent) return [];

  const blocks = [];
  const lines = fileContent.split("\n");

  let currentBlock = null;
  let blockContent = [];
  let lineStartIndex = -1;

  const startPattern = /<<<<<< (question-\d+)/;
  const endPattern = />>>>>> (question-\d+)/;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const startMatch = line.match(startPattern);
    const endMatch = line.match(endPattern);

    if (startMatch) {
      const marker = startMatch[1];
      if (!specificMarker || marker === specificMarker) {
        currentBlock = marker;
        blockContent = [];
        lineStartIndex = i + 1;
      }
    } else if (endMatch && currentBlock === endMatch[1]) {
      blocks.push({
        marker: currentBlock,
        content: blockContent.join("\n"),
        startLine: lineStartIndex,
        endLine: i - 1,
      });
      currentBlock = null;
      lineStartIndex = -1;
    } else if (currentBlock) {
      blockContent.push(line);
    }
  }

  return blocks;
};

const getOriginalFileContent = async (repoPath, relativePath) => {
  try {
    const { stdout } = await execPromise(`git show HEAD:./${relativePath}`, {
      cwd: repoPath,
      reject: false,
    });
    return stdout;
  } catch (error) {
    return "";
  }
};

const computeLCS = (x, y) => {
  const m = x.length;
  const n = y.length;

  const lcsTable = Array(m + 1)
    .fill()
    .map(() => Array(n + 1).fill(0));

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (x[i - 1] === y[j - 1]) {
        lcsTable[i][j] = lcsTable[i - 1][j - 1] + 1;
      } else {
        lcsTable[i][j] = Math.max(lcsTable[i - 1][j], lcsTable[i][j - 1]);
      }
    }
  }

  return lcsTable;
};

const generateQuestionBlockDiff = (currentBlock, originalBlock) => {
  if (originalBlock) {
    const originalLines = originalBlock.content.split("\n");
    const currentLines = currentBlock.content.split("\n");
    return generateDiff(originalLines, currentLines);
  } else {
    const lines = currentBlock.content.split("\n");
    return lines.map((line) => `+${line}`);
  }
};

const generateFileHeader = (relativePath) => {
  return `diff --git a/${relativePath} b/${relativePath}\n--- a/${relativePath}\n+++ b/${relativePath}\n`;
};

const generateDiff = (originalLines, currentLines) => {
  const lcsTable = computeLCS(originalLines, currentLines);
  const diffResult = [];

  let i = originalLines.length;
  let j = currentLines.length;

  const diffStack = [];

  while (i > 0 || j > 0) {
    if (i > 0 && j > 0 && originalLines[i - 1] === currentLines[j - 1]) {
      diffStack.push(` ${currentLines[j - 1]}`);
      i--;
      j--;
    } else if (j > 0 && (i === 0 || lcsTable[i][j - 1] >= lcsTable[i - 1][j])) {
      diffStack.push(`+${currentLines[j - 1]}`);
      j--;
    } else if (i > 0 && (j === 0 || lcsTable[i][j - 1] < lcsTable[i - 1][j])) {
      diffStack.push(`-${originalLines[i - 1]}`);
      i--;
    }
  }

  while (diffStack.length > 0) {
    diffResult.push(diffStack.pop());
  }

  return diffResult;
};

module.exports = getGitDiff;
