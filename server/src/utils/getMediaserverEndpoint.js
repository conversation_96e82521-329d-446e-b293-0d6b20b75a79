const axios = require("axios");
const path = require("path");
const { getBotIdToken } = require("../firebase");

const getMediaserverEndpoint = async (companyId, roomId) => {
  if (process.env.NODE_ENV !== "production") {
    return "http://localhost:3001";
  }
  const botIdToken = await getBotIdToken();
  const response = await axios.get(
    path.join(
      process.env.DISCOVERY_SERVER_URL,
      `/VMAllocation/getAllocatedVM/${companyId}/${roomId}`
    ),
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${botIdToken}`,
      },
    }
  );
  return response.data.domainName;
};

module.exports = { getMediaserverEndpoint };
