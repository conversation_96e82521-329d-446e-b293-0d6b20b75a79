BigInt.prototype.toJSON = function () {
  return this.toString();
};

const { JobForms } = require("neusortlib");
const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");
const isCompanyTechyrr = require("../utils/checkTechyrrCompany");

async function getAllFormsV1(req, res) {
  const { companyName } = req.params;
  const { companyId } = res.locals;
  let {
    page,
    pageSize,
    sorting,
    globalFilter,
    columnFilters,
    createdAt,
    isClosed,
  } = req.query;

  page = parseInt(page);
  pageSize = parseInt(pageSize);
  const offset = (page - 1) * pageSize;
  const createdAtJson = JSON.parse(createdAt);

  const sortArray = sorting ? JSON.parse(sorting) : [];
  const orderBy = buildOrderByClause(sortArray);
  const columnFiltersArray = columnFilters ? JSON.parse(columnFilters) : [];
  const filters = buildFilters(columnFiltersArray);

  let whereClause = globalFilter
    ? {
      OR: [
        { jobRole: { contains: globalFilter } },
        { adminName: { contains: globalFilter } },
        { experience: { contains: globalFilter } },
        { otherSkills: { contains: globalFilter } },
        {
          languages: {
            some: {
              language: {
                programming: { contains: globalFilter },
              },
            },
          },
        },
      ],
      ...filters,
    }
    : { ...filters, isDraft: false, isDeleted: false, licence: "v1" };

  whereClause = makeWhereClause(
    createdAtJson,
    createdAt,
    whereClause,
    isClosed
  );

  await getAllFormsByVersion(
    res,
    companyId,
    orderBy,
    offset,
    pageSize,
    whereClause,
  );
}

async function getAllFormsV2(req, res) {
  const { companyId, uid: adminId } = res.locals;
  let {
    page,
    pageSize,
    sorting,
    globalFilter,
    columnFilters,
    createdAt,
    isClosed,
  } = req.query;

  page = parseInt(page);
  pageSize = parseInt(pageSize);
  const offset = (page - 1) * pageSize;
  const createdAtJson = JSON.parse(createdAt);

  const sortArray = sorting ? JSON.parse(sorting) : [];
  const orderBy = buildOrderByClause(sortArray);
  const columnFiltersArray = columnFilters ? JSON.parse(columnFilters) : [];
  const filters = buildFilters(columnFiltersArray);

  let whereClause = globalFilter
    ? {
      OR: [
        { jobRole: { contains: globalFilter } },
        { adminName: { contains: globalFilter } },
        { experience: { contains: globalFilter } },
        { otherSkills: { contains: globalFilter } },
        {
          languages: {
            some: {
              language: {
                programming: { contains: globalFilter },
              },
            },
          },
        },
      ],
      ...filters,
    }
    : {
      ...filters,
      isDraft: false,
      isDeleted: false,
      AND:
        isCompanyTechyrr(companyId)
          ? [
            { licence: "v3" },
            {
              [`${companyId}_job_form_candidate_mapping`]: {
                some: { admin_id: adminId },
              },
            },
          ]
          : [
            {
              OR: [{ licence: "v2" }, { licence: "v3" }],
            },
          ],
    };

  whereClause = makeWhereClause(
    createdAtJson,
    createdAt,
    whereClause,
    isClosed
  );

  await getAllFormsByVersion(
    res,
    companyId,
    orderBy,
    offset,
    pageSize,
    whereClause,
  );
}

async function getAllFormsV4(req, res) {
  const { companyId } = res.locals;
  let {
    page,
    pageSize,
    sorting,
    globalFilter,
    columnFilters,
    createdAt,
    isClosed,
  } = req.query;

  if (!companyId) {
    return res.status(500).json("Company ID is required");
  }

  page = parseInt(page);
  pageSize = parseInt(pageSize);
  const offset = (page - 1) * pageSize;
  const createdAtJson = JSON.parse(createdAt);

  const sortArray = sorting ? JSON.parse(sorting) : [];
  const orderBy = buildOrderByClause(sortArray);
  const columnFiltersArray = columnFilters ? JSON.parse(columnFilters) : [];
  const filters = buildFilters(columnFiltersArray);

  let whereClause = globalFilter
    ? {
      OR: [
        { jobRole: { contains: globalFilter } },
        { adminName: { contains: globalFilter } },
        { experience: { contains: globalFilter } },
        { otherSkills: { contains: globalFilter } },
        {
          languages: {
            some: {
              language: {
                programming: { contains: globalFilter },
              },
            },
          },
        },
      ],
      ...filters,
    }
    : {
      ...filters,
      isDraft: false,
      isDeleted: false,
      licence: "v4",
    };

  whereClause = makeWhereClause(
    createdAtJson,
    createdAt,
    whereClause,
    isClosed
  );

  await getAllFormsByVersion(
    res,
    companyId,
    orderBy,
    offset,
    pageSize,
    whereClause,
  );
}

async function getAllFormsV5(req, res) {
  const { companyId } = res.locals;
  let {
    page,
    pageSize,
    sorting,
    globalFilter,
    columnFilters,
    createdAt,
    isClosed,
  } = req.query;

  if (!companyId) {
    return res.status(500).json("Company ID is required");
  }

  page = parseInt(page);
  pageSize = parseInt(pageSize);
  const offset = (page - 1) * pageSize;
  const createdAtJson = JSON.parse(createdAt);

  const sortArray = sorting ? JSON.parse(sorting) : [];
  const orderBy = buildOrderByClause(sortArray);
  const columnFiltersArray = columnFilters ? JSON.parse(columnFilters) : [];
  const filters = buildFilters(columnFiltersArray);

  let whereClause = globalFilter
    ? {
      OR: [
        { jobRole: { contains: globalFilter } },
        { adminName: { contains: globalFilter } },
        { experience: { contains: globalFilter } },
        { otherSkills: { contains: globalFilter } },
        {
          languages: {
            some: {
              language: {
                programming: { contains: globalFilter },
              },
            },
          },
        },
      ],
      ...filters,
    }
    : {
      ...filters,
      isDraft: false,
      isDeleted: false,
      licence: "v5",
    };

  whereClause = makeWhereClause(
    createdAtJson,
    createdAt,
    whereClause,
    isClosed
  );

  await getAllFormsByVersion(
    res,
    companyId,
    orderBy,
    offset,
    pageSize,
    whereClause,
  );
}

const getAllFormsByVersion = async (
  res,
  companyId,
  orderBy,
  offset,
  pageSize,
  whereClause,
) => {
  try {
    const result = await callDataLayer("/getAllJobForms", companyId, "POST", {
      orderBy,
      offset,
      pageSize,
      whereClause,
    });

    if (!result.success) {
      return res
        .status(200)
        .json({ company: { forms: [] }, numberOfForms: 0, numberOfCoins: 0 });
    }

    const allForms = new JobForms(
      result.data.forms,
      result.data.noOfForms,
      result.data.noOfCoins
    );

    return res.status(200).json({
      company: { forms: allForms.returnJobFormsData().forms || [] },
      numberOfForms: allForms.getNoOfForms() || 0,
      numberOfCoins: allForms.getNoOfCoins() || 0,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ error: error.message });
  }
}

function buildOrderByClause(sortArray) {
  return sortArray.reduce((acc, { id, desc }) => {
    if (id === "_count.candidates") {
      acc.candidates = { _count: desc ? "desc" : "asc" };
    } else {
      acc[id] = desc ? "desc" : "asc";
    }
    return acc;
  }, {});
}

function buildFilters(columnFiltersArray) {
  return columnFiltersArray.reduce((acc, { id, value }) => {
    if (id === "languages") {
      acc[id] = {
        some: {
          language: {
            programming: { contains: value },
          },
        },
      };
    } else if (id === "_count.candidates") {
      return acc;
    } else {
      acc[id] = { contains: value };
    }
    return acc;
  }, {});
}

function makeWhereClause(createdAtJson, createdAt, whereClause, isClosed) {
  if (createdAtJson.startDate !== null && createdAtJson.startDate.length > 0) {
    const { startDate, endDate } = JSON.parse(createdAt);
    whereClause = {
      ...whereClause,
      created_at: {
        gte: new Date(startDate),
        ...(endDate && endDate.length > 0 && { lte: new Date(endDate) }),
      },
    };
  }
  if (isClosed) {
    whereClause.is_closed = isClosed === "closed" ? 1 : 0;
  }

  return whereClause;
}

module.exports = {
  getAllFormsV1,
  getAllFormsV2,
  getAllFormsV4,
  getAllFormsV5,
};
