const { checkMergingStatusData } = require("../database");
const callDataLayer = require("../utils/callDataLayer");

const checkMergingStatus = async (req, res) => {
  try {
    const { companyId, roomId } = req.body;
    const data = companyId ? (await callDataLayer(`/checkMergingStatus/${roomId}`, companyId)).data : await checkMergingStatusData(roomId);
    res.status(200).json({ success: true, file_create_time: data.file_create_time});
  } catch (error) {
    console.log("Error fetching merging status: ", error);
    res.status(500).json({ success: false, message: "internal-server-error" });
  }
}

module.exports = checkMergingStatus;