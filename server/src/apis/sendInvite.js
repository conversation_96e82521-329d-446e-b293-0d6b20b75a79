const {
  insertInviteIdIntoRabbitMQ,
} = require("../utils/insertIntoRabbitMQ");
const callDataLayer = require("../utils/callDataLayer");

async function sendInvite(req, res) {
  const { email, name, licences } = req.body;
  try {
    // const userInvite = await prisma.user_invitation.create({
    //   data: {
    //     invite_id: inviteId,
    //     user_email: email,
    //     user_name: name,
    //   },
    // });

    const userInviteResponse = await callDataLayer(
      "/superAdmin/userInvitation",
      null,
      "POST",
      {
        email,
        name,
        licences
      }
    );

    const inviteId = userInviteResponse.data.invite_id;
    await insertInviteIdIntoRabbitMQ(email, inviteId, name);
    return res.status(200).json({ message: "User invitation sent" });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "An error occured", error });
  }
}

module.exports = sendInvite;
