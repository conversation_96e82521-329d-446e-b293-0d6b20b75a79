const app = require("firebase-admin");

async function getAllAdmins(req, res) {
  const limit = parseInt(req.query.limit) 
  const offset = parseInt(req.query.offset) 
  console.log(offset,"offset");
  console.log(limit,"limit");
  let allAdminsArray = [];
  try {
    const allUsers = await app.auth().listUsers();
    const allAdmins = allUsers.users.filter(
      (userRecord) =>
        userRecord.customClaims && userRecord.customClaims.admin === true
    );
    allAdminsArray = allAdmins.map((userRecord) => ({
      uid: userRecord.uid,
      email: userRecord.email,
      displayName: userRecord.displayName,
      admin: userRecord.customClaims.admin,
      licences: userRecord.customClaims.licences || [],
    }));
    const startIndex = offset;
    const endIndex = offset + limit;
    filteredArray =  allAdminsArray.slice(startIndex, endIndex);
    res.status(200).json({ filteredArray });
  } catch (error) {
    res.status(500).json({ message: "An internal error occurred", error });
  }
}

module.exports = getAllAdmins;
