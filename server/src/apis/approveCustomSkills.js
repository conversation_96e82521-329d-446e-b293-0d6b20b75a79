const callDataLayer = require("../utils/callDataLayer");

const approveCustomSkills = async (req, res) => {
  try {
    const { skillIds } = req.body;
    await callDataLayer("/superAdmin/customSkills/approve", null, "POST", {
      skillIds,
    });
    res.status(200).json({ message: "Custom skills sent for approval" });
  } catch (error) {
    console.log("Error approving custom skills: ", error);
    res.status(500).json({ message: "internal-server-error" });
  }
};

module.exports = approveCustomSkills;
