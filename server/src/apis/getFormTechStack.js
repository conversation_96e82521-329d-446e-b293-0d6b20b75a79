const { TechStackCollection } = require("neusortlib/models");
const { fetchTechStacks } = require("../database");
const callDataLayer = require("../utils/callDataLayer");

const getFormTechStack = async (req, res) => {
  try {
    const { companyId } = res.locals;
    const { licence } = req.params;

    const result = companyId
      ? (await callDataLayer(`/techStacks/${licence}`, companyId))
      : await (async () => {
        const data = await fetchTechStacks();
        return { success: true, data: data };
      })();

    if (!result.success) {
      return res.status(400).send({
        success: false,
        data: []
      });
    }

    const data = new TechStackCollection(result.data);
    return res
      .status(200)
      .send({ success: true, data: data.returnTechStackCollection(), msg: "Data Fetched Successfully" });
  } catch (error) {
    console.error("Error executing query", error);
    return res.status(500).send({
      success: false,
      msg: "Something went wrong. Could not fetchdata.",
    });
  }
};

module.exports = { getFormTechStack };
