const callDataLayer = require("../utils/callDataLayer");

const getAdminCoinWallet = async (req, res) => {
  const { companyId } = await res.locals;
  try {
    let coinData = {};
    if (companyId) {
      coinData = (await callDataLayer("/getAdminCoinWallet", companyId)).data;
    }
    res.status(200).send({
      success: true,
      message: "Company coins data fetched successfully.",
      data: coinData,
    });
  } catch (err) {
    console.error("Error getAdminCoinWallet() - ", err);
    res.status(500).send({
      success: false,
      message: "Error fetching company coins data",
    });
  }
};

module.exports = getAdminCoinWallet;
