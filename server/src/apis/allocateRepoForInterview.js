const path = require("path");
const fsPromises = require("fs").promises;
const { v4: uuidv4 } = require("uuid");
const util = require("util");
const { exec } = require("child_process");
const execPromise = util.promisify(exec);
const {
  createAzureDevopsRepo,
  pushGitRepo,
  cleanup,
  deleteGitRepo,
  getRepoAndBranchFromUrl,
  getCurrentBranch,
  initGitRepo,
  githubRepoUrlRegex,
} = require("../utils/assignmentSubmission");
const {
  insertInterviewAssignmentIntoRabbitMQ,
} = require("../utils/insertIntoRabbitMQ");

const allocateRepoForInterview = async (req, res) => {
  const { formId, companyId, assignmentPoolId } = req.params;

  const link = req.body.assignmentLink;

  if (!link || githubRepoUrlRegex.test(link) === false) {
    res.status(500).send({
      message:
        "Invalid Link. Please provide a valid link to the assignment repository.",
    });
    return;
  }

  let { repoUrl, branch: localBranch } = getRepoAndBranchFromUrl(link);

  const remoteBranch = uuidv4().replace(/-/g, "");
  const repoName = uuidv4().replace(/-/g, "");
  const assignmentDir = path.join("/assignments", repoName);
  let repoCreated = false;
  let repoId;

  try {
    await fsPromises.mkdir(assignmentDir, { recursive: true });

    if (localBranch.length > 0) {
      await execPromise(
        `git clone -b ${localBranch} --single-branch ${repoUrl} ${assignmentDir}`
      );
    } else {
      await execPromise(
        `git clone --single-branch ${repoUrl} ${assignmentDir}`
      );
      // branch = getCurrentBranch(assignmentDir);
    }

    // reinitialize the git repo
    console.log("Reinitializing git repo", assignmentDir);
    await Promise.all([
      fsPromises.rm(path.join(assignmentDir, ".git"), {
        recursive: true,
        force: true,
      }),
      fsPromises.rm(path.join(assignmentDir, ".gitmodules"), {
        recursive: true,
        force: true,
      }),
    ]);

    await initGitRepo(assignmentDir);
    console.log("Reinitialized git repo", assignmentDir);

    localBranch = getCurrentBranch(assignmentDir);
    console.log("Local Branch after reinitializing:", localBranch);

    const { remoteUrl, id } = await createAzureDevopsRepo(repoName);
    repoId = id;
    repoCreated = true;

    await pushGitRepo(remoteUrl, assignmentDir, localBranch, remoteBranch);

    await insertInterviewAssignmentIntoRabbitMQ(
      companyId,
      formId,
      assignmentPoolId,
      remoteUrl,
      remoteBranch
    );

    await cleanup(assignmentDir);

    res.status(200).send({
      message: "Assignment submitted successfully",
    });
  } catch (error) {
    await cleanup(assignmentDir);

    if (repoCreated) {
      await deleteGitRepo(repoId);
    }
    console.error("Error submitting assignment by GitHub link:", link, error);
    res.status(500).send({
      code: "internal-server-error",
      message: "Something went wrong. Could not submit assignment.",
    });
  }
};

module.exports = { allocateRepoForInterview };
