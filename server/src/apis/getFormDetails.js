const { Form, AssingmentPoolPackage } = require("neusortlib");
const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");
const { getFromRealtimeDatabase } = require("../firebase");
const { makeRepoLink } = require("../utils/assignmentSubmission");

BigInt.prototype.toJSON = function () {
  return this.toString();
};

async function getFormDetails(companyId, formId, adminId) {
  console.log("getFormDetails", companyId, formId);

  const formDetail = companyId
    ? await callDataLayer(
        `/getJobForm/${formId}`,
        companyId,
        "GET",
        null,
        {
          headers: {
            adminid: adminId, 
          },
        }
      )
    : await prisma.job_form.findUnique({
        where: {
          id: parseInt(formId),
        },
        include: {
          languages: {
            select: {
              language: {
                select: {
                  programming: true,
                },
              },
            },
          },
        },
      });

  if (companyId) {
    const data = formDetail.data;
    const form = new Form({ ...data });
    return form.returnFormData();
  }

  return formDetail;
}

async function getFormDetailsAdmin(req, res) {
  try {
    const { formId } = req.params;
    const { companyId, uid: adminId } = res.locals;

    const formDetails = await getFormDetails(companyId, formId, adminId);
    res.status(200).json(formDetails);
  } catch (error) {
    res.status(500).json({
      message: "Error fetching form details",
      error: error,
    });
  }
}

async function getDetailsForRepoAllocation(req, res) {
  try {
    const { companyId, formId, assignmentPoolId } = req.params;
    const formDetails = await getFormDetails(companyId, formId);
    const assignmentPoolDetails = await callDataLayer(
      `/getAssignmentFromPool/${assignmentPoolId}`,
      companyId
    );

    const uniqueLanguages = new Set();

    formDetails.programmingSkills.skills.map((skillObj) => {
      uniqueLanguages.add(skillObj.programming_language.programming);
    });

    formDetails.programmingCustomSkills.skills.map((skillObj) => {
      uniqueLanguages.add(skillObj.programming_language.programming);
    });

    res.status(200).json({
      experience: formDetails.experience,
      jobRole: formDetails.jobRole,
      problemStatement: assignmentPoolDetails.problem_statement,
      languages: Array.from(uniqueLanguages),
      skills: formDetails.programmingSkills.skills.map(
        (skillObj) => skillObj.skill
      ),
      customSkills: formDetails.programmingCustomSkills.skills.map(
        (skillObj) => skillObj.skill
      ),
    });
  } catch (error) {
    res.status(500).json({
      message: "Error fetching form details",
      error: error.message,
    });
  }
}

async function getDetailsForQuestionsApprovalV2(req, res) {
  try {
    const { companyId, formId, branchId } = req.body;
    const formDetails = await getFormDetails(companyId, formId);
    const assignmentPoolPackage = new AssingmentPoolPackage(
      await callDataLayer(
        `/getAssignmentFromPoolByBranchId/${branchId}`,
        companyId
      )
    );

    const assignmentSubmissionDetails =
      assignmentPoolPackage.getAssignmentPoolSubmissions()[0];
    const assignmentBranchDetails = assignmentPoolPackage.getBranches()[0];

    const repoLink = assignmentSubmissionDetails.getRepoLink();
    const experience = formDetails.experience.split("-")[1];
    const branch = assignmentBranchDetails.getName();
    const questionsLink = assignmentSubmissionDetails.getQuestionsLink();

    const questionsPath =
      questionsLink.split("/experienceWiseMap/")[0] +
      `/experienceWiseMap/${experience}/${branch}/questionsV2`;
    const questionsV2 = await getFromRealtimeDatabase(questionsPath);
    const uniqueLanguages = new Set();

    [
      ...formDetails.programmingSkills.skills,
      ...formDetails.programmingCustomSkills.skills,
    ].map((skillObj) => {
      uniqueLanguages.add(skillObj.programming_language.programming);
    });

    res.status(200).json({
      repoLink: `${repoLink}?path=%2F&version=GB${branch}&_a=contents`,
      questions: questionsV2.flat(),
      languages: Array.from(uniqueLanguages),
      skills: formDetails.programmingSkills.skills.map(
        (skillObj) => skillObj.skill
      ),
      customSkills: formDetails.programmingCustomSkills.skills.map(
        (skillObj) => skillObj.skill
      ),
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      message: "Error fetching Questions Approval details",
      error: error.message,
    });
  }
}

async function getDetailsForQuestionsApprovalV3(req, res) {
  try {
    const { companyId, formId, repoName, branchName } = req.body;
    const formDetails = await getFormDetails(companyId, formId);

    const repoLink = makeRepoLink(repoName);

    const questionsPath = `assignments/v3/${repoName}/recommendedQuestions`;
    const questions = await getFromRealtimeDatabase(questionsPath);
    const uniqueLanguages = new Set();

    sendQuestionsApprovalData(formDetails, uniqueLanguages, res, repoLink, branchName, questions);
  } catch (error) {
    console.log(error);
    res.status(500).json({
      message: "Error fetching Questions Approval details",
      error: error.message,
    });
  }
}

async function getDetailsForQuestionsApprovalV4(req, res) {
  try {
    const { companyId, formId, repoName, branchName } = req.body;
    const formDetails = await getFormDetails(companyId, formId);

    const repoLink = makeRepoLink(repoName);

    const questionsPath = `assignments/v4/${repoName}/recommendedQuestions`;
    const questions = await getFromRealtimeDatabase(questionsPath);
    const uniqueLanguages = new Set();

    sendQuestionsApprovalData(formDetails, uniqueLanguages, res, repoLink, branchName, questions);
  } catch (error) {
    console.log(error);
    res.status(500).json({
      message: "Error fetching Questions Approval details",
      error: error.message,
    });
  }
}

async function getDetailsForQuestionsApprovalV5(req, res) {
  try {
    const { companyId, formId, repoName, branchName } = req.body;
    const formDetails = await getFormDetails(companyId, formId);

    const repoLink = makeRepoLink(repoName);

    const questionsPath = `assignments/v5/${repoName}/recommendedQuestions`;
    const questions = await getFromRealtimeDatabase(questionsPath);
    const uniqueLanguages = new Set();

    sendQuestionsApprovalData(formDetails, uniqueLanguages, res, repoLink, branchName, questions);
  } catch (error) {
    console.log(error);
    res.status(500).json({
      message: "Error fetching Questions Approval details",
      error: error.message,
    });
  }
}

function sendQuestionsApprovalData(formDetails, uniqueLanguages, res, repoLink, branchName, questions) {
  [
    ...formDetails.programmingSkills.skills,
    ...formDetails.programmingCustomSkills.skills,
  ].map((skillObj) => {
    uniqueLanguages.add(skillObj.programming_language.programming);
  });

  res.status(200).json({
    repoLink: `${repoLink}?path=%2F&version=GB${branchName}&_a=contents`,
    questions: questions,
    languages: Array.from(uniqueLanguages),
    skills: formDetails.programmingSkills.skills.map(
      (skillObj) => skillObj.skill
    ),
    customSkills: formDetails.programmingCustomSkills.skills.map(
      (skillObj) => skillObj.skill
    ),
  });
}

async function getDetailsForQuestionsApproval(req, res) {
  try {
    const { licence } = req.body;

    switch (licence) {
      case "v2":
        await getDetailsForQuestionsApprovalV2(req, res);
        break;
      case "v3":
        await getDetailsForQuestionsApprovalV3(req, res);
        break;
      case "v4":
        await getDetailsForQuestionsApprovalV4(req, res);
        break;
      case "v5":
        await getDetailsForQuestionsApprovalV5(req, res);
        break;
      default:
        res.status(400).json({
          message: "Invalid licence version",
        });
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({
      message: "Error fetching Questions Approval details",
      error: error.message,
    });
  }
}

module.exports = {
  getFormDetailsAdmin,
  getDetailsForRepoAllocation,
  getDetailsForQuestionsApproval,
};
