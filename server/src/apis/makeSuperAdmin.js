const app = require("firebase-admin");

async function makeSuperAdmin(req, res) {
  const { email } = req.body;
  //   console.log(email);
  try {
    const user = await app.auth().getUserByEmail(email);
    const currentClaims = user.customClaims || {};
    const updatedClaims = {
      ...currentClaims,
      superAdmin: true,
    };
    await app.auth().setCustomUserClaims(user.uid, updatedClaims);
    return res.status(200).json({
      message: `User ${email} has been updated with superAdmin: true`,
    });
  } catch (error) {
    return res.status(500).json({ error });
  }
}

module.exports = makeSuperAdmin;
