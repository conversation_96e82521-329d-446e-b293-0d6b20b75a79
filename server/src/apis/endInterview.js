const endInterviewHandler = require("../eventHandlers/endInterviewHandler");
const endTutorialHandler = require("../eventHandlers/endTutorialHandler");

const endInterview = async (req, res) => {
  try {
    const roomId = req.body.roomId;
    if (!roomId) {
      res.status(400).json({
        error: "roomId is required",
      });
      return;
    }

    if (roomId.toLowerCase().includes("tutorial")) {
      await endTutorialHandler(roomId);
    } else {
      await endInterviewHandler(roomId, null, true);
    }
    console.log("Interview ended by delayed queue:", roomId);
    res.status(200).json({
      message: `Interview ended ${roomId}`,
    });
  } catch (e) {
    console.error("Error ending interview by delayed queue:", e);
    res.status(500).json({
      error: e.message,
    });
  }
};

module.exports = endInterview;
