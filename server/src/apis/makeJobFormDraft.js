const { getUserByEmail } = require("../firebase");
const callDataLayer = require("../utils/callDataLayer");
const {
  sendToHiringManagerThroughRabbitMQ,
} = require("../utils/insertIntoRabbitMQ");
const prisma = require("../utils/prisma");

async function makeJobFormDraftV1(req, res) {
  try {
    const {
      adminId,
      companyName,
      candidates,
      experience,
      otherSkills,
      skills,
      adminName,
      jobRole,
      oldSkills,
      newSkills,
      jd_id,
      formId,
      templatesV1,
      hiringManagerEmail,
    } = req.body;
    const { companyId, email: hrEmail } = res.locals;

    if (
      hiringManagerEmail &&
      hiringManagerEmail.split("@")[1] !== hrEmail.split("@")[1]
    ) {
      return res.status(200).json({
        success: false,
        message:
          "Hiring Manager's email domain must match your company's email domain",
      });
    }

    const result = companyId
      ? (
          await callDataLayer("/admin/v1/makeJobFormDraft", companyId, "POST", {
            adminId,
            companyName,
            candidates,
            experience,
            otherSkills,
            skills,
            adminName,
            jobRole,
            oldSkills,
            newSkills,
            jd_id,
            formId,
            templatesV1,
            hiringManagerEmail,
          })
        ).data
      : await prisma.$transaction(async (tx) => {
          const company = await tx.company.findFirst({
            where: {
              name: companyName,
            },
          });

          const createForm = await tx.job_form.create({
            data: {
              adminId: adminId,
              companyId: company.id,
              experience: experience,
              otherSkills: otherSkills,
              adminName: adminName,
              jobRole: jobRole,
              isDraft: true,
              licence: "v1",
            },
          });

          await Promise.all(
            oldSkills.flatMap((oldSkill) =>
              oldSkill.skills.map((skill) =>
                tx.form_skill_mapping.create({
                  data: {
                    formId: createForm.id,
                    skillId: skill.skillId,
                  },
                })
              )
            )
          );

          await Promise.all(
            newSkills.map((skill) =>
              tx.custom_skills.create({
                data: {
                  formId: createForm.id,
                  skillName: skill.skillName,
                  languageId: skill.languageId,
                },
              })
            )
          );

          const skillIds = await Promise.all(
            skills.map(async (skillName) => {
              const skill = await tx.programming_language.findUnique({
                where: {
                  programming: skillName,
                },
              });
              if (skill) {
                return skill.language_id;
              }
            })
          );

          await Promise.all(
            skillIds.filter(Boolean).map(async (skillId) => {
              await tx.form_language_mapping.create({
                data: {
                  formId: createForm.id,
                  languageId: skillId,
                },
              });
            })
          );

          if (jd_id) {
            await tx.jd_extracted_data.update({
              where: {
                id: jd_id,
              },
              data: {
                job_form_id: parseInt(createForm.id),
              },
            });
          }
        });

    if (hiringManagerEmail && !formId) {
      sendToHiringManager(hiringManagerEmail, hrEmail, "v1", result.formId);
      return res
        .status(200)
        .json({ success: true, message: "Sent to Hiring Manager" });
    }
    return res
      .status(200)
      .json({ success: true, message: "Draft saved successfully!" });
  } catch (error) {
    console.log("error while making job form drafts:", error);
    return res.status(200).json({ success: false, message: "fail" });
  }
}

async function makeJobFormDraftV2(req, res) {
  try {
    const {
      adminId,
      companyName,
      candidates,
      experience,
      otherSkills,
      skills,
      adminName,
      jobRole,
      oldSkills,
      newSkills,
      jd_id,
      formId,
      templatesV2,
      hiringManagerEmail,
    } = req.body;
    const { companyId, email: hrEmail } = res.locals;

    if (
      hiringManagerEmail &&
      hiringManagerEmail.split("@")[1] !== hrEmail.split("@")[1]
    ) {
      return res.status(200).json({
        success: false,
        message:
          "Hiring Manager's email domain must match your company's email domain",
      });
    }

    const result = companyId
      ? (
          await callDataLayer("/admin/v2/makeJobFormDraft", companyId, "POST", {
            adminId,
            companyName,
            candidates,
            experience,
            otherSkills,
            skills,
            adminName,
            jobRole,
            oldSkills,
            newSkills,
            jd_id,
            formId,
            templatesV2,
            hiringManagerEmail,
          })
        ).data
      : await prisma.$transaction(async (tx) => {
          const company = await tx.company.findFirst({
            where: {
              name: companyName,
            },
          });

          const createForm = await tx.job_form.create({
            data: {
              adminId: adminId,
              companyId: company.id,
              experience: experience,
              otherSkills: otherSkills,
              adminName: adminName,
              jobRole: jobRole,
              isDraft: true,
              licence: "v2",
            },
          });

          await Promise.all(
            oldSkills.flatMap((oldSkill) =>
              oldSkill.skills.map((skill) =>
                tx.form_skill_mapping.create({
                  data: {
                    formId: createForm.id,
                    skillId: skill.skillId,
                  },
                })
              )
            )
          );

          await Promise.all(
            newSkills.map((skill) =>
              tx.custom_skills.create({
                data: {
                  formId: createForm.id,
                  skillName: skill.skillName,
                  languageId: skill.languageId,
                },
              })
            )
          );

          const skillIds = await Promise.all(
            skills.map(async (skillName) => {
              const skill = await tx.programming_language.findUnique({
                where: {
                  programming: skillName,
                },
              });
              if (skill) {
                return skill.language_id;
              }
            })
          );

          await Promise.all(
            skillIds.filter(Boolean).map(async (skillId) => {
              await tx.form_language_mapping.create({
                data: {
                  formId: createForm.id,
                  languageId: skillId,
                },
              });
            })
          );

          if (jd_id) {
            await tx.jd_extracted_data.update({
              where: {
                id: jd_id,
              },
              data: {
                job_form_id: parseInt(createForm.id),
              },
            });
          }
        });
    if (hiringManagerEmail && !formId) {
      sendToHiringManager(hiringManagerEmail, hrEmail, "v2", result.formId);
      return res
        .status(200)
        .json({ success: true, message: "Sent to Hiring Manager" });
    }
    return res
      .status(200)
      .json({ success: true, message: "Draft saved successfully!" });
  } catch (error) {
    console.log("error while making job form drafts:", error);
    return res.status(200).json({ success: false, message: "fail" });
  }
}

async function makeJobFormDraftV4(req, res) {
  try {
    const {
      adminId,
      companyName,
      candidates,
      experience,
      otherSkills,
      skills,
      adminName,
      jobRole,
      oldSkills,
      newSkills,
      jd_id,
      formId,
      templatesV4,
      hiringManagerEmail,
    } = req.body;
    const { companyId, email: hrEmail } = res.locals;

    if (
      hiringManagerEmail &&
      hiringManagerEmail.split("@")[1] !== hrEmail.split("@")[1]
    ) {
      return res.status(200).json({
        success: false,
        message:
          "Hiring Manager's email domain must match your company's email domain",
      });
    }

    if (!companyId) {
      return res.status(500).json({ message: "Company id is required" });
    }

    const result = (
      await callDataLayer("/admin/v4/makeJobFormDraft", companyId, "POST", {
        adminId,
        companyName,
        candidates,
        experience,
        otherSkills,
        skills,
        adminName,
        jobRole,
        oldSkills,
        newSkills,
        jd_id,
        formId,
        templatesV4,
        hiringManagerEmail,
      })
    ).data;

    if (hiringManagerEmail && !formId) {
      sendToHiringManager(hiringManagerEmail, hrEmail, "v4", result.formId);
      return res
        .status(200)
        .json({ success: true, message: "Sent to Hiring Manager" });
    }
    return res
      .status(200)
      .json({ success: true, message: "Draft saved successfully!" });
  } catch (error) {
    console.log("error while making job form drafts:", error);
    return res.status(200).json({ success: false, message: "fail" });
  }
}

async function makeJobFormDraftV5(req, res) {
  try {
    const {
      adminId,
      companyName,
      candidates,
      experience,
      otherSkills,
      skills,
      adminName,
      jobRole,
      oldSkills,
      newSkills,
      jd_id,
      formId,
      templatesV5,
      hiringManagerEmail,
    } = req.body;
    const { companyId, email: hrEmail } = res.locals;

    if (
      hiringManagerEmail &&
      hiringManagerEmail.split("@")[1] !== hrEmail.split("@")[1]
    ) {
      return res.status(200).json({
        success: false,
        message:
          "Hiring Manager's email domain must match your company's email domain",
      });
    }

    if (!companyId) {
      return res.status(500).json({ message: "Company id is required" });
    }

    const result = (
      await callDataLayer("/admin/v5/makeJobFormDraft", companyId, "POST", {
        adminId,
        companyName,
        candidates,
        experience,
        otherSkills,
        skills,
        adminName,
        jobRole,
        oldSkills,
        newSkills,
        jd_id,
        formId,
        templatesV5,
        hiringManagerEmail,
      })
    ).data;

    if (hiringManagerEmail && !formId) {
      sendToHiringManager(hiringManagerEmail, hrEmail, "v5", result.formId);
      return res
        .status(200)
        .json({ success: true, message: "Sent to Hiring Manager" });
    }
    return res
      .status(200)
      .json({ success: true, message: "Draft saved successfully!" });
  } catch (error) {
    console.log("error while making job form drafts:", error);
    return res.status(200).json({ success: false, message: "fail" });
  }
}

const sendToHiringManager = async (
  hiringManagerEmail,
  hrEmail,
  licenceInUse,
  formId
) => {
  let inviteId;

  const hiringManagerDetails = await getUserByEmail(hiringManagerEmail);

  if (!hiringManagerDetails?.customClaims?.admin) {
    const hrDetails = await getUserByEmail(hrEmail);

    inviteId = (
      await callDataLayer("/superAdmin/userInvitation", null, "POST", {
        email: hiringManagerEmail,
        name: "Hiring Manager of " + hrEmail,
        licences: hrDetails?.customClaims?.licences,
      })
    ).data.invite_id;
  }

  await sendToHiringManagerThroughRabbitMQ({
    hiringManagerEmail: hiringManagerEmail,
    reviewLink: `admin.neusort.com/admin/${licenceInUse}/dashboard?formId=${formId}${
      inviteId ? `&inviteId=${inviteId}` : ""
    }`,
  });
};

module.exports = {
  makeJobFormDraftV1,
  makeJobFormDraftV2,
  makeJobFormDraftV4,
  makeJobFormDraftV5,
};
