const callDataLayer = require("../utils/callDataLayer");

async function getMonthlyTransactionsByYear(req, res) {
  const { year } = req.params;
  const { companyId } = res.locals;

  try {
    const data = (await callDataLayer(
      `/getMonthlyTransactionsByYear/${year}`,
      companyId,
      "GET"
    )).data;

    return res.status(200).json({ success: true, data });
  } catch (error) {
    console.error("Error getting monthly transactions:", error);
    return res.status(500).json({
      success: false,
      error: "Error fetching monthly transactions"
    });
  }
}

module.exports = { getMonthlyTransactionsByYear };