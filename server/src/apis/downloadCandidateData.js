const { sendDownloadCandidateDataToRabbitMQ } = require("../utils/insertIntoRabbitMQ");

const downloadCandidateData = async (req, res) => {
  try {
    const { companyId } = res.locals;
    const { formId, receiverEmail, dashboardType } = req.body;

    if (!formId) {
      return res.status(400).json({
        success: false,
        message: "Form ID is required.",
      });
    }
    await sendDownloadCandidateDataToRabbitMQ({
      companyId,
      formId,
      receiverEmail,
      dashboardType
    });

    return res.status(200).json({
      success: true,
      message: `Download request for formId ${formId} initiated successfully.`,
    });
  } catch (error) {
    console.error("Error in downloadCandidateData:", error);
    return res.status(500).json({
      success: false,
      message: "Internal Server Error",
    });
  }
};

module.exports = { downloadCandidateData };
