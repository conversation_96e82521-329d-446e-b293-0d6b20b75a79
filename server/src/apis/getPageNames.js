const callDataLayer = require("../utils/callDataLayer");

const getPageNames = async (req, res) => { 
  const { companyId } = res.locals;  
  
  try {
    const pagesData = await callDataLayer(
      "/admin/getPagesForAdmin",  
      companyId, 
      "POST",  
      req.body,
    );

    const pageNames = pagesData.pages || []; 

    return res.status(200).json(pageNames);
  } catch (error) {
    console.error("Error fetching page names:", error);

    return res.status(500).json({
      success: false,
      message: "Failed to fetch page names. Please try again later.",
    });
  }
};

module.exports = { getPageNames };
