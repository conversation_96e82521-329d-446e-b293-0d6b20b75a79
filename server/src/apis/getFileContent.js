const path = require("path");
const fs = require("fs");

const {
  defaultAssignmentsDir,
} = require("../constants/fileStructureConstants");
const { ROOMS } = require("../cache");
const { validateRoomAccess } = require("./validateRoomAccess");
const unsupportedFiles = require("../constants/unsupportedFiles");
const { cloneRepo } = require("./cloneRepo");
const { findSubmittedAssignmentById } = require("../database");
const callDataLayer = require("../utils/callDataLayer");

/* API CONTRACT
{
  "filePath": "/path/to/your/file.txt"
}
*/

const MAX_FILE_SIZE_IN_MB = 4;

function getFileExtension(filePath) {
  return filePath.split(".").pop();
}

async function getFileContent(req, res) {
  try {
    const { email, companyId, uid } = res.locals;
    const { roomId, allowSvg } = req.body;

    const accessResult = await validateRoomAccess(
      roomId,
      email,
      res.locals.role,
      companyId
    );

    if (accessResult.role === "unauthorized") {
      return res.status(401).send("unauthorized");
    }

    if (!allowSvg && unsupportedFiles.includes(getFileExtension(req.body.filePath))) {
      return res.status(400).send("unsupported-file-type");
    }

    const filePath = defaultAssignmentsDir + uid + "/" + req.body.filePath;
    console.log("filePath", filePath);

    // Ensure the path is safe to use
    if (!filePath || !path.isAbsolute(filePath)) {
      console.log(
        "Invalid file path. Please provide an absolute path.",
        filePath
      );
      return res.status(500).send("file-fetch-error");
    }

    if (!fs.existsSync(filePath)) {
      console.log("File does not exist. Cloning repo.");
      const doesRoomExist = await ROOMS.hasRoom(roomId);
      const roomData = ROOMS.getRoom(roomId);

      if (!doesRoomExist || !roomData) {
        console.log("Room does not exist. Cannot fetch file.");
        return res.status(500).send("internal-server-error");
      }

      let dbData = [];
      if (!!companyId) {
        dbData = [
          (
            await callDataLayer(
              `/submittedAssignment/${roomData.submission_id}`,
              companyId
            )
          ).data,
        ];
      } else {
        dbData = await findSubmittedAssignmentById(roomData.submission_id);
      }

      if (!dbData.length) return res.status(404).send("file-fetch-error");
      await cloneRepo(dbData[0]);
    }

    const stats = fs.statSync(filePath);
    const fileSizeInBytes = stats.size;
    const fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);

    if (fileSizeInMegabytes > MAX_FILE_SIZE_IN_MB) {
      console.log(
        "File size greater than",
        MAX_FILE_SIZE_IN_MB,
        "=>",
        fileSizeInMegabytes
      );
      return res.status(400).send("file-too-large");
    }

    const fileContent = fs.readFileSync(filePath, "utf8");
    return res.status(200).send({ content: fileContent });
  } catch (err) {
    console.log("Error getting file content: " + err.message);
    return res.status(500).send("file-fetch-error");
  }
}

module.exports = { getFileContent };
