const { ROOMS } = require("../cache");
const callDataLayer = require("../utils/callDataLayer");

const validateRoomAccess = async (
  roomId,
  email,
  roleFromCustomClaims,
  companyId
) => {
  if (!companyId) {
    return { role: "unauthorized", reason: "general_access_denied" };
  }
  let roomData = ROOMS.getRoom(roomId);

  if (!roomData) {
    roomData = (await callDataLayer(`/room/${roomId}`, companyId)).data;
  }

  if (!roomData || (Array.isArray(roomData) && roomData.length === 0)) {
    return { role: "unauthorized", reason: "room_not_found" };
  }

  if (Array.isArray(roomData)) roomData = roomData[0];

  if (roleFromCustomClaims === "interviewer") {
    return { role: "interviewer", reason: null };
  }

  if (roomData?.user_email === email) {
    return { role: "interviewee", reason: null };
  }

  if (roomData?.is_demo === 1) {
    return { role: "interviewee", reason: null };
  }

  if (roomData?.user_email && roomData.user_email !== email) {
    return {
      role: "unauthorized",
      reason: "email_mismatch",
      intendedEmail: roomData.user_email,
      currentEmail: email,
    };
  }

  return { role: "unauthorized", reason: "general_access_denied" };
};

module.exports = { validateRoomAccess };