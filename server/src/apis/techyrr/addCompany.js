const { insertCompany } = require("../../database/techyrr");

const addCompany = async (req, res) => {
	try {
		const company = req.body.company;
		const result = await insertCompany(company);
		return res.status(200).send(result);
	} catch (error) {
		console.error("Error executing query", error);
		return res.status(500).send({ success: false, msg: "Something went wrong. Could not fetchdata." });
	}
};

module.exports = { addCompany };
