const { getCompany } = require("../../database/techyrr");

const checkCompany = async (req, res) => {
	try {
		const company = req.body.company;
		const result = await getCompany(company);
		return res.status(200).send(result);
	} catch (error) {
		console.error("Error executing query", error);
		return res.status(500).send({ success: false, msg: "Something went wrong. Could not fetchdata." });
	}
};

module.exports = { checkCompany };
