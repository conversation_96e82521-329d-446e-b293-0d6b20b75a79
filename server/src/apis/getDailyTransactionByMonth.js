const callDataLayer = require("../utils/callDataLayer");

async function getDailyTransactionByMonth(req, res) {
  const { month, year, page, perPage } = req.query;
  const { companyId } = res.locals;

  try {
    const data = (await callDataLayer(
      `/getDailyTransactionByMonth?month=${month}&year=${year}&page=${page || 1}&perPage=${perPage || 10}`,
      companyId,
      "GET"
    )).data;

    return res.status(200).json({ success: true, data });
  } catch (error) {
    console.error("Error getting daily transactions:", error);
    return res.status(500).json({
      success: false,
      error: "Error fetching daily transactions"
    });
  }
}

module.exports = getDailyTransactionByMonth;