const { sendErrorEmail } = require("../utils/insertIntoRabbitMQ");

const reportError = async (req, res) => {
  const { uid } = res.locals;
  const { errorFunction, error, deviceDetails, isSendMail } = req.body;
  try {
    await sendErrorEmail({ isSendMail: isSendMail, errorFunction: errorFunction, error: JSON.stringify(error), uid: uid, deviceDetails: JSON.stringify(deviceDetails) });
    return res.status(201).json({ message: "Error reported successfully" });
  } catch {
    return res.status(500).json({ message: "Error reporting failed" });
  }
};

module.exports = reportError;
