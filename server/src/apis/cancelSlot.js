const { promisePool, updateTimeSlotOccupiedCount } = require("../database");
const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");
const { REDIS } = require("../cache/index");

const cancelSlot = async (req, res) => {
  try {
    const { submissionId } = req.params;
    const { companyId } = res.locals;
    console.log("cancelSlot:", submissionId, companyId);

    const result = await cancelSlotDbOperations(companyId, submissionId);

    try {
      await REDIS.deleteBookingSlotCache(submissionId);
    } catch (error) {
      console.error(`Error clearing booking slot cache: ${error}`);
    }

    res.status(200).json(result);
  } catch (error) {
    console.log("Error in cancelSlot", error.response.data);
    const errorStatus = error.status;
    const errorCode = error.response.data.code;

    console.log("errorStatus", errorStatus);
    console.log("errorCode", errorCode);

    res.status(errorStatus).json({ errorCode: errorCode });
  }
};

const cancelSlotDbOperations = async (companyId, submissionId) => {
  return await callDataLayer(
    `/cancelSlot/${submissionId}`,
    companyId,
    "DELETE"
  );
};

module.exports = { cancelSlot, cancelSlotDbOperations };
