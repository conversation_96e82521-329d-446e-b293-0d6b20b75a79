const { moment } = require("../utils/moment");
const { convertSlotsToTimezone } = require("../utils/bookSlot");
const { BOOKING_SLOT_CACHE, REDIS } = require("../cache");
const callDataLayer = require("../utils/callDataLayer");

const preferred_time_range_start =
  process.env.DEFAULT_DAY_START_TIME || "00:00:00";
const preferred_time_range_end = process.env.DEFAULT_DAY_END_TIME || "23:00:00";
const durationInMinutes = parseInt(process.env.ROOM_DURATION_IN_MINUTES) || 60;

const numberOfInterviewsOverlap =
  parseInt(process.env.NUMBER_OF_INTERVIEWS_OVERLAP) || 1;
const bufferHours = parseInt(process.env.BUFFER_HOURS) || 1;

const getAvailableSlots = async (req, res, next) => {
  try {
    const { companyId } = res.locals;
    const { date: requestedDate, submissionId } = req.body;
    console.log("body", req.body);

    const timezone = req.headers.timezone || "UTC";

    function convertLocalTimeToUTC(date, timeString) {
      const localMoment = moment.tz(
        `${date} ${timeString}`,
        "YYYY-MM-DD HH:mm:ss",
        timezone
      );

      const utcMoment = localMoment.clone().utc();

      return utcMoment;
    }

    const preferredStartTime = convertLocalTimeToUTC(
      requestedDate,
      preferred_time_range_start
    );
    const preferredEndTime = convertLocalTimeToUTC(
      requestedDate,
      preferred_time_range_end
    );

    const currentDate = moment.tz(timezone).format("YYYY-MM-DD");
    console.log("currentDate", currentDate);
    console.log("requestedDate", requestedDate);

    const currentDayMaxOccupiedCount = await REDIS.getMaxRoomCount();

    const bookingSlotCache = await BOOKING_SLOT_CACHE.get(submissionId);
    if (!bookingSlotCache) {
      return res.status(500).send("internal-server-error");
    }

    const validDates = JSON.parse(bookingSlotCache.get("validDates") || "[]");

    if (!validDates.includes(requestedDate)) {
      return res.status(400).send("invalid-time-slot");
    }

    const { data: result } = await callDataLayer(
      "/getAvailableSlots",
      companyId,
      "POST",
      {
        roomLength: durationInMinutes,
        dayStartTimeUTC: (() => {
          if (requestedDate === currentDate) {
            const nowStart = moment.utc();
            const nowStartWithBuffer = moment
              .utc(nowStart)
              .add(bufferHours, "hours");

            const preferredStartTimeWithBuffer = preferredStartTime
              .clone()
              .add(bufferHours, "hours");

            if (nowStartWithBuffer.isAfter(preferredStartTimeWithBuffer)) {
              const preferredMinutes = preferredStartTimeWithBuffer.minutes();

              const adjustedTime = moment
                .utc(nowStartWithBuffer)
                .minutes(preferredMinutes)
                .seconds(0);

              if (adjustedTime.isBefore(nowStartWithBuffer)) {
                adjustedTime.add(1, "hour");
              }

              return adjustedTime.format("YYYY-MM-DD HH:mm:ss");
            }

            return preferredStartTimeWithBuffer.format("YYYY-MM-DD HH:mm:ss");
          }

          return preferredStartTime.format("YYYY-MM-DD HH:mm:ss");
        })(),
        dayEndTimeUTC: preferredEndTime.format("YYYY-MM-DD HH:mm:ss"),
        maxOccupiedCount:
          requestedDate === currentDate
            ? currentDayMaxOccupiedCount
            : numberOfInterviewsOverlap,
      }
    );

    let slots = convertSlotsToTimezone(result, "UTC", timezone);

    await bookingSlotCache.set("validSlots", JSON.stringify(slots));
    await bookingSlotCache.set(
      "isCurrentDate",
      JSON.stringify(requestedDate === currentDate)
    );

    return res.status(200).json({ slots });
  } catch (err) {
    console.error("Error in getAvailableSlots:", err);
    return res.status(500).send("internal-server-error");
  }
};

module.exports = getAvailableSlots;
