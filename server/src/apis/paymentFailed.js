const callDataLayer = require("../utils/callDataLayer");

const paymentFailed = async (req, res) => {
  try {
    const { companyId } = res.locals;
    const data = req.body;

    await callDataLayer("/paymentFailed", companyId, "POST", data);

    return res.status(201).send({ success: true });
  } catch (error) {
    console.error('paymentFailed(): ', error);
    return res.status(500).send({
      success: false,
      msg: 'Something went wrong.',
    });
  }
};

module.exports = paymentFailed;