const callDataLayer = require("../utils/callDataLayer");

const getAdminData = async (req, res) => {
    const { companyId } = res.locals;

    try {
        const data = await callDataLayer("/admin/getAllAdminData", companyId, "GET");

        return res.status(200).json({
            success: true,
            roles: data.roles, 
            admins: data.admins,
            pages: data.pages
        });
        
    } catch (error) {
        console.error("Error fetching admin data:", error); 

        return res.status(500).json({
            success: false,
            message: "Failed to fetch admin data. Please try again later.",
        });
    }
};

module.exports = { getAdminData };
