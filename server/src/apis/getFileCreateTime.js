const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");

async function getFileCreateTime(req, res) {
  try {
    const { roomId, companyId } = req.query;
    const data = companyId
      ? (
        await callDataLayer(
          `/getCreateTime/${roomId}`,
          companyId
        )
      )
      : await prisma.rooms.findFirst({
        where: {
          roomid: roomId,
        },
        select: {
          file_create_time: true,
        },
      });
    return res
      .status(200)
      .json({
        time: data?.file_create_time
      });
  } catch (error) {
    console.error("Error getting time :", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

module.exports = getFileCreateTime;
