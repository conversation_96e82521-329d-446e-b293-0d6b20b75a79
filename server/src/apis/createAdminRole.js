const callDataLayer = require("../utils/callDataLayer");

const createAdminRole = async (req, res) => {
    const { companyId } = res.locals; 
    try {
        const data = await callDataLayer("/admin/createAdminRole", companyId, "POST",req.body);

        return res.status(201).json({
            success: true,
            role: data.role,  
            message: "Role created successfully!",
        });
    } catch (error) {
        console.error("Error creating role:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to create role. Please try again later.",
        });
    }
};

module.exports = { createAdminRole };
