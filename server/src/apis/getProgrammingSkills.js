const { ProgrammingSkills, ProgrammingCustomSkills } = require("neusortlib");
const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");

async function getProgrammingSkills(req, res) {
	const { skills, exp } = req.body;
	const { companyId } = res.locals;
  const { licence } = req.params;

	const getSkills = companyId
		? (
      await callDataLayer(`/programmingLanguagesAndSkills/${licence}`, companyId, "POST", {
				skills,
				exp,
			})
		)
		: await prisma.$transaction(async (tx) => {
			const getLangIds = await tx.programming_language.findMany({
				where: {
					programming: {
						in: skills,
					},
				},
				select: {
					language_id: true,
				},
			});

			const langIds = getLangIds.map((l) => l.language_id);

			const languagesAndSkills = await tx.programming_skills.findMany({
				where: {
					programming_language_id: {
						in: langIds,
					},
					experience: { lte: parseInt(exp) },
				},
				select: {
					skill: true,
					id: true,
					programming_language: {
						select: {
							programming: true,
							language_id: true,
						},
					},
				},
			});

			const programmingCustomSkills = await tx.programming_custom_skills.findMany({
				where: {
					programming_language_id: {
						in: langIds,
					},
					experience: { lte: parseInt(exp) },
				},
				select: {
					skill: true,
					id: true,
					programming_language: {
						select: {
							programming: true,
							language_id: true,
						},
					},
				},
			})
			return { success: ture, data: {programmingSkills: languagesAndSkills, programmingCustomSkills: programmingCustomSkills} };
		});

	if (!getSkills.success) {
		return res.status(400).json({ data: null });
	}

	const programmingSkills = new ProgrammingSkills(getSkills.data.programmingSkills.skills);
	const programmingCustomSkills = new ProgrammingCustomSkills(getSkills.data.programmingCustomSkills.skills);

	const transformedProgrammingSkills = programmingSkills.returnProgrammingSkills().skills.reduce((acc, { skill, id, programming_language: { programming, language_id } }) => {
		const existingLang = acc.find((lang) => lang.languageId === language_id);

		if (existingLang) {
			existingLang.skills.push({
				skillName: skill,
				skillId: id,
				type: "old",
			});
		} else {
			acc.push({
				languageId: language_id,
				languageName: programming,
				skills: [{ skillName: skill, skillId: id, type: "old" }],
			});
		}

		return acc;
	}, []);

	const data = {
		programmingSkills: transformedProgrammingSkills,
		programmingCustomSkills: programmingCustomSkills.returnProgrammingCustomSkills().skills,
	};

	return res.status(200).json({ data });
}

module.exports = getProgrammingSkills;
