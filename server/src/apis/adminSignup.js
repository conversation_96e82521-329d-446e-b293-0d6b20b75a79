const app = require("firebase-admin");
const callDataLayer = require("../utils/callDataLayer");
const { setCustomClaims } = require("../firebase");
const { UserInvitation } = require("neusortlib");

async function adminSignup(req, res) {
  const { inviteId, email, password, phone, gender, dob, displayName, companyId, techyrrAdmin } = req.body;
  try {
    let userInviteData, companyName, user;

    if (companyId) {
      const response = await callDataLayer(`/admin/invite/${inviteId}`, companyId, "GET");
      if (!response?.data) {
        return res.status(404).json({ message: "Invitation not found for the specified company." });
      }
      const userInvitation = new UserInvitation(response.data);
      userInviteData = userInvitation.returnUserInvite();
    } else {
      const response = await callDataLayer(`/superAdmin/userInvitation/${inviteId}`, null);
      if (!response?.data) {
        return res.status(404).json({ message: "Invitation not found." });
      }
      const userInvitation = new UserInvitation(response.data);
      userInviteData = userInvitation.returnUserInvite();

      if (!userInviteData || userInviteData.user_email !== email) {
        return res.status(400).json({ message: "Wrong email or invitation link!" });
      }

      companyName = userInviteData.user_email.split("@")[1].split(".")[0];
    }

    try {
      user = await app.auth().createUser({
        email,
        password,
        displayName,
      });
    } catch (error) {
      if (
        error.errorInfo?.code === "auth/email-already-exists" ||
        error.errorInfo?.code === "auth/email-already-in-use"
      ) {
        console.log("User already exists, fetching user data:", email);
        user = await app.auth().getUserByEmail(email);
      } else throw error;
    }

    let resolvedCompanyId = companyId;
    if (!companyId) {
      const companyResponse = await callDataLayer("/ddl/registerCompany", null, "POST", {
        companyName,
      });
      resolvedCompanyId = companyResponse?.companyId;
    }

    await setCustomClaims(user.uid, {
      admin: true,
      gender: gender,
      dob: dob,
      phone: phone,
      interviewer: !techyrrAdmin,
      companyId: resolvedCompanyId,
      licences: companyId ? userInviteData.licences : userInviteData.licences?.licences,
      techyrrAdmin,
    });

    const adminResponse = await callDataLayer("/addAdmins", resolvedCompanyId, "POST", { user, userInviteData });

    if (userInviteData.roles && userInviteData.roles.length > 0) {
      const roleMappings = userInviteData.roles.map((roleId) => ({
        admin_id: user.uid,
        role_id: roleId,
      }));

      const roleMappingResponse = await callDataLayer("/admin/addAdminRoleMapping", resolvedCompanyId, "POST", roleMappings);
    }
    return res.status(200).json({ message: "Admin signup is successful." });
  } catch (error) {
    console.error("Error during admin signup:", error);
    if (!res.headersSent) {
      return res.status(500).json({ message: "Internal server error." });
    }
  }
}

module.exports = adminSignup;
