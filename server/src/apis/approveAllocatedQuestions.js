const { sendQuestionApprovalResponse } = require("../utils/insertIntoRabbitMQ");

const approveAllocatedQuestions = async (req, res) => {
  try {
    const { licence, approved } = req.body;

    await sendQuestionApprovalResponse(
      {
        ...req.body,
      },
      licence
    );

    res.status(200).json({
      message: `Questions ${approved ? "approved" : "rejected"} successfully`,
    });
  } catch (e) {
    console.error("approveAllocatedQuestions", e);
    res.send(500).json({ message: "Internal Server Error", error: e.message });
  }
};

module.exports = approveAllocatedQuestions;
