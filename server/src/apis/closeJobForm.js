const callDataLayer = require("../utils/callDataLayer");
const { insertSendCloseJobForm } = require("../utils/insertIntoRabbitMQ");
const { Form } = require("neusortlib");

async function closeJobForm(req, res) {

  try {
    const { formId, templates, sendEmail } = req.body;
    const { companyId } = res.locals;

    if (!companyId) {
      return res.status(403).json({ message: "CompanyId Not Found" });
    }

    const data = (await callDataLayer("/closeJobForm", companyId, "POST", { formId, templates, sendEmail }));
    if (data.success) {
      const { formData, companyData, remainingCoins } = data.data;
      const FormData = new Form({ ...formData });
      if (sendEmail) {
        await Promise.all(
          FormData.returnFormData().candidates.map((candidate) =>
            insertSendCloseJobForm({
              name: candidate.name,
              email: candidate.email,
              companyName: companyData.name,
              jobRole: FormData.getJobRole(),
              closeJobFormTemplate: FormData.getCloseJobFormTemplate(),
              closeJobFormSubject: FormData.getCloseJobFormSubject(),
            })
          )
        );
      }
      return res.status(200).json({ success: true, message: "Job Form Closed", data: { ...companyData, remainingCoins } });
    } else {
      return res.status(200).json({ success: false, message: "Job Form is already closed" });
    }
  } catch (error) {
    console.error("Error in close job form", error);
    return res.status(500).json({ success: false, error: "Server Error" });
  }
}

module.exports = closeJobForm;