const prisma = require("../utils/prisma");

async function getCandidateSkills(req, res) {
  const { userId } = req.body;
  const getAllSkills = await prisma.candidate_form_mapping.findMany({
    where: {
      candidateId: userId,
    },
    include: {
      form: {
        include: {
          languages: {
            select: {
              language: {
                select: {
                  programming: true,
                },
              },
            },
          },
        },
      },
    },
  });
  const allSkills = getAllSkills.map((skill) => {
    const stack = skill.form.languages;
    const skills = stack.map((s) => {
      return s.language.programming;
    });
    return skills;
  });

  let filteredSkills = [];

  for (let skill of allSkills) {
    skill.map((s) => {
      if (!filteredSkills.includes(s)) {
        filteredSkills.push(s);
      }
    });
  }
  //   console.log(filteredSkills);
  return res.status(200).json({ filteredSkills });
}

module.exports = getCandidateSkills;
