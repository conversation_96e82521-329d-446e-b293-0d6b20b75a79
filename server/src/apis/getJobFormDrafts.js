const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");

async function getJobFormDraftsV1(req, res) {
  const { companyName, adminId } = req.body;
  const { companyId, email } = res.locals;

  let drafts;
  try {
    if (companyId) {
      const draftsCreated = (await callDataLayer(`/admin/v1/getJobFormDrafts/${adminId}`, companyId)).data;
      const draftsAssigned = (await callDataLayer(`/admin/v1/getJobFormDrafts/hmEmail/${email}`, companyId)).data;
      const draftsAssignedIds = draftsAssigned.map((draft) => draft.id);
      const draftsCreatedFiltered = draftsCreated.filter(
        (draft) => !draftsAssignedIds.includes(draft.id)
      );
      drafts = [...draftsCreatedFiltered, ...draftsAssigned];
    } else {
      const company = await prisma.company.findUnique({
        where: {
          name: companyName,
        },
      });

      drafts = await prisma.job_form.findMany({
        where: {
          companyId: company.id,
          adminId: adminId,
          isDraft: true,
          isDeleted: false,
          licence: 'v1'
        },
        select: {
          isDraft: true,
          created_at: true,
          experience: true,
          jobRole: true,
          adminId: true,
          adminName: true,
          id: true,
          otherSkills: true,
          languages: {
            select: {
              language: {
                select: {
                  programming: true,
                },
              },
            },
          },
          form_skill_mapping: {
            select: {
              skill: {
                select: {
                  skill: true,
                  id: true,
                  programming_language: {
                    select: {
                      programming: true,
                      language_id: true,
                    },
                  },
                },
              },
            },
          },
          custom_skills: {
            select: {
              id: true,
              skillName: true,
              language: {
                select: {
                  language_id: true,
                  programming: true,
                },
              },
            },
          },
        },
      });
    }

    const data = await getDraftData(drafts, companyId);

    return res.status(200).json({
      data,
      total: drafts.length,
      message: "All drafts receieved",
    });
  } catch (error) {
    console.log("Error in getJobFormDraftsV1", error);
    return res.status(500).json("Internal Error Occurred");
  }
}

async function getJobFormDraftsV2(req, res) {
  const { companyName, adminId } = req.body;
  const { companyId, email } = res.locals;

  try {
    const { data, drafts } = await fetchJobFormDraftsByVersion(adminId, companyId, email, "v2");

    return res.status(200).json({
      data,
      total: drafts.length,
      message: "All drafts receieved",
    });
  } catch (error) {
    console.log("Error in getJobFormDraftsV2", error);
    return res.status(500).json("Internal Error Occurred");
  }
}

async function getJobFormDraftsV4(req, res) {
  const { adminId } = req.body;
  const { companyId, email } = res.locals;

  try {
    if (!companyId) {
      return res.status(500).json("Company ID is required");
    }

    const { data, drafts } = await fetchJobFormDraftsByVersion(adminId, companyId, email, "v4");

    return res.status(200).json({
      data,
      total: drafts.length,
      message: "All drafts receieved",
    });
  } catch (error) {
    console.log("Error in getJobFormDraftsV4", error);
    return res.status(500).json("Internal Error Occurred");
  }
}

async function getJobFormDraftsV5(req, res) {
  const { adminId } = req.body;
  const { companyId, email } = res.locals;

  try {
    if (!companyId) {
      return res.status(500).json("Company ID is required");
    }

    const { data, drafts } = await fetchJobFormDraftsByVersion(adminId, companyId, email, "v5");

    return res.status(200).json({
      data,
      total: drafts.length,
      message: "All drafts receieved",
    });
  } catch (error) {
    console.log("Error in getJobFormDraftsV5", error);
    return res.status(500).json("Internal Error Occurred");
  }
}

async function fetchJobFormDraftsByVersion(adminId, companyId, email, version) {
  const draftsCreated = (await callDataLayer(`/admin/${version}/getJobFormDrafts/${adminId}`, companyId)).data;
  const draftsAssigned = (await callDataLayer(`/admin/${version}/getJobFormDrafts/hmEmail/${email}`, companyId)).data;
  const draftsAssignedIds = draftsAssigned.map((draft) => draft.id);
  const draftsCreatedFiltered = draftsCreated.filter(
    (draft) => !draftsAssignedIds.includes(draft.id)
  );
  const drafts = [...draftsCreatedFiltered, ...draftsAssigned];

  const data = await getDraftData(drafts, companyId);
  return { data, drafts };
}

async function getDraftData(drafts, companyId) {
  const transformedDrafts = drafts.map((draft) => {
    const transformedSkills = draft.form_skill_mapping.reduce((acc, curr) => {
      const { language_id, programming } = curr.skill.programming_language;
      const existingLanguage = acc.find(
        (item) => item.languageId === language_id
      );

      if (!existingLanguage) {
        acc.push({
          languageId: language_id,
          languageName: programming,
          skills: [],
          type: "old",
        });
      }

      const languageObject = acc.find(
        (item) => item.languageId === language_id
      );
      languageObject.skills.push({
        skillId: curr.skill.id,
        skillName: curr.skill.skill,
      });

      return acc;
    }, []);
    return {
      ...draft,
      form_skill_mapping: transformedSkills,
    };
  });

  const newTransformedDrafts = transformedDrafts.map((draft) => {
    const custom = draft.custom_skills.reduce((acc, curr) => {
      const { language_id, programming } = curr.language;
      acc.push({
        id: curr.id,
        languageId: language_id,
        languageName: programming,
        skillName: curr.skillName,
        type: "new",
      });
      return acc;
    }, []);
    return {
      ...draft,
      custom_skills: custom,
    };
  });

  const data = await Promise.all(
    newTransformedDrafts.map(async (draft) => {
      const languages = draft.languages.map(
        (lang) => lang.language.programming
      );


      const getTechStack = companyId ?
        (await callDataLayer('/getTechStackByLanguages', companyId, "POST", { languages })).data :
        await prisma.programming_language_group.findMany({
          where: {
            programming_language_group_mapping: {
              some: {
                programming_language: {
                  programming: {
                    in: languages,
                  },
                },
              },
            },
          },
          select: {
            name: true,
          },
        });

      const techStack = [
        ...new Set(getTechStack.map((stack) => stack?.name)),
      ];

      const mailTemplates = {
        sendAssignmentTemplate: draft.send_assignment_template,
        sendAssignmentSubject: draft.send_assignment_subject,
        interviewInviteTemplate: draft.interview_invite_template,
        interviewInviteSubject: draft.interview_invite_subject,
        interviewScheduleTemplate: draft.interview_schedule_template,
        interviewScheduleSubject: draft.interview_schedule_subject,
        interviewScheduleReminderTemplate: draft.interview_schedule_reminder_template,
        interviewScheduleReminderSubject: draft.interview_schedule_reminder_subject,
        interviewRescheduleReminderTemplate: draft.interview_reschedule_reminder_template,
        interviewRescheduleReminderSubject: draft.interview_reschedule_reminder_subject,
        closeJobFormTemplate: draft.close_job_form_template,
        closeJobFormSubject: draft.close_job_form_subject,
      };

      delete draft.send_assignment_template;
      delete draft.send_assignment_subject;
      delete draft.interview_invite_template;
      delete draft.interview_invite_subject;
      delete draft.interview_schedule_template;
      delete draft.interview_reschedule_reminder_template;
      delete draft.interview_schedule_subject;
      delete draft.interview_schedule_reminder_template;
      delete draft.interview_schedule_reminder_subject;
      delete draft.interview_reschedule_reminder_subject;
      delete draft.close_job_form_template;
      delete draft.close_job_form_subject;

      return {
        ...draft,
        mailTemplates,
        languages: languages,
        draftTechStack: techStack,
      };
    })
  );
  return data;
}

module.exports = {
  getJobFormDraftsV1,
  getJobFormDraftsV2,
  getJobFormDraftsV4,
  getJobFormDraftsV5,
};