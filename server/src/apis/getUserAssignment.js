const getUserAssignment = async (req,res) => {

    const {assignmentId} = req.body;
    const {uid} = req.locals;
    const assignmentDetails = await findPendingAssignmentById(assignmentId);
    // uid must match the uid of the user who is submitting the assignment (from user_assignment table)
    if (assignmentDetails[0].uid !== uid) {
        console.log("User not allowed to submit assignment for uid - ", uid);
        res.status(403).send({
          success: false,
          code: "unauthorized",
          message: "You are not authorized to submit this assignment.",
        });
        return;
      }
    
}

module.exports = {getUserAssignment};