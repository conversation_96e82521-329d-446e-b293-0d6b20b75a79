const { adminViewAssignmentDir } = require("../constants/fileStructureConstants");
const fs = require('fs');
const createZip = require("../utils/createZip");
const { cloneRepo } = require("./cloneRepo");
const path = require("path");

async function downloadZip(req, res) {
  const { repoUrl, is_admin, uid, branch } = req.body;

  if (!repoUrl) {
    return res.status(400).send("Missing repository URL");
  }

  let targetDir = null;
  const outputZipPath = adminViewAssignmentDir + `${uid}.zip`;
  try {
    targetDir = await cloneRepo({ github_link: repoUrl, uid }, null, is_admin, branch);
    await createZip(path.resolve(targetDir), path.resolve(outputZipPath));
    res.status(200).download(outputZipPath, `${uid}.zip`, (err) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('Error downloading file');
      }
    });
  } catch (error) {
    console.error('Error creating ZIP file:', error);
    res.status(500).send('Error creating ZIP file');
  } finally {
    setTimeout(() => {
      fs.unlink(outputZipPath, (unlinkErr) => {
        if (unlinkErr) {
          console.error('Error deleting file - ', uid, unlinkErr);
        } else {
          console.log('ZIP file deleted after download - ', uid);
        }
      });
    }, 1000 * 60 * 2);
  }
}

module.exports = downloadZip;
