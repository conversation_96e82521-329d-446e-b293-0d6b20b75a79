const { parseResume, cleanUpResumeFiles } = require("../utils/resumeParser.js");
const { extractDataFromJD } = require("../utils/prompts");
const { GENAI } = require("../cache/index.js");
const getJsonFromPromptWithRetries = require("../utils/getResultFromPromptWithRetries.js");
const callDataLayer = require("../utils/callDataLayer.js");
const prisma = require("../utils/prisma.js");
const { JobDescriptionData } = require("neusortlib");
const { uploadFileToAzure } = require("../azureBlobStorage/index.js");
const { filesUploadDir } = require("../constants/fileStructureConstants.js");
const path = require("path");
const busboy = require("busboy");
const fs = require("fs");
const fsPromises = fs.promises;

const getDataFromJD = async (req, res) => {
  const contentType = req.headers["content-type"];
  try {
    if (contentType && contentType.includes("multipart/form-data")) {
      let resumeFilePath = null;
      let clientDisconnected = false;

      const cleanupHandler = () => {
        console.log("Client disconnected");
        cleanUpResumeFiles(resumeFilePath);
        clientDisconnected = true;
      };

      const bb = busboy({ headers: req.headers });

      req.on("aborted", cleanupHandler);

      bb.on("field", (fieldname, value) => {
        req.body[fieldname] = value;
      });

      bb.on("file", async (fieldname, file, fileinfo) => {
        const adminId = req.body.adminId || res.locals.uid;
        resumeFilePath = path.join(
          filesUploadDir,
          adminId,
          `${Date.now()}_${fileinfo.filename}`
        );
        const dirPath = path.dirname(resumeFilePath);
        try {
          await fsPromises.mkdir(dirPath, { recursive: true });

          const writeStream = fs.createWriteStream(resumeFilePath);
          file.pipe(writeStream);

          await new Promise((resolve, reject) => {
            writeStream.on("finish", resolve);
            writeStream.on("error", reject);
            file.on("error", reject);
          });

          console.log(
            `File ${fileinfo.filename} uploaded to ${resumeFilePath} successfully`
          );
        } catch (error) {
          cleanUpResumeFiles(resumeFilePath);
          console.error(`Error uploading file ${fileinfo.filename}:`, error);
          res.status(500).send({
            success: false,
            code: "upload-error",
            message: "Something went wrong. Could not upload file.",
          });
          return;
        }
      });

      bb.on("finish", async () => {
        req.removeListener("aborted", cleanupHandler);
        if (clientDisconnected) return;
        await processJobDescription(req, res, {
          resumeFilePath,
        });
      });

      bb.on("error", async (error) => {
        await cleanUpResumeFiles(resumeFilePath);
        console.error("Error in busboy getDataFromJD:", error);
        return res.status(500).send({
          success: false,
          message: "Something went wrong.",
        });
      });

      req.pipe(bb);
    } else if (contentType && contentType.includes("application/json")) {
      await processJobDescription(req, res, {
        resumeFilePath: null,
      });
    }
  } catch (error) {
    console.error("Error", error);
    return res
      .status(500)
      .send({ success: false, msg: "Something went wrong." });
  }
};

const processJobDescription = async (req, res, { resumeFilePath }) => {
  try {
    let fileUrl = null;
    const { job_description: jdFromBody, adminId } = req.body;
    const { companyId, uid } = res.locals;

    let job_description = jdFromBody || "";

    if (resumeFilePath) {
      fileUrl = await uploadFileToAzure(
        resumeFilePath,
        `${uid}/${path.basename(resumeFilePath)}`,
        process.env.AZURE_STORAGE_RESUMES_CONTAINER_NAME
      );

      job_description = job_description.concat(
        await parseResume(resumeFilePath)
      );
    }
    if (jdFromBody) {
      job_description = (jdFromBody || "").concat("\n").concat(job_description);
    }

    if (job_description.trim() === "") {
      return res.status(400).send({
        success: false,
        message: "Resume or job description is required.",
      });
    }

    const response = await getJsonFromPromptWithRetries(
      GENAI,
      extractDataFromJD(job_description),
      "getResultFromPrompt",
      5,
      "api-call-getDataFromJD-",
      "{",
      "}"
    );

    if (!response) {
      console.log("Error while getting response for summary");
      return res.status(500).send({
        success: false,
        message: "Failed to process job description.",
      });
    }

    const jd_data = new JobDescriptionData(
      companyId
        ? (
            await callDataLayer(`/createNewJD`, companyId, "POST", {
              adminId,
              job_description,
              gpt_response: response,
              resume_url: fileUrl,
            })
          ).data
        : await prisma.jd_extracted_data.create({
            data: {
              adminId,
              job_description,
              gpt_response: response,
              resume_url: fileUrl,
            },
          })
    );

    cleanUpResumeFiles(resumeFilePath);

    if (response?.skills?.length > 0) {
      const getTechStack = companyId
        ? (
            await callDataLayer("/getTechStackByLanguages", companyId, "POST", {
              languages: response.skills,
            })
          ).data
        : await prisma.programming_language_group.findMany({
            where: {
              programming_language_group_mapping: {
                some: {
                  programming_language: {
                    programming: {
                      in: response.skills,
                    },
                  },
                },
              },
            },
            select: {
              name: true,
            },
          });

      const techStack = [...new Set(getTechStack.map((stack) => stack?.name))];

      return res.status(200).send({
        success: true,
        data: { ...response, techStack },
        jd_id: jd_data.getId(),
        resume_url: fileUrl,
      });
    } else {
      return res.status(200).send({
        success: true,
        data: response,
        jd_id: jd_data.getId(),
        resume_url: fileUrl,
      });
    }
  } catch (error) {
    await cleanUpResumeFiles(resumeFilePath);
    console.error("Error in getDataFromJD:", error);
    return res.status(500).send({
      success: false,
      message: "Something went wrong.",
    });
  }
};

module.exports = { getDataFromJD };
