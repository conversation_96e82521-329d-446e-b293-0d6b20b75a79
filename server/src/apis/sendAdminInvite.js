const {
    insertAdminInviteIntoRabbitMQ,
} = require("../utils/insertIntoRabbitMQ");
const callDataLayer = require("../utils/callDataLayer");

async function sendAdminInvite(req, res) {
    const { email, licences, roles, user } = req.body;
    const { companyId } = res.locals;

    try {
        if (!email || !roles || roles.length === 0) {
            return res.status(400).json({
                message: "Email and role IDs are required.",
            });
        }

        const inviteData = {
            email,
            licences,
            roles,
            user,
        };

        const userInviteResponse = await callDataLayer(
            "/admin/invite",
            companyId,
            "POST",
            inviteData
        );

        const inviteId = userInviteResponse.data.invite_id;

        await insertAdminInviteIntoRabbitMQ(inviteId, companyId, email);

        return res.status(200).json({ message: "Admin invitation sent" });
    } catch (error) {
        console.error("Error in sending admin invite:", error);
        return res.status(500).json({ message: "An error occurred", error });
    }
}

module.exports = { sendAdminInvite };
