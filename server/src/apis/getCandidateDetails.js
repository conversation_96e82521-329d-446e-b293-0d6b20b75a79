const { CandidateDetails } = require("neusortlib");
const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");
BigInt.prototype.toJSON = function () {
  return this.toString();
};

async function getCandidateDetails(req, res) {
  try {
    let { formId, candidateId } = req.body;
    const companyId = res.locals.companyId;
    if (!formId || !candidateId) {
      return res
        .status(400)
        .json({ error: "formId and candidateId are required" });
    }
    const candidate = companyId
      ? new CandidateDetails(
        (
          await callDataLayer(
            `/candidate?formId=${formId}&candidateId=${candidateId}`,
            companyId
          )
        ).data
      )
      : await prisma.candidate_form_mapping.findFirst({
        where: {
          formId: parseInt(formId),
          candidateId: parseInt(candidateId),
        },
        include: {
          candidate: {
            select: {
              email: true,
              name: true,
              id: true,
            },
          },
        },
      });
    return res
      .status(200)
      .json({
        candidate: companyId
          ? { candidate: candidate.returnCandidate() }
          : candidate,
        companyId: companyId,
      });
  } catch (error) {
    console.error("Error getting candidate details:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

module.exports = getCandidateDetails;
