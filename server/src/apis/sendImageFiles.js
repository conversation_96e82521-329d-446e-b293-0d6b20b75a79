const axios = require("axios");
const Busboy = require("busboy");
const FormData = require("form-data");

const sendImageFiles = (req, res) => {
  const { roomId } = req.params;
  const { uid } = res.locals;
  const contentType = req.headers["content-type"];
  if (!contentType || !contentType.includes("multipart/form-data")) {
    return res
      .status(400)
      .send({ success: false, message: "Content type must be multipart/form-data" });
  }

  let clientDisconnected = false;
  const fileBuffers = {};
  const fields = {};

  const cleanupHandler = () => {
    console.log("Client disconnected");
    clientDisconnected = true;
  };

  req.on("aborted", cleanupHandler);

  const bb = Busboy({ headers: req.headers });

  bb.on("field", (fieldname, val) => {
    fields[fieldname] = val;
  });

  bb.on("file", (fieldname, file, { filename, encoding, mimeType }) => {
    const chunks = [];

    file.on("data", (chunk) => {
      chunks.push(chunk);
    });

    file.on("end", () => {
      const buffer = Buffer.concat(chunks);
      fileBuffers[fieldname] = {
        buffer,
        filename: filename || `${fieldname}.jpg`,
        mimeType: mimeType || "image/jpeg"
      };
    });

    file.on("error", (err) => {
      console.error("Error reading file:", err);
    });
  });

  bb.on("error", (error) => {
    console.error("Busboy error:", error);
    return res.status(500).json({ message: "Upload error", error: error.toString() });
  });

  bb.on("finish", async () => {
    req.removeListener("aborted", cleanupHandler);
    if (clientDisconnected) return;

    const fileKeys = Object.keys(fileBuffers);
    if (fileKeys.length === 0) {
      return res.status(400).json({ message: "No files uploaded" });
    }

    try {
      const formData = new FormData();
      const testType = fields.testType;

      fileKeys.forEach(key => {
        const { buffer, filename, mimeType } = fileBuffers[key];
        formData.append("files", buffer, {
          filename,
          contentType: mimeType,
        });
      });

      let endpointUrl;
      if (testType === "click") {
        endpointUrl = `${process.env.DATA_PROCESS_SERVER}internal/tutorial-click/${roomId}/${uid}`;

        if (fields.clickPosition) {
          formData.append("clickPosition", fields.clickPosition);
        }
        if (fields.screenDimensions) {
          formData.append("screenDimensions", fields.screenDimensions);
        }
        if (fields.stepId) {
          formData.append("stepId", fields.stepId);
        }
        if (fields.timestamp) {
          formData.append("timestamp", fields.timestamp);
        }
      } else if (testType === "camera") {
        formData.append("createCSV", fields.createCSV);
        endpointUrl = `${process.env.DATA_PROCESS_SERVER}internal/test-camera/${roomId}/${uid}`;
      } else {
        return res.status(400).json({ message: "Invalid test type" });
      }

      const response = await axios.post(
        endpointUrl,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${process.env.STATIC_TOKEN}`,
          },
          timeout: 30000,
        }
      );

      return res.status(200).json(response.data);
    } catch (error) {
      console.error(`Error processing ${fields.testType} data:`, error.message);
      return res.status(500).json({
        message: `An error occurred while processing ${fields.testType} data`,
      });
    }
  });

  req.pipe(bb);
};

module.exports = sendImageFiles;