const fs = require("fs");
const simpleGit = require("simple-git");
const { getTargetDirForUid, getRepoPath } = require("../utils/directoryUtils");
const { PRODUCER } = require("../cache");
const { deleteDir } = require("../utils/deleteDir");

/* API CONTRACT 
{
  "repoUrl": "https://github.com/user/repo.git",
  "targetDir": "./clonedRepos/myRepo"
}
*/

const handleCloneRepoApiCall = async (req, res) => {
  const { repoUrl, targetDir, is_admin, uid, branch } = req.body;

  if (!repoUrl || !targetDir) {
    return res.status(400).send("Missing repository URL or target directory.");
  }

  try {
    await cloneRepo({ github_link: repoUrl, uid }, targetDir, is_admin, branch);
    res.status(200).json({
      uid: uid,
      msg: `Repository successfully cloned into ${targetDir}`,
    });
  } catch (error) {
    res.status(500).send("Failed to clone repository: " + error.message);
  }
};

const cloneRepo = async (dbData, targetDir, is_admin, branch) => {
  const { github_link, uid } = dbData;
  if (!targetDir) targetDir = getTargetDirForUid(uid, github_link, is_admin);

  if (fs.existsSync(targetDir)) {
    console.log(`Directory ${targetDir} already exists. Skipped cloning repo.`);
    return targetDir;
  }

  const git = simpleGit();

  try {
    if (!fs.existsSync(getRepoPath(uid, "", is_admin))) {
      fs.mkdirSync(getRepoPath(uid, "", is_admin), { recursive: true });
    }
    const gitUrl = github_link.replace(
      "https://" + process.env.AZURE_DEVOPS_ORG,
      "https://" + process.env.AZURE_DEVOPS_TOKEN
    );

    let clonedBranch = branch || "default";

    if (branch) {
      try {
        await git.clone(gitUrl, targetDir, [
          "--branch",
          branch,
          "--single-branch",
        ]);
      } catch (error) {
        if (
          error.message.includes("Remote branch") &&
          error.message.includes("not found")
        ) {
          console.log(
            `Branch '${branch}' not found. Cloning default branch instead.`
          );
          await git.clone(gitUrl, targetDir);
          clonedBranch = "default";
        } else {
          throw error;
        }
      }
    } else {
      await git.clone(gitUrl, targetDir);
    }

    console.log(
      `Branch '${clonedBranch}' successfully cloned into ${targetDir}`
    );

    if (is_admin) {
      await PRODUCER.sendDelayedMessage(
        process.env.DELETE_REPO_QUEUE,
        { dir: targetDir, removeBase: true }
      );
    }
    return targetDir;
  } catch (error) {
    console.log(
      "Failed to clone repository: " + github_link + " into " + targetDir,
      error
    );
    await deleteDir({ targetDir });
    throw error;
  }
};

module.exports = { handleCloneRepoApiCall, cloneRepo };
