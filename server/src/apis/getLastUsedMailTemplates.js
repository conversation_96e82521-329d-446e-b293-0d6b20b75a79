const callDataLayer = require("../utils/callDataLayer");

const getLastUsedMailTemplatesV1 = async (req, res) => {
  try {
    const companyId = res.locals.companyId;
    const lastJobForm = (
      await callDataLayer("/getAllJobForms", companyId, "POST", {
        orderBy: { id: "desc" },
        offset: 0,
        pageSize: 1,
        whereClause: { licence: 'v1' }
      })
    ).data;

    if (!lastJobForm || !lastJobForm.forms || lastJobForm.forms.length === 0) {
      console.log("No job forms found");
      return res.send({ success: false, message: "No templates found" });
    }

    const mailTemplates = getMailTemplates(lastJobForm);

    res.send({ success: true, mailTemplates });
  } catch (error) {
    console.log("Error in getLastUsedMailTemplates", error);
    res.status(500).send({ success: false, message: "Internal server error" });
  }
};

const getLastUsedMailTemplatesV2 = async (req, res) => {
  try {
    const companyId = res.locals.companyId;
    const lastJobForm = (
      await callDataLayer("/getAllJobForms", companyId, "POST", {
        orderBy: { id: "desc" },
        offset: 0,
        pageSize: 1,
        whereClause: { licence: 'v2' }
      })
    ).data;

    if (!lastJobForm || !lastJobForm.forms || lastJobForm.forms.length === 0) {
      console.log("No job forms found");
      return res.send({ success: false, message: "No templates found" });
    }

    const mailTemplates = getMailTemplates(lastJobForm);

    res.send({ success: true, mailTemplates });
  } catch (error) {
    console.log("Error in getLastUsedMailTemplates", error);
    res.status(500).send({ success: false, message: "Internal server error" });
  }
};

const getLastUsedMailTemplatesV3 = async (req, res) => {
  try {
    const companyId = res.locals.companyId;
    const lastJobForm = (
      await callDataLayer("/getAllJobForms", companyId, "POST", {
        orderBy: { id: "desc" },
        offset: 0,
        pageSize: 1,
        whereClause: { licence: "v3" },
      })
    ).data;

    if (!lastJobForm || !lastJobForm.forms || lastJobForm.forms.length === 0) {
      console.log("No job forms found");
      return res.send({ success: false, message: "No templates found" });
    }

    const mailTemplates = getMailTemplates(lastJobForm);

    res.send({ success: true, mailTemplates });
  } catch (error) {
    console.log("Error in getLastUsedMailTemplates", error);
    res.status(500).send({ success: false, message: "Internal server error" });
  }
};

const getLastUsedMailTemplatesV4 = async (req, res) => {
  try {
    const companyId = res.locals.companyId;

    if (!companyId) {
      return res.status(500).json("Company ID is required");
    }

    const lastJobForm = (
      await callDataLayer("/getAllJobForms", companyId, "POST", {
        orderBy: { id: "desc" },
        offset: 0,
        pageSize: 1,
        whereClause: { licence: 'v4' }
      })
    ).data;

    if (!lastJobForm || !lastJobForm.forms || lastJobForm.forms.length === 0) {
      console.log("No job forms found");
      return res.send({ success: false, message: "No templates found" });
    }

    const mailTemplates = getMailTemplates(lastJobForm);

    res.send({ success: true, mailTemplates });
  } catch (error) {
    console.log("Error in getLastUsedMailTemplates", error);
    res.status(500).send({ success: false, message: "Internal server error" });
  }
};

const getLastUsedMailTemplatesV5 = async (req, res) => {
  try {
    const companyId = res.locals.companyId;

    if (!companyId) {
      return res.status(500).json("Company ID is required");
    }

    const lastJobForm = (
      await callDataLayer("/getAllJobForms", companyId, "POST", {
        orderBy: { id: "desc" },
        offset: 0,
        pageSize: 1,
        whereClause: { licence: 'v5' }
      })
    ).data;

    if (!lastJobForm || !lastJobForm.forms || lastJobForm.forms.length === 0) {
      console.log("No job forms found");
      return res.send({ success: false, message: "No templates found" });
    }

    const mailTemplates = getMailTemplates(lastJobForm);

    res.send({ success: true, mailTemplates });
  } catch (error) {
    console.log("Error in getLastUsedMailTemplates", error);
    res.status(500).send({ success: false, message: "Internal server error" });
  }
};

function getMailTemplates(lastJobForm) {
  const mailTemplates = {
    sendAssignmentTemplate: lastJobForm.forms[0].sendAssignmentTemplate,
    interviewScheduleTemplate: lastJobForm.forms[0].interviewScheduleTemplate,
    interviewScheduleReminderTemplate: lastJobForm.forms[0].interviewScheduleReminderTemplate,
    interviewRescheduleReminderTemplate: lastJobForm.forms[0].interviewRescheduleReminderTemplate,
    interviewInviteTemplate: lastJobForm.forms[0].interviewInviteTemplate,
    closeJobFormTemplate: lastJobForm.forms[0].closeJobFormTemplate,
    sendAssignmentSubject: lastJobForm.forms[0].sendAssignmentSubject,
    interviewScheduleSubject: lastJobForm.forms[0].interviewScheduleSubject,
    interviewScheduleReminderSubject: lastJobForm.forms[0].interviewScheduleReminderSubject,
    interviewRescheduleReminderSubject: lastJobForm.forms[0].interviewRescheduleReminderSubject,
    interviewInviteSubject: lastJobForm.forms[0].interviewInviteSubject,
    closeJobFormSubject: lastJobForm.forms[0].closeJobFormSubject,
  };
  return mailTemplates;
}

module.exports = {
  getLastUsedMailTemplatesV1,
  getLastUsedMailTemplatesV2,
  getLastUsedMailTemplatesV3,
  getLastUsedMailTemplatesV4,
  getLastUsedMailTemplatesV5,
};