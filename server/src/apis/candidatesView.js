const { FormCandidates } = require("neusortlib/models");
const callDataLayer = require("../utils/callDataLayer");
const { calculateCheatingStatus } = require("../utils/calculateCheatingStatus");

const createCandidatesView = async (req, res) => {
  try {
    const { name, description, cfmIds, formId, otherAdminsId } = req.body;
    const { uid, companyId } = res.locals;

    const dataLayerResponse = await callDataLayer(
      "/admin/createCandidatesView",
      companyId,
      "POST",
      {
        name,
        description,
        cfmIds,
        formId,
        adminId: uid,
        otherAdminsId,
      }
    );

    return res.status(201).json(dataLayerResponse.data);
  } catch (error) {
    console.error("Error creating candidates view:", error.message);

    if (error.response && error.response.status !== 500) {
      return res.status(error.response.status).json({
        message:
          error.response.data?.error ||
          error.response.data?.message ||
          "Error creating candidates view",
      });
    }

    return res.status(500).json({
      message: "Something went wrong while creating candidates view.",
    });
  }
};

const getFormViews = async (req, res) => {
  try {
    const { formId } = req.body;
    const { uid, companyId } = res.locals;

    const {
      page = 1,
      pageSize = 5,
      columnFilters = "[]",
      sorting = "[]",
    } = req.query;

    const endpoint = `/admin/getFormViews?page=${page}&pageSize=${pageSize}&columnFilters=${encodeURIComponent(
      columnFilters
    )}&sorting=${encodeURIComponent(sorting)}`;

    const dataLayerResponse = await callDataLayer(endpoint, companyId, "POST", {
      formId,
      adminId: uid,
    });

    return res.status(200).json(dataLayerResponse);
  } catch (error) {
    console.error("Error in getFormViews middleware:", error.message);

    if (error.response && error.response.status !== 500) {
      return res.status(error.response.status).json({
        message:
          error.response.data?.error ||
          error.response.data?.message ||
          "Error fetching candidate views",
      });
    }

    return res.status(500).json({
      message: "Something went wrong while fetching candidate views.",
    });
  }
};

const getFormViewDetails = async (req, res) => {
  try {
    const { viewId } = req.params;
    const { uid, companyId } = res.locals;

    const dataLayerResponse = await callDataLayer(
      `/admin/getFormViewDetails`,
      companyId,
      "POST",
      {
        adminId: uid,
        viewId,
      }
    );

    return res.status(200).json(dataLayerResponse);
  } catch (error) {
    console.error("Error in getFormViewDetails middleware:", error.message);

    if (error.response && error.response.status !== 500) {
      return res.status(error.response.status).json({
        message:
          error.response.data?.error ||
          error.response.data?.message ||
          "Error fetching candidate view details",
      });
    }

    return res.status(500).json({
      message: "Something went wrong while fetching candidate view details.",
    });
  }
};

const updateCandidateNote = async (req, res) => {
  try {
    const { viewId, id } = req.params;
    const { notes } = req.body;
    const { uid, companyId } = res.locals;

    const dataLayerResponse = await callDataLayer(
      `/admin/updateCandidateNote`,
      companyId,
      "POST",
      {
        adminId: uid,
        viewId,
        id,
        notes,
      }
    );

    return res.status(200).json(dataLayerResponse);
  } catch (error) {
    console.error("Error in updateCandidateNote middleware:", error.message);

    if (error.response && error.response.status !== 500) {
      return res.status(error.response.status).json({
        message:
          error.response.data?.error ||
          error.response.data?.message ||
          "Error updating candidate note",
      });
    }

    return res.status(500).json({
      message: "Something went wrong while updating candidate note.",
    });
  }
};

const deleteCandidatesViews = async (req, res) => {
  try {
    const { viewIds } = req.body;
    const { uid, companyId } = res.locals;

    if (!viewIds) {
      return res.status(400).json({
        message: "View IDs are required",
      });
    }

    const dataLayerResponse = await callDataLayer(
      `/admin/deleteCandidatesViews`,
      companyId,
      "POST",
      {
        adminId: uid,
        viewIds,
      }
    );

    return res.status(200).json(dataLayerResponse);
  } catch (error) {
    console.error("Error in deleteCandidatesViews middleware:", error.message);

    if (error.response && error.response.status !== 500) {
      return res.status(error.response.status).json({
        message:
          error.response.data?.error ||
          error.response.data?.message ||
          "Error deleting candidate views",
      });
    }

    return res.status(500).json({
      message: "Something went wrong while deleting candidate views.",
    });
  }
};

async function getViewCandidates(req, res) {
  const { viewId } = req.body;
  const { uid, companyId } = res.locals;
  const { version } = req.params;

  let { page, pageSize, sorting, globalFilter, columnFilters } = req.query;

  try {
    if (!companyId) {
      return res.status(500).json({ message: "Company id is required" });
    }

    const candidates = await callDataLayer(
      `/admin/${version}/fetchViewCandidates`,
      companyId,
      "POST",
      {
        adminId: uid,
        viewId,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        sorting,
        columnFilters,
      }
    );

    if (!candidates.success) {
      return res.status(400).json({
        candidates: [],
        total: 0,
      });
    }

    const candidateModel = new FormCandidates(
      candidates.data.candidates,
      candidates.data.total
    );

    return res.status(200).json({
      ...candidateModel.returnFormCandidates(),
    });
  } catch (error) {
    console.error("Error fetching candidates:", error.message);
    return res.status(400).json({
      candidates: [],
      total: 0,
    });
  }
}

function processCandidates(candidates) {
  return calculateCheatingStatus(candidates);
}

function filterCandidates(candidates, cheatingStatus) {
  let filteredCandidates = candidates;
  if (cheatingStatus) {
    filteredCandidates = filteredCandidates.filter(
      (candidate) =>
        candidate.derivedAttributes.cheatingStatus === cheatingStatus
    );
  }
  return filteredCandidates;
}

module.exports = {
  getViewCandidates,
  createCandidatesView,
  getFormViews,
  getFormViewDetails,
  updateCandidateNote,
  deleteCandidatesViews,
};
