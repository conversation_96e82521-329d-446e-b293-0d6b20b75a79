const path = require('path');
const fs = require('fs');
const { cloneRepo } = require('./cloneRepo');
const { displayFileContent, listDirectory } = require('../utils/listDirectory');

async function sendProjectRepo(req, res) {
  try {
    const uid = req.params.uid;
    const requestedFilePath = req.params[0] || "";
    const reqPath = `/${uid}/${requestedFilePath || ''}`;
    const { repoUrl, is_admin, branch } = req.cookies.repoData ? JSON.parse(req.cookies.repoData) : null;

    const targetDir = await cloneRepo({ github_link: repoUrl, uid }, null, is_admin, branch);

    const assignmentPath = path.join(targetDir, requestedFilePath || "");

    if (fs.lstatSync(assignmentPath).isDirectory()) {
      const directoryList = listDirectory(assignmentPath, reqPath);
      res.send(directoryList);
    } else {
      const ext = path.extname(assignmentPath).toLowerCase();
      const is_img = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif',
        '.webp', '.heic', '.jfif', '.ico', '.raw', '.indd', '.ai', '.eps'].includes(ext);
      const is_svg = ext === '.svg';
      if (is_img) {
        const sendImg = displayFileContent(assignmentPath, reqPath, true, false);
        res.send(sendImg);
      } else {
        const fileContent = displayFileContent(assignmentPath, reqPath, false, is_svg);
        res.send(fileContent);
      }
    }
  } catch (error) {
    console.error('Error sending project repo:', error);
    res.status(500).send('Error sending project repo');
  }
}
module.exports = sendProjectRepo;