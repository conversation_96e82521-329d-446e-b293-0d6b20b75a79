const callDataLayer = require("../utils/callDataLayer");
const { sendErrorEmail } = require("../utils/insertIntoRabbitMQ");

const verifyOrder = async (req, res) => {
  try {
    const { companyId } = res.locals;
    const data = req.body;

    const orderData = await callDataLayer("/verifyOrder", companyId, "POST", data);

    return res.status(201).send({ success: true, data: orderData.data });
  } catch (error) {
    console.error('verifyOrder(): ', error);
    if (process.env.NODE_ENV === 'production') {
      sendErrorEmail({ isSendMail: true, error: error });
    }
    return res.status(500).send({
      success: false,
      msg: 'Something went wrong.',
    });
  }
};

module.exports = verifyOrder;