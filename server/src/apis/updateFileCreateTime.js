const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");

async function updateFileCreateTime(req, res) {
  try {
    const { roomId, time, companyId } = req.body;
    const data = companyId
      ? (
        await callDataLayer(
          `/updateCreateTime`, companyId, "POST", {
            roomId,
            createTime: time,
        })
      )
      : await prisma.rooms.update({
        where: {
          roomid: roomId,
        },
        data: {
          file_create_time: time,
        },
      });
    return res
      .status(200)
      .json({
        time: data.file_create_time
      });
  } catch (error) {
    console.error("Error updating time :", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

module.exports = updateFileCreateTime;
