const { StorageSharedKeyCredential, generateBlobSASQueryParameters, ContainerSASPermissions } = require('@azure/storage-blob');

const accountName = process.env.AZURE_ACCOUNT_NAME;
const accountKey = process.env.AZURE_ACCOUNT_KEY;
const containerName = process.env.AZURE_STORAGE_AUDIO_RECORDINGS_CONTAINER_NAME;
const sharedKeyCredential = new StorageSharedKeyCredential(accountName, accountKey);

async function downloadRecording(req, res) {
  try {
    const { file } = req.params;

    const expiryDate = new Date();
    expiryDate.setHours(expiryDate.getHours() + 1);

    const fileName = `${file}.mp4`

    const sasOptions = {
      containerName,
      fileName,
      permissions: ContainerSASPermissions.parse("r"),
      expiresOn: expiryDate,
    };

    const sasToken = generateBlobSASQueryParameters(sasOptions, sharedKeyCredential).toString();

    const sasUrl = `https://${accountName}.blob.core.windows.net/${containerName}/${fileName}?${sasToken}`;

    res.status(200).json({ downloadUrl: sasUrl });
  } catch (error) {
    console.error("Error generating SAS URL:", error);
    res.status(500).json({ error: "Error generating SAS URL" });
  }
}

module.exports = downloadRecording;
