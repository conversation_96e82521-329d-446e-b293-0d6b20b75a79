const { JobFormDetails } = require("neusortlib");
const callDataLayer = require("../utils/callDataLayer");
const {
  insertFormDataToRabbitMQ,
  insertCustomSkillsIntoRabbitMQ,
  insertV3FormDataToRabbitMQ,
  insertV2FormDataToRabbitMQ,
  insertV4FormDataToRabbitMQ,
  insertV5FormDataToRabbitMQ,
} = require("../utils/insertIntoRabbitMQ");

async function sendJobFormDetailsV1(req, res) {
  try {
    const { formData, formId } = req.body;
    const { templatesV1 } = formData;

    const { companyId } = res.locals;

    if (companyId) {
      const jobFormResult = await callDataLayer(
        "/admin/v1/jobForm",
        companyId,
        "POST",
        {
          ...formData,
          ...templatesV1,
          formId,
        }
      );
      console.log(
        "jobFormResult from data layer:",
        JSON.stringify(jobFormResult, null, 2)
      );
      const jobForm = new JobFormDetails(
        jobFormResult.data.company,
        jobFormResult.data.jobForm
      );
      const {
        skillIds,
        queueExperience,
        addCandidatesToForm,
        programmingSkills: { skills },
        programmingCustomSkills: { skills: customSkills },
      } = jobForm.getJobForm().returnJobForm();

      const nonApprovedSkills = jobForm
        .getJobForm()
        .getProgrammingCustomSkills()
        .getSkills()
        .filter((skill) => !skill.approved);
      if (!nonApprovedSkills?.length) {
        console.log("generate assignment:", {
          candidateFormId: addCandidatesToForm[0].candidateFormId,
          name: addCandidatesToForm[0].name,
          email: addCandidatesToForm[0].email,
          languageIds: skillIds,
          experience: queueExperience,
          companyId: companyId,
          sendAssignmentTemplate: templatesV1.sendAssignmentTemplate,
          sendAssignmentSubject: templatesV1.sendAssignmentSubject,
        });

        await Promise.all(
          addCandidatesToForm.map((candidate) =>
            insertFormDataToRabbitMQ({
              candidateFormId: candidate.candidateFormId,
              name: candidate.name,
              email: candidate.email,
              languageIds: Array.from(
                new Set(
                  [...skills, ...customSkills].map(
                    (skill) => skill.programming_language.language_id
                  )
                )
              ),
              experience: queueExperience,
              companyId: companyId,
              sendAssignmentTemplate: templatesV1.sendAssignmentTemplate,
              sendAssignmentSubject: templatesV1.sendAssignmentSubject,
              skillIds: skills.map((skill) => skill.id),
              customSkillIds: customSkills.map((skill) => skill.id),
            })
          )
        );
      } else {
        insertCustomSkillsIntoRabbitMQ({
          skillIds: nonApprovedSkills.map((skill) => skill.id),
          formId: jobForm.getJobForm().getId(),
          companyId: companyId,
        });
      }
      return res
        .status(200)
        .json({ message: "Form Submitted", data: jobFormResult.data.company });
    } else {
      return res.status(500).json({ message: "Company not found" });
    }
  } catch (error) {
    console.error("sendJobFormDetailsV1 Error", error);
    return res.status(500).send({
      success: false,
      msg: "Try Again",
    });
  }
}

async function sendJobFormDetailsV2(req, res) {
  const { formData, formId } = req.body;
  const { templatesV2 } = formData;
  const { companyId } = res.locals;

  await sendJobFormDetailsByVersion(
    res,
    companyId,
    formData,
    templatesV2,
    formId,
    "v2",
    null
  );
}

async function sendJobFormDetailsV3(req, res) {
  const { formData, formId, paymentDetails } = req.body;
  const { templatesV3 } = formData;
  const { companyId } = res.locals;

  await sendJobFormDetailsByVersion(
    res,
    companyId,
    formData,
    templatesV3,
    formId,
    "v3",
    paymentDetails,
  );
}

async function sendJobFormDetailsV4(req, res) {
  const { formData, formId } = req.body;
  const { templatesV4 } = formData;
  const { companyId } = res.locals;

  await sendJobFormDetailsByVersion(
    res,
    companyId,
    formData,
    templatesV4,
    formId,
    "v4",
    null,
  );
}

async function sendJobFormDetailsV5(req, res) {
  const { formData, formId } = req.body;
  const { templatesV5 } = formData;
  const { companyId } = res.locals;

  await sendJobFormDetailsByVersion(
    res,
    companyId,
    formData,
    templatesV5,
    formId,
    "v5",
    null,
  );
}

const sendJobFormDetailsByVersion = async (
  res,
  companyId,
  formData,
  templates,
  formId,
  version,
  paymentDetails = null,
) => {
  try {
    if (!companyId) {
      return res.status(500).json({ message: "Company id is required" });
    }
    const jobFormResult = await callDataLayer(
      `/admin/${version}/jobForm`,
      companyId,
      "POST",
      {
        ...formData,
        ...templates,
        ...(paymentDetails ? { paymentDetails } : {}),
        formId,
      }
    );
    
    const jobForm = new JobFormDetails(
      jobFormResult.data.company,
      jobFormResult.data.jobForm
    );

    const nonApprovedSkills = jobForm
      .getJobForm()
      .getProgrammingCustomSkills()
      .getSkills()
      .filter((skill) => !skill.approved);
    if (!nonApprovedSkills?.length) {
      switch (version) {
        case "v2":
        case "v3":
          await insertV3FormDataToRabbitMQ({
            formId: jobForm.getJobForm().getId(),
            companyId,
          });
          break;
        case "v4":
          await insertV4FormDataToRabbitMQ({
            formId: jobForm.getJobForm().getId(),
            companyId,
          });
          break;
        case "v5":
          await insertV5FormDataToRabbitMQ({
            formId: jobForm.getJobForm().getId(),
            companyId,
          });
          break;
      }
    } else {
      insertCustomSkillsIntoRabbitMQ({
        skillIds: nonApprovedSkills.map((skill) => skill.id),
        formId: jobForm.getJobForm().getId(),
        companyId: companyId,
      });
    }
    return res
      .status(200)
      .json({ message: "Form Submitted", data: jobFormResult.data.company });
  } catch (error) {
    console.error("sendJobFormDetails Error", error);
    return res.status(500).send({
      success: false,
      msg: "Try Again",
    });
  }
}

module.exports = {
  sendJobFormDetailsV1,
  sendJobFormDetailsV2,
  sendJobFormDetailsV3,
  sendJobFormDetailsV4,
  sendJobFormDetailsV5,
};
