const callDataLayer = require("../utils/callDataLayer");
const {
  insertFormDataToRabbitMQ,
  insertCandidatesFormDataToRabbitMQ,
} = require("../utils/insertIntoRabbitMQ");
const prisma = require("../utils/prisma");

const isValidEmail = (email) => {
  const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return regex.test(email);
};

async function addCandidatesToFormV1(req, res) {
  try {
    const { formId, candidates, langIds } = req.body;
    const { companyId } = res.locals;

    const { total, candidateData, jobFormDetails, companyData } = companyId
      ? (
          await callDataLayer("/admin/addCandidatesToForm", companyId, "POST", {
            formId,
            candidates,
          })
        ).data
      : await prisma.$transaction(async (tx) => {
          const jobFormDetails = await tx.job_form.findUnique({
            where: {
              id: formId,
            },
          });
          if (!jobFormDetails) {
            console.log("No job form found");
            return { total: 0, candidateData: [], jobFormDetails: {} };
          }

          const candidateMap = new Map();

          for (const candidate of candidates) {
            if (!isValidEmail(candidate.email)) {
              console.warn("Invalid email found:", candidate.email);
              continue;
            }

            if (!candidateMap.has(candidate.email)) {
              const existingCandidate = await tx.candidate.findUnique({
                where: {
                  email: candidate.email,
                },
              });

              if (!existingCandidate) {
                const newCandidate = await tx.candidate.create({
                  data: {
                    name: candidate.name,
                    email: candidate.email,
                  },
                });
                candidateMap.set(candidate.email, {
                  id: newCandidate.id,
                  name: candidate.name,
                  email: candidate.email,
                  phoneNo: candidate.phoneNo.toString(),
                });
              } else {
                candidateMap.set(candidate.email, {
                  id: existingCandidate.id,
                  name: candidate.name,
                  email: candidate.email,
                  phoneNo: candidate.phoneNo.toString(),
                });
              }
            }
          }

          const filteredCandidates = await Promise.all(
            Array.from(candidateMap.values()).map(async (c) => {
              const find = await tx.candidate_form_mapping.findFirst({
                where: {
                  formId: formId,
                  candidate: {
                    email: c.email,
                  },
                },
              });
              if (!find) {
                return c;
              }
              return undefined;
            })
          );
          const candidatesToAdd = filteredCandidates.filter(
            (c) => c !== undefined
          );
          total = candidatesToAdd?.length;

          const addCandidatesToForm = await Promise.all(
            candidatesToAdd.map(({ id, name, email, phoneNo }) =>
              tx.candidate_form_mapping
                .create({
                  data: {
                    candidateId: id,
                    formId: formId,
                    candidate_phoneNo: phoneNo,
                  },
                })
                .then((res) => ({ id: res.id, name, email }))
            )
          );
          return { total, candidateData: addCandidatesToForm, jobFormDetails };
        });

    await Promise.all(
      candidateData.map((candidate) =>
        insertFormDataToRabbitMQ({
          candidateFormId: candidate.id,
          name: candidate.name,
          email: candidate.email,
          languageIds: langIds,
          experience: jobFormDetails.experience.split("-")[1],
          companyId,
          sendAssignmentTemplate: jobFormDetails.send_assignment_template,
          sendAssignmentSubject: jobFormDetails.send_assignment_subject,
        })
      )
    );
    return res.status(200).json({
      message: `Added ${total} Candidates`,
      data: { ...companyData, totalCandidates: total },
    });
  } catch (error) {
    console.log("addCandidatesToFormV1 Error:", error);
    return res.status(400).json({ message: "Try Again" });
  }
}

async function addCandidatesToFormCommon(req, res, version) {
  try {
    const { formId, candidates } = req.body;
    const { companyId } = res.locals;

    if (!companyId) {
      return res.status(500).json("Company ID is required");
    }

    const { total, companyData } =
      (await callDataLayer("/admin/addCandidatesToForm", companyId, "POST", {
        formId,
        candidates,
      })
      ).data;

    await insertCandidatesFormDataToRabbitMQ(
      {
        formId,
        companyId,
      },
      version
    );

    return res.status(200).json({
      message: `Added ${total} Candidates`,
      data: { ...companyData, totalCandidates: total },
    });
  } catch (error) {
    console.log(`addCandidatesToForm${version.toUpperCase()} Error:`, error);
    return res.status(400).json({ message: "Try Again" });
  }
}

async function addCandidatesToFormV2(req, res) {
  return addCandidatesToFormCommon(req, res, "v2");
}

async function addCandidatesToFormV3(req, res) {
  return addCandidatesToFormCommon(req, res, "v3");
}

async function addCandidatesToFormV4(req, res) {
  return addCandidatesToFormCommon(req, res, "v4");
}

async function addCandidatesToFormV5(req, res) {
  return addCandidatesToFormCommon(req, res, "v5");
}

module.exports = {
  addCandidatesToFormV1,
  addCandidatesToFormV2,
  addCandidatesToFormV3,
  addCandidatesToFormV4,
  addCandidatesToFormV5,
};
