const {
  storeInRealtimeDatabase,
  getFromRealtimeDatabase,
} = require("../firebase");

async function sendRecommendedQuestions(req, res) {
  const { firebaseLink, recQuestions } = req.body;
  try {
    const response = await getFromRealtimeDatabase(firebaseLink);
    if (response.recommendedQuestions) {
      return res
        .status(409)
        .json({ message: "Questions have already been set" });
    }
    const storingQuestions = await storeInRealtimeDatabase(
      `/${firebaseLink}/recommendedQuestions`,
      recQuestions
    );
    console.log(firebaseLink, recQuestions);
    return res.status(200).json({ message: "Questions successfully sent" });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: error });
  }
}

module.exports = sendRecommendedQuestions;
