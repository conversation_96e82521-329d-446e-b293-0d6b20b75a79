const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");

const fillFeedback = async (req, res) => {
  const { roomid, feedback } = req.body;
  const { uid, companyId } = await res.locals;

  try {
    if (companyId) {
      await callDataLayer("/feedback", companyId, "POST", {
        roomid,
        feedback,
        uid,
      });
    } else {
      const feedbackRecord = await prisma.feedbacks2.create({
        data: {
          roomid,
          uid,
        },
      });

      await prisma.feedbackquestion.createMany({
        data: feedback.map(({ questionId, answer }) => ({
          feedbackId: feedbackRecord.id,
          questionId,
          answer,
        })),
      });
    }

    res.status(200).send({
      success: true,
      message: "Feedback saved successfully.",
    });
  } catch (err) {
    console.error("Error while saving feedback - ", err);
    res.status(500).send({
      success: false,
      message: "Something went wrong. Could not save feedback.",
    });
  }
};

module.exports = fillFeedback;
