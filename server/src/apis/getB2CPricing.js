const callDataLayer = require("../utils/callDataLayer");

const getB2CPricing = async (req, res) => {
  try {
    const data = await callDataLayer("/public/B2CPricing", process.env.TECHYRR_COMPANY_ID, "GET");
    return res.status(200).json({
      success: true,
      data: data.data,
    });
  } catch (error) {
    console.error("Error fetching roles:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch roles. Please try again later.",
    });
  }
};

module.exports = { getB2CPricing };
