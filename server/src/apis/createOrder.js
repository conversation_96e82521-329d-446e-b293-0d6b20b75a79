const callDataLayer = require("../utils/callDataLayer");

const createOrder = async (req, res) => {
  try {
    const { companyId } = res.locals;
    const data = req.body;

    const orderData = await callDataLayer("/createOrder", companyId, "POST", data);

    return res.status(201).send({ success: true, data: orderData.data });
  } catch (error) {
    console.error('createOrder(): ', error);
    return res.status(500).send({
      success: false,
      msg: 'Something went wrong.',
    });
  }
};

module.exports = createOrder;