const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");

const getFeedbackQuestions = async (req, res) => {
    const { uid, companyId } = res.locals;
    try {
        let data;
        if (companyId) {
            data = await callDataLayer("/getFeedbackQuestions", companyId, "GET");
            return res.status(200).json({
                success: true,
                questions: data.questions,
            });
        } else {
            data = await prisma.feedback_questions.findMany({
                where: {
                    deleted: false, 
                },
                select: {
                    id:true,
                    text: true,
                    type: true, 
                },
            });
        }
        return res.status(200).json({
            success: true,
            questions: data,
        });

    } catch (error) {
        console.error("Error fetching questions:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to fetch questions. Please try again later.",
        });
    }
};

module.exports = { getFeedbackQuestions };
