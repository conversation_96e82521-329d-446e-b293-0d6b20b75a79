const { insertTimeSlot, createRoom, promisePool } = require("../database");
const { storeInRealtimeDatabase } = require("../firebase");
const generateRoomId = require("../utils/generateRoomId");

const scheduleInterview = async (req, res) => {
  const {
    startTime,
    endTime,
    isDemo,
    demoLength,
    userEmail,
    languageId,
    submissionId,
    firebaseLink,
    recommendedQuestions,
  } = req.body;
  const roomId = generateRoomId();
  const createdBy = res.locals.email;
  const languageType = languageId;

  if (!startTime || !endTime || !userEmail || !languageId) {
    return res.status(400).send("Missing required fields.");
  }

  try {
    await createRoom(promisePool, {
      roomId,
      languageId,
      languageType,
      userEmail,
      createdBy,
      isDemo,
      demoLength: demoLength || 0,
      submissionId,
    });
    await insertTimeSlot(roomId, startTime, endTime);
    await storeInRealtimeDatabase(`/${firebaseLink}/recommendedQuestions`, recommendedQuestions);
    res.status(200).json({
      success: true,
      roomId,
      message: "Interview scheduled successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error,
      message: "Error scheduling interview",
    });
  }
};

module.exports = scheduleInterview;
