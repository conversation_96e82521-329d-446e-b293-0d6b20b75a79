const prisma = require("../utils/prisma");
const { getFromRealtimeDatabase } = require("../firebase");
const { CandidateAssessmentDetails } = require("neusortlib");
const callDataLayer = require("../utils/callDataLayer");
const isCompanyTechyrr = require("../utils/checkTechyrrCompany");

async function getCandidateAssignmentV1(req, res) {
  try {
    const { formId, candidateId } = req.body;
    const { companyId } = res.locals;

    let assignment, submissionData, room, interviewStats;
    if (companyId) {
      const result = await callDataLayer(
        `/admin/v1/candidateAssessmentDetails/formId/${formId}/${candidateId}`,
        companyId
      );

      console.log(JSON.stringify(result));

      const candidateAssessmentData = new CandidateAssessmentDetails(
        result.data.candidate,
        result.data.assignment,
        result.data.submissionData,
        result.data.room
      );

      if (!candidateAssessmentData.getCandidate().getId()) {
        return res.status(404).json({
          assignment: {
            id: null,
            problemStatement: null,
            deadline: null,
          },
          message: null,
        });
      }

      if (!candidateAssessmentData.getAssignment().getId()) {
        return res.status(404).json({
          assignment: {
            id: null,
            problemStatement: null,
            deadline: null,
          },
          message: null,
        });
      }

      if (!candidateAssessmentData.getSubmissionData().getId()) {
        return res.status(200).json({
          assignment: candidateAssessmentData
            .getAssignment()
            .returnAssignment(),
          message: "Assignment sent",
        });
      }

      if (!candidateAssessmentData.getSubmissionData().getIsParsed()) {
        return res.status(200).json({
          assignment: candidateAssessmentData
            .getAssignment()
            .returnAssignment(),
          message: "Assignment sent",
        });
      }

      const response = await getFromRealtimeDatabase(
        `${candidateAssessmentData.getSubmissionData().getFirebaseLink()}`
      );

      submissionData = {
        ...candidateAssessmentData.getSubmissionData().returnSubmissionData(),
        questionsV2: response.questionsV2,
        questions: response.questions,
        date: response.date,
        unimplementedFeatures: response.unimplementedFeatures,
        expectedFeatures: response.expectedFeatures,
        recommendedQuestions:
          response.recommendedQuestions &&
            response.recommendedQuestions.length > 0
            ? response.recommendedQuestions
            : null,
      };

      if (!response) {
        return res.status(200).json({
          assignment: candidateAssessmentData
            .getAssignment()
            .returnAssignment(),
          message: "Assignment sent",
        });
      }

      if (!candidateAssessmentData.getRoom().getId()) {
        return res.status(200).json({
          assignment: candidateAssessmentData
            .getAssignment()
            .returnAssignment(),
          submissionData,
          message: "Assignment submitted",
        });
      }

      if (!candidateAssessmentData.getRoom().getInterviewTaken()) {
        return res.status(200).json({
          assignment: candidateAssessmentData
            .getAssignment()
            .returnAssignment(),
          submissionData,
          roomData: candidateAssessmentData.getRoom().returnRoomDetails(),
          message: "Interview scheduled",
        });
      }

      if (
        !candidateAssessmentData
          .getRoom()
          .getInterviewStats()
          .returnInterviewStats()
      ) {
        return res.status(200).json({
          assignment: candidateAssessmentData
            .getAssignment()
            .returnAssignment(),
          submissionData,
          roomData: candidateAssessmentData.getRoom().returnRoomDetails(),
          message: "Interview scheduled",
        });
      }

      return res.status(200).json({
        assignment: candidateAssessmentData.getAssignment().returnAssignment(),
        submissionData,
        roomData: candidateAssessmentData.getRoom().returnRoomDetails(),
        interviewStats: candidateAssessmentData
          .getRoom()
          .getInterviewStats()
          .returnInterviewStats(),
        message: "Interview done",
      });
    } else {
      const candidateForm = await prisma.candidate_form_mapping.findFirst({
        where: {
          candidateId: BigInt(candidateId),
          formId: BigInt(formId),
        },
      });

      if (!candidateForm) {
        return res.status(404).json({
          assignment: {
            id: null,
            problemStatement: null,
            deadline: null,
          },
          message: null,
        });
      }

      // getting assignment details
      const applicationId = parseInt(candidateForm.id);
      assignment = await prisma.user_assignments.findFirst({
        where: {
          applicationid: applicationId,
        },
        select: {
          id: true,
          problemStatement: true,
          deadline: true,
        },
      });
      // console.log(assignment);

      if (!assignment) {
        return res.status(404).json({
          assignment: {
            id: null,
            problemStatement: null,
            deadline: null,
          },
          message: null,
        });
      }
      // getting submission details
      const submission = await prisma.assignment_submissions.findFirst({
        where: {
          user_assignment_id: assignment.id.toString(),
        },
      });

      if (!submission) {
        return res.status(200).json({
          assignment,
          message: "Assignment sent",
        });
      }

      if (submission.isParsed !== 1) {
        return res.status(200).json({
          assignment,
          message: "Assignment sent",
        });
      }

      // getting submission data from firebase
      const response = await getFromRealtimeDatabase(
        `${submission.firebase_link}`
      );
      submissionData = {
        firebaseLink: submission.firebase_link,
        githubLink: submission.github_link,
        score: submission.score,
        questions: response.questions,
        date: response.date,
        unimplementedFeatures: response.unimplementedFeatures,
        expectedFeatures: response.expectedFeatures,
        recommendedQuestions:
          response.recommendedQuestions &&
            response.recommendedQuestions.length > 0
            ? response.recommendedQuestions
            : null,
      };

      // console.log(response);
      if (!response) {
        return res.status(200).json({
          assignment,
          message: "Assignment sent",
        });
      }

      //Getting room details
      room = await prisma.rooms.findFirst({
        where: {
          submission_id: submission.id,
        },
        include: {
          time_slots: true,
        },
      });

      if (!room) {
        return res.status(200).json({
          assignment,
          submissionData,
          message: "Assignment submitted",
        });
      }
      // console.log(room);

      if (room.interview_taken === false) {
        return res.status(200).json({
          assignment,
          submissionData,
          roomData: {
            roomId: room?.time_slots[0]?.roomid,
            startTime: room?.time_slots[0]?.start_time,
            endTime: room?.time_slots[0]?.end_time,
          },
          message: "Interview scheduled",
        });
      }

      interviewStats = await prisma.rooms.findFirst({
        where: {
          roomid: room.roomid,
        },
        select: {
          coding_conversation: {
            select: {
              question: true,
              answer: true,
              coding_hints: {
                select: {
                  hint: true,
                  query: true,
                },
              },
              coding_conversation_ratings: {
                select: {
                  rating_value: true,
                  rating_reason: true,
                },
              },
            },
          },
          summary: {
            select: {
              summary: true,
            },
          },
          conversation: {
            select: {
              text: true,
              conversation_ratings: {
                select: {
                  rating_value: true,
                  rating_reason: true,
                  ratings: {
                    select: {
                      rating_title: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      let codingConvo = [];
      let codingQuestions = [];

      for (let i = 0; i < interviewStats.coding_conversation.length; i++) {
        let convo = interviewStats.coding_conversation[i];

        if (convo.answer === null) {
          if (!codingQuestions.includes(convo.question)) {
            codingConvo.push(convo);
            codingQuestions.push(convo.question);
          }
        } else {
          codingConvo.push(convo);
        }
      }
      interviewStats.coding_conversation = codingConvo;

      if (!interviewStats) {
        return res.status(200).json({
          assignment,
          submissionData,
          roomData: {
            roomId: room.time_slots[0].roomid,
            startTime: room.time_slots[0].start_time,
            endTime: room.time_slots[0].end_time,
          },
          message: "Interview scheduled",
        });
      }
    }

    return res.status(200).json({
      assignment,
      submissionData,
      roomData: {
        roomId: room.time_slots[0].roomid,
        startTime: room.time_slots[0].start_time,
        endTime: room.time_slots[0].end_time,
        file_create_time: room.file_create_time,
      },
      interviewStats,
      message: "Interview done",
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "Internal server error" });
  }
}

async function getCandidateAssignmentV2(req, res) {
  const { formId, candidateId } = req.body;
  const { companyId } = res.locals;

  await getCandidateAssignmentByVersion(
    res,
    formId,
    candidateId,
    companyId,
    "v2"
  );
}

async function getCandidateAssignmentV4(req, res) {
  const { formId, candidateId } = req.body;
  const { companyId } = res.locals;

  await getCandidateAssignmentByVersion(
    res,
    formId,
    candidateId,
    companyId,
    "v4"
  );
}

async function getCandidateAssignmentV5(req, res) {
  const { formId, candidateId } = req.body;
  const { companyId } = res.locals;

  await getCandidateAssignmentByVersion(
    res,
    formId,
    candidateId,
    companyId,
    "v5"
  );
}

module.exports = { 
  getCandidateAssignmentV1,
  getCandidateAssignmentV2,
  getCandidateAssignmentV4,
  getCandidateAssignmentV5,
};

const getCandidateAssignmentByVersion = async (
  res,
  formId,
  candidateId,
  companyId,
  version,
) => {
  try {
    let submissionData;

    if (!companyId) {
      return res.status(500).json({ message: "Company id is required" });
    }

    const result = await callDataLayer(
      `/admin/${version}/candidateAssessmentDetails/formId/${formId}/${candidateId}`,
      companyId
    );

    const candidateAssessmentData = new CandidateAssessmentDetails(
      result.data.candidate,
      result.data.assignment,
      result.data.submissionData,
      result.data.room
    );

    if (!candidateAssessmentData.getCandidate().getId()) {
      return res.status(404).json({
        assignment: {
          id: null,
          problemStatement: null,
          deadline: null,
        },
        message: null,
      });
    }

    if (!candidateAssessmentData.getAssignment().getId()) {
      return res.status(404).json({
        assignment: {
          id: null,
          problemStatement: null,
          deadline: null,
        },
        message: null,
      });
    }

    if (!candidateAssessmentData.getSubmissionData().getId()) {
      return res.status(200).json({
        assignment: candidateAssessmentData
          .getAssignment()
          .returnAssignment(),
        message: "Project sent",
      });
    }

    if (!candidateAssessmentData.getSubmissionData().getIsParsed()) {
      return res.status(200).json({
        assignment: candidateAssessmentData
          .getAssignment()
          .returnAssignment(),
        message: "Project sent",
      });
    }

    const response = await getFromRealtimeDatabase(
      `${candidateAssessmentData.getSubmissionData().getFirebaseLink()}`
    );
    const branch = candidateAssessmentData.getSubmissionData().getBranch();

    submissionData = {
      ...candidateAssessmentData.getSubmissionData().returnSubmissionData(),
      questions: response.questions,
      date: response.date,
      unimplementedFeatures: response.unimplementedFeatures,
      expectedFeatures: response.expectedFeatures,
      branch: isCompanyTechyrr(companyId) ? `${branch}-without-comments` : branch,
      recommendedQuestions: isCompanyTechyrr(companyId)
        ? null
        : (response.recommendedQuestions && response.recommendedQuestions.length > 0
          ? response.recommendedQuestions
          : null),
    };

    if (!response) {
      return res.status(200).json({
        assignment: candidateAssessmentData
          .getAssignment()
          .returnAssignment(),
        message: "Project sent",
      });
    }

    if (!candidateAssessmentData.getRoom().getId()) {
      return res.status(200).json({
        assignment: candidateAssessmentData
          .getAssignment()
          .returnAssignment(),
        submissionData,
        message: "Project sent",
      });
    }

    if (!candidateAssessmentData.getRoom().getInterviewTaken()) {
      return res.status(200).json({
        assignment: candidateAssessmentData
          .getAssignment()
          .returnAssignment(),
        submissionData,
        roomData: candidateAssessmentData.getRoom().returnRoomDetails(),
        message: "Interview scheduled",
      });
    }

    if (
      !candidateAssessmentData
        .getRoom()
        .getInterviewStats()
        .returnInterviewStats()
    ) {
      return res.status(200).json({
        assignment: candidateAssessmentData
          .getAssignment()
          .returnAssignment(),
        submissionData,
        roomData: candidateAssessmentData.getRoom().returnRoomDetails(),
        message: "Interview scheduled",
      });
    }

    return res.status(200).json({
      assignment: candidateAssessmentData.getAssignment().returnAssignment(),
      submissionData,
      roomData: candidateAssessmentData.getRoom().returnRoomDetails(),
      interviewStats: candidateAssessmentData
        .getRoom()
        .getInterviewStats()
        .returnInterviewStats(),
      message: "Interview done",
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "Internal server error" });
  }
}