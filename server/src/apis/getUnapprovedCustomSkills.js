const callDataLayer = require("../utils/callDataLayer");

const getUnapprovedCustomSkills = async (req, res) => {
  try {
    const { formId, companyId } = req.params;

    const jobForm = (
      await callDataLayer(`/getJobForm/${formId}`, companyId, "GET")
    ).data;

    res.status(200).json({
      data: jobForm.programmingCustomSkills.skills,
    });
  } catch (error) {
    console.log(error);
    res.status(500).send("internal-server-error");
  }
};

module.exports = getUnapprovedCustomSkills;
