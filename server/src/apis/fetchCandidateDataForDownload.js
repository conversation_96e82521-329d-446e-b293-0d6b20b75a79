const callDataLayer = require("../utils/callDataLayer");
const { sendDownloadCandidateDataForEmailToRabbitMQ } = require("../utils/insertIntoRabbitMQ");

async function fetchCandidateDataForDownload({ formId, companyId, receiverEmail, dashboardType }) {
    try {
        if (!receiverEmail) {
            throw new Error("receiverEmail is required but missing.");
        }

        let candidateData;
        if (dashboardType == "v1") {
            candidateData = await callDataLayer(
                "/admin/v1/fetchFormCandidates",
                companyId,
                "POST",
                { formId }
            );
        }
        else {
            candidateData = await callDataLayer(
                "/admin/v2/fetchFormCandidates",
                companyId,
                "POST",
                { formId }
            );
        }


        if (!candidateData?.data?.candidates || candidateData.data.candidates.length === 0) {
            console.warn("No candidates found for formId:", formId);
            return;
        }

        await sendDownloadCandidateDataForEmailToRabbitMQ(candidateData.data.candidates, receiverEmail,dashboardType);
    } catch (error) {
        console.error(`Failed to fetch candidate data for receiver: ${receiverEmail}`, error);
    }
}

module.exports = fetchCandidateDataForDownload;
