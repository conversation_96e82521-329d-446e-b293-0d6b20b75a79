const axios = require("axios");
async function getSpeechToken(req, res, next) {
  console.log("Fetching speech token");
  res.setHeader("Content-Type", "application/json");
  const speechKey = process.env.AZURE_SPEECH_KEY;
  const speechRegion = process.env.AZURE_SPEECH_REGION;

  console.log("speechKey", speechKey);
  console.log("speechRegion", speechRegion);

  if (!speechKey || !speechRegion) {
    res.status(400).send("You forgot to add your speech key or region to the .env file.");
  } else {
    const headers = {
      headers: {
        "Ocp-Apim-Subscription-Key": speechKey,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    };

    try {
      const tokenResponse = await axios.post(
        `https://${speechRegion}.api.cognitive.microsoft.com/sts/v1.0/issueToken`,
        null,
        headers
      );
      console.log("Token fetched from back-end: " + tokenResponse.data, speechRegion);
      res.send({ token: tokenResponse.data, region: speechRegion, expiresIn: 600 });
    } catch (err) {
      res.status(401).send("There was an error authorizing your speech key.");
    }
  }
}

module.exports = { getSpeechToken };
