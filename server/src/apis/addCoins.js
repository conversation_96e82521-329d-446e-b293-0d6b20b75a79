const callDataLayer = require("../utils/callDataLayer");

async function addCoins(req, res) {
  const { companyId, coins } = req.body;
  try {
    const data = await callDataLayer("/superAdmin/addCoins", companyId, "POST", { coins });
    return res.status(200).json({ message: data.message });
  } catch (error) {
    console.error("Error adding coins:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
}
module.exports = addCoins;
