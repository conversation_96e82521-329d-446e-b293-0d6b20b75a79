const { storeEmbeddings } = require("../database");

const storeVoiceEmbeddings = async (req, res) => {
	try {
		const { uid, interview_id, embeddings } = req.body;
		await storeEmbeddings(uid, interview_id, embeddings);
		return res.status(200).send({ success: true, msg: "Embeddings stored successfully" });
	} catch (error) {
		console.error("Error executing query", error);
		return res.status(500).send({ success: false, msg: "Something went wrong." });
	}
};

module.exports = { storeVoiceEmbeddings };
