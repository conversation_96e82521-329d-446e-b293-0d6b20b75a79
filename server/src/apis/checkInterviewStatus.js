const { checkInterviewTaken } = require("../database");
const callDataLayer = require("../utils/callDataLayer");

const checkInterviewStatus = async (req, res) => {
  try {
    const { companyId, roomId } = req.body;
    const data = companyId ? (await callDataLayer(`/checkInterviewStatus/${roomId}`, companyId)).data : await checkInterviewTaken(roomId);
    res.status(200).json({ success: true, interview_taken: data.interview_taken || false });
  } catch (error) {
    console.log("Error fetching interview status: ", error);
    res.status(500).json({ success: false, message: "internal-server-error" });
  }
}

module.exports = checkInterviewStatus;