const fs = require("fs").promises;
const path = require("path");

const {
  fileStructureConstants,
  defaultAssignmentsDir,
} = require("../constants/fileStructureConstants");
const { cloneRepo } = require("./cloneRepo");
const { findSubmittedAssignmentById } = require("../database");
const { getTargetDirForUid } = require("../utils/directoryUtils");
const callDataLayer = require("../utils/callDataLayer");

const excludeDirs = ["node_modules"];

async function getAssignments(req, res) {
  try {
    const rootDir = path.join(defaultAssignmentsDir);
    const structure = await readDirectory(rootDir);
    res.json(structure);
  } catch (error) {
    res.status(500).send(error.message);
  }
}

async function getAssignmentStructure(assignmentId, companyId) {
  try {
    const dbData = companyId
      ? [
          (
            await callDataLayer(
              `/submittedAssignment/${assignmentId}`,
              companyId
            )
          ).data,
        ]
      : await findSubmittedAssignmentById(assignmentId);

    if (!dbData.length) return null;
    await cloneRepo(dbData[0]);
    const { uid, github_link } = dbData[0];
    const rootDir = getTargetDirForUid(uid, github_link);
    const structure = await readDirectory(rootDir);
    console.log("Structure of directory", rootDir, "is", structure);
    return structure;
  } catch (error) {
    console.log(
      "Error while fetching assignment structure for assignment id - ",
      assignmentId,
      error
    );
    return null;
  }
}

// Function to recursively read directory contents
async function readDirectory(dir) {
  const dirents = await fs.readdir(dir, { withFileTypes: true });
  // Sort dirents array alphabetically and directories first
  dirents.sort((a, b) => {
    if (a.isDirectory() === b.isDirectory()) {
      return a.name.localeCompare(b.name); // Sort by name if both are files or both are directories
    }
    return a.isDirectory() ? -1 : 1; // Directories come first
  });
  let files = await Promise.all(
    dirents.map((dirent) => {
      const res = path.resolve(dir, dirent.name);
      if (excludeDirs.indexOf(dirent.name) != -1) return null;
      if (dirent.name.startsWith(".")) return null;
      return dirent.isDirectory()
        ? readDirectory(res)
        : { name: dirent.name, type: fileStructureConstants.FILE };
    })
  );
  files = files.filter((f) => f);
  return {
    name: path.basename(dir),
    type: fileStructureConstants.FOLDER,
    items: files,
  };
}

module.exports = { getAssignments, getAssignmentStructure };
