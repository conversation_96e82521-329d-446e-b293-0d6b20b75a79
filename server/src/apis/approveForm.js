const { JobFormDetails } = require("neusortlib");
const { approveFormV1, approveFormV2, insertV3FormDataToRabbitMQ, insertV4FormDataToRabbitMQ, insertV5FormDataToRabbitMQ } = require("../utils/insertIntoRabbitMQ");
const callDataLayer = require("../utils/callDataLayer");

const approveForm = async (req, res) => {
  try {
    const { formId, companyId } = req.body;
    const jobFormDetailsResult = (await callDataLayer(`/jobFormDetails/${formId}`, companyId, "GET")).data;
    const jobFormDetails = new JobFormDetails(jobFormDetailsResult.company, {
      ...jobFormDetailsResult.jobForm,
      programmingSkills: jobFormDetailsResult.jobForm.programmingSkills.skills,
      programmingCustomSkills: jobFormDetailsResult.jobForm.programmingCustomSkills.skills,
    });

    const formResults = (await callDataLayer(`/getJobForm/${formId}`, companyId, "GET")).data;

    const {
      skillIds,
      queueExperience,
      addCandidatesToForm,
      programmingSkills: { skills },
      programmingCustomSkills: { skills: customSkills },
    } = jobFormDetails.getJobForm().returnJobForm();
    const { sendAssignmentSubject, sendAssignmentTemplate } = formResults;

    const version = formResults.licence;
    console.log("version", version);
    console.log("jobFormDetails", jobFormDetails);
    switch (version) {
      case "v1": {
        await Promise.all(
          addCandidatesToForm.map((candidate) => {
            const data = {
              candidateFormId: candidate.id,
              name: candidate.name,
              email: candidate.email,
              languageIds: skillIds,
              experience: queueExperience,
              companyId: companyId,
              sendAssignmentTemplate,
              sendAssignmentSubject,
              skillIds: skills.map((skill) => skill.id),
              customSkillIds: customSkills.map((skill) => skill.id),
            };
            approveFormV1(data);
          })
        );
        break;
      }
      case "v2": {
        const data = {
          formId,
          companyId,
        };
        approveFormV2(data);
        break;
      }
      case "v3": {
        const data = {
          formId,
          companyId,
        };
        insertV3FormDataToRabbitMQ(data);
        break;
      }
      case "v4": {
        const data = {
          formId,
          companyId,
        };
        insertV4FormDataToRabbitMQ(data);
        break;
      }
      case "v5": {
        const data = {
          formId,
          companyId,
        };
        insertV5FormDataToRabbitMQ(data);
        break;
      }
    }

    res.status(200).json({ message: "Form approved" });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: "internal-server-error" });
  }
};

module.exports = approveForm;
