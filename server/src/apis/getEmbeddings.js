const { fetchEmbeddings } = require("../database");

const getEmbeddings = async (req, res) => {
	try {
		const { uid } = req.params;
		const data = await fetchEmbeddings(uid);
		return res.status(200).send({ success: true, data: data });
	} catch (error) {
		console.error("Error executing query", error);
		return res.status(500).send({ success: false, msg: "Something went wrong." });
	}
};

module.exports = { getEmbeddings };
