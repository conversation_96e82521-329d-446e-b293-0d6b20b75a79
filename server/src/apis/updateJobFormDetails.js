const callDataLayer = require("../utils/callDataLayer");

async function updateJobFormDetailsV1(req, res) {
  const { formData, formId } = req.body;
  const { templatesV1 } = formData;
  const { companyId } = res.locals;

  await updateJobFormByVersion(
    res,
    companyId,
    formData,
    templatesV1,
    formId,
    "v1",
  );
}

async function updateJobFormDetailsV2(req, res) {
  const { formData, formId } = req.body;
  const { templatesV2 } = formData;
  const { companyId } = res.locals;

  await updateJobFormByVersion(
    res,
    companyId,
    formData,
    templatesV2,
    formId,
    "v2",
  );
}

async function updateJobFormDetailsV4(req, res) {
  const { formData, formId } = req.body;
  const { templatesV4 } = formData;
  const { companyId } = res.locals;

  await updateJobFormByVersion(
    res,
    companyId,
    formData,
    templatesV4,
    formId,
    "v4",
  );
}

async function updateJobFormDetailsV5(req, res) {
  const { formData, formId } = req.body;
  const { templatesV5 } = formData;
  const { companyId } = res.locals;

  await updateJobFormByVersion(
    res,
    companyId,
    formData,
    templatesV5,
    formId,
    "v5",
  );
}

const updateJobFormByVersion = async (
  res,
  companyId,
  formData,
  templates,
  formId,
  version,
) => {
  try {
    if (!companyId) {
      return res.status(500).json({ message: "Company not found" });
    }

    const jobFormResult = await callDataLayer(
      `/admin/${version}/jobForm/update`,
      companyId,
      "POST",
      {
        ...formData,
        ...templates,
        formId,
      }
    );

    return res
      .status(200)
      .json({ message: "Form Updated", data: jobFormResult });
  } catch (error) {
    console.error("updateJobFormDetails Error", error);
    return res.status(500).send({
      success: false,
      msg: "Try Again",
    });
  }
}

module.exports = {
  updateJobFormDetailsV1,
  updateJobFormDetailsV2,
  updateJobFormDetailsV4,
  updateJobFormDetailsV5,
};
