const { toTimeString, subtractMinutes, moment } = require("../utils/moment");
const generateRoomId = require("../utils/generateRoomId");
const { promisePool } = require("../database/index");
const { BOOKING_MUTEX, REDIS } = require("../cache/index");
const { checkIfCanBook } = require("../utils/bookSlot");
const callDataLayer = require("../utils/callDataLayer");
const { sendInterviewInviteMail } = require("../utils/insertIntoRabbitMQ");

//from db using submission id
const preferred_time_range_start =
  process.env.DEFAULT_DAY_START_TIME || "00:00:00";
const preferred_time_range_end = process.env.DEFAULT_DAY_END_TIME || "23:00:00";
const languageType = 2;

const bookSlotTimezoneConversionMiddleware = (req, res, next) => {
  const fromTZ = req.headers["timezone"];
  const toTZ = "UTC";

  if (!fromTZ || !req.body) return next();

  const convert = (date, time) =>
    moment.tz(`${date} ${time}`, "YYYY-MM-DD HH:mm:ss", fromTZ).tz(toTZ);

  if (req.body.date && req.body.roomStartTime) {
    req.body.startTimeUTC = convert(req.body.date, req.body.roomStartTime);
    req.body.startTimeUserTz = req.body.roomStartTime;
    delete req.body.roomStartTime;
  }
  if (req.body.date && req.body.roomEndTime) {
    req.body.endTimeUTC = convert(req.body.date, req.body.roomEndTime);
    req.body.endTimeUserTz = req.body.roomEndTime;
    delete req.body.roomEndTime;
  }

  if (req.body.date) {
    req.body.dateUserTz = req.body.date;
    delete req.body.date;
  }

  next();
};

const bookSlot = async (req, res) => {
  const { companyId } = res.locals;

  const connection = await promisePool.getConnection();

  const unlocks = [];

  try {
    const timezone = req.headers.timezone;
    const name = res.locals.name || "Candidate";
    const email = res.locals.email;
    const uid = res.locals.uid;
    let data = req.body;

    console.log("data", req.body);

    // Divide the slot into 15min granularity
    const slots = [];
    const startTime = data.startTimeUTC.clone();
    const endTime = data.endTimeUTC.clone();
    let currentTime = startTime;
    while (currentTime.isBefore(endTime)) {
      slots.push(currentTime);
      currentTime = currentTime.clone().add(15, "minutes");
    }

    console.log("slots", slots);

    // Aquire lock for each of the slots
    for (const slot of slots) {
      const key = `${slot.format("YYYY-MM-DD HH:mm:ss").replace(/:/g, "_")}`;
      const unlock = await BOOKING_MUTEX.lock(key);
      unlocks.push(unlock);
    }

    console.log("Lock acquired for slots", unlocks);

    const roomId = generateRoomId();

    data = {
      ...data,
      roomId,
      languageType,
      userEmail: email,
      isDemo: false,
      demoLength: 0,
      createdBy: email,
      companyId,
      uid,
      email,
      timezone,
    };

    // Check if the slot is available
    await checkIfCanBook(
      data,
      slots.map((slot) => slot.format("YYYY-MM-DD HH:mm:ss"))
    );

    // Create the slot
    const isRescheduleRequest = data.isRescheduleRequest;

    await callDataLayer(
      isRescheduleRequest ? `/rescheduleSlot` : `/bookSlot`,
      companyId,
      "POST",
      {
        roomData: {
          roomId,
          languageType: data.languageType,
          isDemo: data.isDemo,
          demoLength: data.demoLength,
          submissionId: data.submissionId,
          userEmail: email,
        },
        timeSlotData: {
          timeSlotAvailability: slots.reduce((acc, slot) => {
            acc.push({
              from: slot.format("YYYY-MM-DD HH:mm:ss"),
              to: slot.add(15, "minutes").format("YYYY-MM-DD HH:mm:ss"),
            });
            return acc;
          }, []),
          companyTimeSlot: {
            preferredStart: preferred_time_range_start,
            preferredEnd: preferred_time_range_end,
            startTime: `${data.dateUserTz} ${data.startTimeUserTz}`,
            endTime: `${data.dateUserTz} ${data.endTimeUserTz}`,
            timezone: moment.tz(timezone).format("Z"),
          },
        },
      }
    );

    const company = await callDataLayer("/company", companyId);

    const jobFormDetails = (
      await callDataLayer(`/getJobForm/room/${roomId}`, companyId)
    ).data;
    const interviewInviteTemplate = jobFormDetails.interviewInviteTemplate;
    const interviewInviteSubject = jobFormDetails.interviewInviteSubject;
    const interviewLink = `${process.env.EVAL_CLIENT_URL}${companyId}/${roomId}`;
    const rescheduleLink = `${process.env.EVAL_CLIENT_URL}book-slot/interview/${companyId}/${data.submissionId}`;

    await sendInterviewInviteMail({
      duration: 60,
      roomId,
      name,
      email,
      date: data.dateUserTz,
      startTime: toTimeString(subtractMinutes(data.startTimeUserTz, 10)),
      endTime: data.endTimeUserTz,
      source: "neusort",
      interviewLink,
      rescheduleLink,
      companyId,
      companyName: company.companyDetails.name,
      interviewInviteTemplate,
      interviewInviteSubject,
    });

    try {
      await REDIS.deleteBookingSlotCache(data.submissionId);
    } catch (error) {
      console.error(`Error clearing booking slot cache: ${error}`);
    }

    const slotDetails = {
      startTime: data.roomStartTime,
      endTime: data.roomEndTime,
      interviewLink,
    };
    res.status(200).json({
      slotDetails,
    });
  } catch (err) {
    await connection.rollback();
    console.error("Error in bookSlot:", err);
    const errorCode = err.status ? err.response.data.code : err.code;

    if (errorCode === "already-booked-slot") {
      return res.status(400).json({ errorCode: "already-booked-slot" });
    } else if (
      errorCode === "initial-lock-unavailable" ||
      errorCode === "err_lock_already_acquired"
    ) {
      return res
        .status(500)
        .json({ errorCode: "booking-temporarily-unavailable" });
    } else if (errorCode === "interview-taken") {
      return res.status(400).json({ errorCode: "interview-taken" });
    } else {
      return res.status(500).json({ errorCode: "internal-server-error" });
    }
  } finally {
    connection.release();
    if (unlocks.length > 0) {
      let unlockErrors = [];
      for (const unlock of unlocks) {
        try {
          await unlock();
        } catch (error) {
          console.error("Error unlocking slot:", error);
          unlockErrors.push(error);
        }
      }
      if (unlockErrors.length > 0) {
        console.error(
          `Failed to release ${unlockErrors.length} locks out of ${unlocks.length}`
        );
      }
    }
  }
};

module.exports = { bookSlot, bookSlotTimezoneConversionMiddleware };
