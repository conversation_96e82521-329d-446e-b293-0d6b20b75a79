const { insertEmailIntoRabbitMQ } = require("../utils/insertIntoRabbitMQ");
const { getUserByEmail } = require("../firebase");
const callDataLayer = require("../utils/callDataLayer");

const isValidEmail = (email) => {
    const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return regex.test(email);
};

const getDomain = (email) => email.split("@")[1];

const isSameDomain = (senderEmail, receiverEmail) => {
    return getDomain(senderEmail) === getDomain(receiverEmail);
};

async function sendHiringEmail(req, res) {
    try {
        let { hireEmail, candidateProfileLink } = req.body;
        const { email: hrEmail,companyId } = res.locals;

        if (!hireEmail) {
            return res.status(400).json({ message: "Email is required." });
        }

        if (!isValidEmail(hireEmail)) {
            return res.status(400).json({ message: "Invalid email format." });
        }

        if (!candidateProfileLink) {
            return res.status(400).json({ message: "Candidate profile link is required." });
        }

        if (!isSameDomain(hrEmail, hireEmail)) {
            return res.status(400).json({
                message: "Sender and receiver email domains do not match.",
            });
        }

        let inviteId;
        
        const hiringManagerDetails = await getUserByEmail(hireEmail); 
        
        if (!hiringManagerDetails?.customClaims?.admin) {
            const hrDetails = await getUserByEmail(hrEmail);
            const licences=hrDetails?.customClaims?.licences;
            const roles=[3];

            const inviteData = {
                email : hireEmail,
                licences,
                roles,
                user:hrDetails,
            };

            const userInviteResponse = await callDataLayer(
                "/admin/invite",
                companyId,
                "POST",
                inviteData
            );

            inviteId = userInviteResponse.data.invite_id;
        }
        
        if (inviteId) {
            candidateProfileLink = candidateProfileLink + `?inviteId=${inviteId}?companyId=${companyId}`;
        }
        
        const emailData = {
            hireEmail,
            candidateProfileLink,
        };

        await insertEmailIntoRabbitMQ(emailData);

        return res.status(200).json({
            message: "Email queued for sending successfully.",
        });
    } catch (error) {
        console.error("Error in sendHiringEmail:", error);
        return res.status(500).json({
            message: "An error occurred while processing the email request.",
        });
    }
}

module.exports = { sendHiringEmail };
