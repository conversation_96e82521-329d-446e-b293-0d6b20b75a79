const callDataLayer = require("../utils/callDataLayer");
const prisma = require("../utils/prisma");

async function deleteDraft(req, res) {
  const { formId } = req.body;
  const { companyId } = res.locals;

  companyId
    ? callDataLayer("/deleteDraft", companyId, "POST", { formId })
    : await prisma.job_form.update({
      where: {
        id: formId,
      },
      data: {
        isDeleted: true,
      },
    });
  return res.status(200).json({ message: "Draft Deleted" });
}

module.exports = deleteDraft;
