const { hasRoomWithSubmissionId } = require("../database/index");
const { moment } = require("../utils/moment");

const { BOOKING_SLOT_CACHE, REDIS } = require("../cache");
const callDataLayer = require("../utils/callDataLayer");

const miniumumNumberOfDaysBeforeInterview =
  parseInt(process.env.MINIMUM_NUMBER_OF_DAYS_BEFORE_INTERVIEW) || 0;
const preferred_time_range_start =
  process.env.DEFAULT_DAY_START_TIME || "09:00:00";
const preferred_time_range_end = process.env.DEFAULT_DAY_END_TIME || "17:00:00";
const durationInMinutes = parseInt(process.env.ROOM_DURATION_IN_MINUTES) || 60;
const numDays = parseInt(process.env.NUMBER_OF_DAYS) || 14;
const miniumumNumberOfSlots =
  parseInt(process.env.MINIMUM_NUMBER_OF_SLOTS) || 5;
const numberOfInterviewsOverlap =
  parseInt(process.env.NUMBER_OF_INTERVIEWS_OVERLAP) || 1;
const bufferHours = parseInt(process.env.BUFFER_HOURS) || 1;

const getNumSlotsPerDay = async (req, res, next) => {
  try {
    const submissionId = req.params.submissionId;
    const timezone = req.headers.timezone || "UTC";
    const { uid, companyId, email } = res.locals;

    const assignmentRes = await callDataLayer(
      `/submittedAssignment/${submissionId}`,
      companyId,
      "GET",
      null,
      { headers: { email, uid } }
    );

    if (!assignmentRes.success) {
      return res.status(400).json({ errorCode: assignmentRes.code });
    }

    const assignment = assignmentRes.data;

    if (assignmentRes.data) {
      if (assignment.uid !== uid) {
        return res.status(400).json({
          errorCode: "email-mismatch",
          intendedEmail: assignment.email,
          currentEmail: email,
        });
      }
    } else {
      return res.status(404).json({ errorCode: "not-found" });
    }

    const exists = !companyId
      ? await hasRoomWithSubmissionId(submissionId)
      : assignment.is_booked;

    const today = moment.tz(timezone).format("YYYY-MM-DD");
    const preferredStartTime = moment
      .tz(
        `${today} ${preferred_time_range_start}`,
        "YYYY-MM-DD HH:mm:ss",
        timezone
      )
      .tz("UTC");

    const preferredEndTime = moment
      .tz(
        `${today} ${preferred_time_range_end}`,
        "YYYY-MM-DD HH:mm:ss",
        timezone
      )
      .tz("UTC");

    const currentDateNumberOfInterviewsOverlap = await REDIS.getMaxRoomCount();

    const result = (
      await callDataLayer(`/timeSlotsPerDate`, companyId, "POST", {
        queryData: {
          roomLength: durationInMinutes,
          numDays: numDays,
          minSlots: miniumumNumberOfSlots,
          currentDayMaxOccupiedCount: currentDateNumberOfInterviewsOverlap,
          maxOccupiedCount: numberOfInterviewsOverlap,
          dayStartTimeUTC: preferredStartTime.format("HH:mm:ss"),
          dayEndTimeUTC: preferredEndTime.format("HH:mm:ss"),
          bufferHours: bufferHours,
          minimumNumberOfDaysBeforeInterview:
            miniumumNumberOfDaysBeforeInterview,
          currentDate: moment
            .tz(
              `${moment
                .tz(timezone)
                .format("YYYY-MM-DD")} ${preferred_time_range_start}`,
              "YYYY-MM-DD HH:mm:ss",
              timezone
            )
            .clone()
            .tz("UTC")
            .format("YYYY-MM-DD"),

          currentDayStartUTC: (() => {
            const nowTimeUTC = moment.tz(timezone).tz("UTC");
            const nowWithBuffer = nowTimeUTC.add(bufferHours, "hours");

            if (nowWithBuffer.isAfter(preferredStartTime)) {
              const preferredMinutes = preferredStartTime.minutes();

              const adjustedTime = moment
                .utc(nowWithBuffer)
                .minutes(preferredMinutes)
                .seconds(0);

              if (adjustedTime.isBefore(nowWithBuffer)) {
                adjustedTime.add(1, "hour");
              }

              if (
                adjustedTime.isBetween(
                  preferredStartTime,
                  preferredEndTime,
                  null,
                  "[]"
                )
              ) {
                return adjustedTime.format("HH:mm:ss");
              }

              if (
                adjustedTime.isAfter(preferredEndTime) &&
                adjustedTime.dayOfYear() === preferredEndTime.dayOfYear()
              ) {
                return preferredEndTime.format("HH:mm:ss");
              }

              return adjustedTime.format("HH:mm:ss");
            }

            return preferredStartTime.format("HH:mm:ss");
          })(),
        },
      })
    ).data;

    const slotsPerDayInUserTimezone = result.reduce((acc, row) => {
      const utcDateTime = moment.utc(
        `${row.date}T${preferredStartTime.format("HH:mm:ss")}`
      );
      const localDateTime = utcDateTime.clone().tz(timezone);

      acc[localDateTime.format("YYYY-MM-DD")] = row.available_slots;

      return acc;
    }, {});

    await BOOKING_SLOT_CACHE.set(
      submissionId,
      new Map().set(
        "validDates",
        JSON.stringify(Object.keys(slotsPerDayInUserTimezone))
      )
    );

    res.locals.slotData = {
      from: Object.keys(slotsPerDayInUserTimezone)[0],
      to: Object.keys(slotsPerDayInUserTimezone)[
        Object.keys(slotsPerDayInUserTimezone).length - 1
      ],
      slotsPerDay: slotsPerDayInUserTimezone,
      disabledDates: Object.keys(slotsPerDayInUserTimezone).filter(
        (date) => slotsPerDayInUserTimezone[date] == 0
      ),
      roomLengthInMinutes: durationInMinutes,
      alreadyBooked: exists,
      slotDetails: exists
        ? {
            interviewLink: `${process.env.EVAL_CLIENT_URL}${companyId}/${assignment.room.roomid}`,
            ...assignment.slot,
          }
        : null,
      interviewTaken: assignment.interviewTaken,
    };

    return next();
  } catch (err) {
    console.error("Error in getNumSlotsPerDay:", err);
    return res.status(500).json({ errorCode: "internal-server-error" });
  }
};

const dateConversionMiddleware = (req, res) => {
  try {
    const userTimeZone = req.headers.timezone || "UTC";

    function getFullTimeZoneName(date, timeZone) {
      return new Intl.DateTimeFormat("en-US", {
        timeZone,
        timeZoneName: "long",
      })
        .formatToParts(date)
        .find((part) => part.type === "timeZoneName").value;
    }

    function convertDatesToTimezone(data, timeZone) {
      const convertDate = (dateString) => {
        const date = moment.tz(dateString, timeZone).startOf("day");
        const fullTimeZoneName = getFullTimeZoneName(date.toDate(), timeZone);
        return (
          date.format(`ddd MMM DD YYYY HH:mm:ss [GMT]ZZ`) +
          ` (${fullTimeZoneName})`
        );
      };

      const convertedSlotsPerDay = {};
      for (const [key, value] of Object.entries(data.slotsPerDay)) {
        convertedSlotsPerDay[convertDate(key)] = value;
      }

      const convertedDisabledDates = data.disabledDates.map(convertDate);
      const convertedFrom = convertDate(data.from);
      const convertedTo = convertDate(data.to);

      return {
        slotsPerDay: convertedSlotsPerDay,
        disabledDates: convertedDisabledDates,
        from: convertedFrom,
        to: convertedTo,
      };
    }

    const slotDetails = res.locals.slotData.slotDetails;
    if (slotDetails) {
      slotDetails.startTime = moment
        .utc(slotDetails.startTime)
        .tz(userTimeZone)
        .toDate();
      slotDetails.endTime = moment
        .utc(slotDetails.endTime)
        .tz(userTimeZone)
        .toDate();
    }

    res.status(200).json({
      ...convertDatesToTimezone(res.locals.slotData, userTimeZone),
      roomLengthInMinutes: res.locals.slotData.roomLengthInMinutes,
      alreadyBooked: res.locals.slotData.alreadyBooked,
      interviewTaken: res.locals.slotData.interviewTaken,
      slotDetails: slotDetails && {
        startTime: slotDetails.startTime,
        endTime: slotDetails.endTime,
        interviewLink: slotDetails.interviewLink,
      },
    });
  } catch (error) {
    console.error("Error during date conversion:", error);
    res.status(500).json({ errorCode: "internal-server-error" });
  }
};

module.exports = { getNumSlotsPerDay, dateConversionMiddleware };
