const path = require("path");
const fsPromises = require("fs").promises;
const fs = require("fs");
const busboy = require("busboy");
const { v4: uuidv4 } = require("uuid");
const {
  isGitRepo,
  initGitRepo,
  createAzureDevopsRepo,
  pushGitRepo,
  cleanup,
  cleanupOnDisconnect,
  insertIntoDB,
  deleteGitRepo,
  getCurrentBranch,
} = require("../utils/assignmentSubmission");

const submitAssignmentByDirectoryUpload = async (req, res) => {
  let clientDisconnected = false;
  const companyId = await res.locals.companyId;
  try {
    const { assignmentId } = req.params;

    const repoName = uuidv4().replace(/-/g, "");
    console.log("Repo name:", repoName);

    const assignmentDir = path.join("/assignments", repoName);

    await fsPromises.mkdir(assignmentDir, { recursive: true });

    const bb = busboy({ headers: req.headers });

    const cleanupHandler = cleanupOnDisconnect.bind(
      null,
      assignmentDir,
      res,
      () => {
        clientDisconnected = true;
      }
    );
    req.on("aborted", cleanupHandler);

    bb.on("file", async (fieldname, file, filename, encoding, mimetype) => {
      const filePath = path.join(assignmentDir, fieldname);
      const dirPath = path.dirname(filePath);

      try {
        await fsPromises.mkdir(dirPath, { recursive: true });

        const writeStream = fs.createWriteStream(filePath);
        file.pipe(writeStream);

        await new Promise((resolve, reject) => {
          writeStream.on("finish", resolve);
          writeStream.on("error", reject);
          file.on("error", reject);
        });

        console.log(`File ${filename} uploaded to ${filePath} successfully`);
      } catch (error) {
        await cleanup(assignmentDir);
        console.error(`Error uploading file ${filename}:`, error);
        res.status(500).send({
          success: false,
          code: "upload-error",
          message: "Something went wrong. Could not upload file.",
        });
        return;
      }
    });

    bb.on("finish", async () => {
      req.removeListener("aborted", cleanupHandler);

      if (clientDisconnected) return;

      let repoCreated = false;
      let repoId;

      try {
        const isGit = await isGitRepo(assignmentDir);

        if (!isGit) {
          console.log("Initializing Git repo");
        } else {
          console.log("Reinitializing Git repo");
          await Promise.all([
            fsPromises.rm(path.join(assignmentDir, ".git"), {
              recursive: true,
              force: true,
            }),
            fsPromises.rm(path.join(assignmentDir, ".gitmodules"), {
              recursive: true,
              force: true,
            }),
          ]);
        }

        await initGitRepo(assignmentDir);
        console.log("Reinitialized git repo", assignmentDir);

        const remoteBranch = uuidv4().replace(/-/g, "");
        const localBranch = getCurrentBranch(assignmentDir);
        console.log("Local Branch after reinitializing:", localBranch);

        const { remoteUrl, id } = await createAzureDevopsRepo(repoName);
        repoId = id;
        repoCreated = true;

        await pushGitRepo(remoteUrl, assignmentDir, localBranch, remoteBranch);
        await insertIntoDB(remoteUrl, remoteBranch, assignmentId, companyId);
        await cleanup(assignmentDir);

        res.status(200).send({
          success: true,
          message: "Assignment submitted successfully",
        });
        return;
      } catch (error) {
        await cleanup(assignmentDir);
        if (repoCreated) {
          await deleteGitRepo(repoId);
        }
        console.error(error);
        res.status(500).send({
          code: "internal-server-error",
          message: "Something went wrong. Could not submit assignment.",
        });
        return;
      }
    });

    bb.on("error", async (error) => {
      await cleanup(assignmentDir);
      console.error("Error in submitAssignmentByDirectoryUpload:", error);
      res.status(500).send({
        code: "internal-server-error",
        message: "Something went wrong. Could not submit assignment.",
      });
      return;
    });

    req.pipe(bb);
  } catch (error) {
    console.error("Error setting up assignment directory:", error);
    res.status(500).send({
      code: "internal-server-error",
      message: "Something went wrong. Could not submit assignment.",
    });
  }
};

module.exports = { submitAssignmentByDirectoryUpload };
