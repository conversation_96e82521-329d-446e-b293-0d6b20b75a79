const callDataLayer = require("../utils/callDataLayer");

async function sendCheatingTimestamps(req, res) {
    try {
        const { companyId } = res.locals;
        const dataLayerResponse = await callDataLayer(
            `/sendCheatingTimestamps`,
            companyId,
            "POST",
            req.body
        );

        if (dataLayerResponse.success !== true) {
            console.error("Failed to send cheating timestamps to data layer");
            return res.status(500).json({ success: false, message: "Failed to send data" });
        }
        return res.status(200).json({ success: true });
    } catch (e) {
        console.error("Error ending room", e);
        return res.status(500).json({ success: false, error: e });
    }
}

module.exports = sendCheatingTimestamps;
