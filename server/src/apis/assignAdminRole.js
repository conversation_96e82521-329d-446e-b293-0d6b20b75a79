const callDataLayer = require("../utils/callDataLayer");

const assignRoleToAdmin = async (req, res) => {
    const { companyId } = res.locals;

    try {
        const result = await callDataLayer("/admin/assignRole", companyId, "POST", req.body);

        if (result.success) {
            return res.status(200).json({
                success: true,
                message: "Role assigned successfully!",
            });
        } else {
            return res.status(400).json({
                success: false,
                message: result.message || "Failed to assign role",
            });
        }
    } catch (error) {
        console.error("Error assigning role:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to assign role. Please try again later.",
        });
    }
};

module.exports = { assignRoleToAdmin };
