const callDataLayer = require("../utils/callDataLayer");

async function sendMetaInfo(req, res) {
  const metaInfo = req.body;  
    
  try {
    const metaInfoResponse = await callDataLayer(
      "/metaInfo", 
      metaInfo.companyId,
      "POST",
      metaInfo  
    );

    return res.status(200).json({ message: "Meta info sent successfully" });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "An error occurred while sending meta info", error });
  }
}

module.exports = sendMetaInfo;
