const callDataLayer = require("../utils/callDataLayer");

async function getFirstTransactionYear(req, res) {
  const { companyId } = res.locals;

  try {
    const data = (await callDataLayer(
      "/getFirstTransactionYear",
      companyId,
      "GET"
    )).data;

    return res.status(200).json({ success: true, data });
  } catch (error) {
    console.error("Error getting first transaction year:", error);
    return res.status(500).json({
      success: false,
      error: "Error fetching first transaction year"
    });
  }
}

module.exports = { getFirstTransactionYear };