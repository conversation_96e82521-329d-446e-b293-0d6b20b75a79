const { FormCandidates } = require("neusortlib/models");
const callDataLayer = require("../utils/callDataLayer");
const { calculateCheatingStatus } = require("../utils/calculateCheatingStatus");

async function getFormCandidates(req, res) {
  const { formId } = req.body;
  const { companyId } = res.locals;
  const { version } = req.params;

  let { page, pageSize, sorting, globalFilter, columnFilters } = req.query;

  try {
    if (!companyId) {
      return res.status(500).json({ message: "Company id is required" });
    }

    const candidates = await callDataLayer(
      `/admin/${version}/fetchFormCandidates`,
      companyId,
      "POST",
      {
        formId,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        sorting,
        columnFilters,
      }
    );

    if (!candidates.success) {
      return res.status(400).json({
        candidates: [],
        total: 0,
      });
    }

    const candidateModel = new FormCandidates(
      candidates.data.candidates,
      candidates.data.total
    );

    return res.status(200).json({
      ...candidateModel.returnFormCandidates(),
    });
  } catch (error) {
    console.error("Error fetching candidates:", error.message);
    return res.status(400).json({
      candidates: [],
      total: 0,
    });
  }
}

function processCandidates(candidates) {
  return calculateCheatingStatus(candidates);
}

function filterCandidates(candidates, cheatingStatus) {
  let filteredCandidates = candidates;
  if (cheatingStatus) {
    filteredCandidates = filteredCandidates.filter(
      (candidate) =>
        candidate.derivedAttributes.cheatingStatus === cheatingStatus
    );
  }
  return filteredCandidates;
}

module.exports = {
  getFormCandidates,
};
