const callDataLayer = require("../utils/callDataLayer");

const getRoles = async (req, res) => {
    const { companyId } = res.locals;
    try {
        const data = await callDataLayer("/admin/getRoles", companyId, "GET");

        return res.status(200).json({
            success: true,
            roles: data.roles, 
            message: "Roles fetched successfully!",
        });
    } catch (error) {
        console.error("Error fetching roles:", error);
        return res.status(500).json({
            success: false,
            message: "Failed to fetch roles. Please try again later.",
        });
    }
};

module.exports = { getRoles };
