const { findSubmittedAssignmentById } = require("../database");
const { getFromRealtimeDatabase, getUserByUid } = require("../firebase");

const getGeneratedQuestions = async (req, res) => {
  const { submissionId } = req.params;
  try {
    const submissionDetails = await findSubmittedAssignmentById(submissionId);
    if (!submissionDetails?.length) {
      return res.status(404).json({ success: false, message: "Submission not found" });
    }
    const { firebase_link: firebaseLink, uid } = submissionDetails[0];
    const questions = await getFromRealtimeDatabase(
      `/${firebaseLink}/questions`
    );
    const userRecord = await getUserByUid(uid);
    const userEmail = userRecord.email;
    res.status(200).json({ firebaseLink, questions, userEmail });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error });
  }
};

module.exports = getGeneratedQuestions;
