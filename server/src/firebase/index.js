const app = require("firebase-admin");
const { getAuth } = require("firebase-admin/auth");
const serviceAccount = require("../../certs/hyra-720a2-38c19f644b5e.json");
const { botId } = require("../constants/roomConstants");

app.initializeApp({
  credential: app.credential.cert(serviceAccount),
  databaseURL:
    "https://hyra-720a2-default-rtdb.asia-southeast1.firebasedatabase.app",
});

async function getUserByEmail(email) {
  try {
    const user = await app.auth().getUserByEmail(email);
    return user.toJSON();
  } catch (e) {
    console.error("Error fetching user data:", e);
    return null;
  }
}

async function getUserByUid(uid) {
  try {
    const user = await app.auth().getUser(uid);
    return user.toJSON();
  } catch (e) {
    console.error("Error fetching user data:", e);
    return null;
  }
}

async function getCustomToken(req, res) {
  try {
    const { uid } = req.body;
    const customToken = await getAuth().createCustomToken(uid);
    res.status(200).json({ success: true, token: customToken });
  } catch (error) {
    console.error("Error generating custom token: ", error);
    res.status(500).json({ success: false, error: error });
  }
}

async function storeInRealtimeDatabase(path, data) {
  const db = app.database();
  const ref = db.ref(path);
  try {
    await ref.set(data);
    console.log("Data stored successfully in Firebase Realtime Database");
  } catch (error) {
    console.error("Failed to store data in Firebase Realtime Database:", error);
  }
}

async function getFromRealtimeDatabase(path) {
  const db = app.database();
  const ref = db.ref(path);
  try {
    const snapshot = await ref.once("value");
    if (snapshot.exists()) {
      console.log(
        "Data retrieved successfully from Firebase Realtime Database"
      );
      return snapshot.val();
    } else {
      console.log("No data found at the specified path: ", path);
      return null;
    }
  } catch (error) {
    console.error(
      "Failed to retrieve data from Firebase Realtime Database:",
      error
    );
    throw error; // Rethrow or handle error as appropriate for your app
  }
}

async function getBotIdToken() {
  const customToken = await app.auth().createCustomToken(botId);
  const idToken = await exchangeCustomTokenForIdToken(customToken);
  return idToken;
}

async function exchangeCustomTokenForIdToken(customToken) {
  try {
    const response = await fetch(
      `https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key=${process.env.FIREBASE_API_KEY}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token: customToken,
          returnSecureToken: true,
        }),
      }
    );

    const data = await response.json();
    return data.idToken;
  } catch (error) {
    console.error("Error exchanging custom token for ID token:", error);
    throw error;
  }
}

async function getCustomClaims(uid) {
  try {
    const userRecord = await app.auth().getUser(uid);
    console.log("Previous custom claims:", userRecord.customClaims);
    return userRecord.customClaims;
  } catch (error) {
    console.error("Error fetching user data:", error);
    throw error;
  }
}

async function setCustomClaims(uid, newClaims) {
  try {
    const previousClaims = await getCustomClaims(uid);
    await app
      .auth()
      .setCustomUserClaims(uid, { ...previousClaims, ...newClaims });
    console.log("Custom claims set successfully for uid:", uid, {
      ...previousClaims,
      ...newClaims,
    });
  } catch (error) {
    console.error("Error setting custom claims:", error);
  }
}

module.exports = {
  getUserByEmail,
  getUserByUid,
  getCustomToken,
  storeInRealtimeDatabase,
  getFromRealtimeDatabase,
  getBotIdToken,
  setCustomClaims,
};
