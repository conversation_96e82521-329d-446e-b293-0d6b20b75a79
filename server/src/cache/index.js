const Rooms = require("../models/rooms.js");
const Ratings = require("../models/ratings.js");
const Questions = require("../models/questions.js");
const ModelFactory = require("../models/genAI.js");
const Producer = require("../models/producer.js");
const Mutex = require("../models/mutex.js");
const RedisClient = require("../models/redis.js");
const BookingSlotCache = require("../models/bookingSlotCache.js");
const Skills = require("../models/skills.js");
const DSASkills = require("../models/dsaSkills.js");

const REDIS = new RedisClient();
const ROOMS = new Rooms(REDIS);
const RATINGS = new Ratings();
const QUESTIONS = new Questions();
const SKILLS = new Skills();
const DSA_SKILLS = new DSASkills();
const GENAI = ModelFactory.getInstance(
  process.env.GENAI_TO_USE || "AzureOpenAI"
);
const PRODUCER = new Producer();
const BOOKING_MUTEX = new Mutex(REDIS);
const BOOKING_SLOT_CACHE = new BookingSlotCache(REDIS);

module.exports = {
  REDIS,
  ROOMS,
  RATINGS,
  QUESTIONS,
  SKILLS,
  DSA_SKILLS,
  GENAI,
  PRODUCER,
  BOOKING_MUTEX,
  BOOKING_SLOT_CACHE,
};
