const { BlobServiceClient } = require("@azure/storage-blob");

const AZURE_STORAGE_CONNECTION_STRING =
  process.env.AZURE_STORAGE_CONNECTION_STRING;

if (!AZURE_STORAGE_CONNECTION_STRING) {
  throw new Error("Azure Storage connection string is missing.");
}

const blobServiceClient = BlobServiceClient.fromConnectionString(
  AZURE_STORAGE_CONNECTION_STRING
);

const getContainerClient = async (CONTAINER_NAME) => {
  const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);

  if (!(await containerClient.exists())) {
    await containerClient.create({ access: "blob" });
  }

  return containerClient;
};

const uploadFileToAzure = async (filePath, fileName, container) => {
  const containerClient = await getContainerClient(container);
  const blobName = fileName;
  const blockBlobClient = containerClient.getBlockBlobClient(blobName);

  await blockBlobClient.uploadFile(filePath);

  return blockBlobClient.url;
};

module.exports = { uploadFileToAzure };
