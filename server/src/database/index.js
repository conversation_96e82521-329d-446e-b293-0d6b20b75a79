const fs = require("fs");
const mysql = require("mysql2");
const { ROOMS, RATINGS, GENAI } = require("../cache/index.js");
const getJsonFromPromptWithRetries = require("../utils/getResultFromPromptWithRetries.js");
const { conversationRatingPrompt } = require("../utils/prompts.js");
const { toTimeStamp, toTimeString } = require("../utils/moment.js");

const pool = mysql.createPool({
  connectionLimit: 2,
  host: process.env.MYSQL_SERVER,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DB,
  ssl: {
    ca: fs.readFileSync(process.env.MYSQL_CA),
  },
  dateStrings: "DATETIME",
  timezone: process.env.MYSQL_TIMEZONE,
});

const promisePool = pool.promise();

async function queryDatabase(connection, sql, params) {
  try {
    const [rows, fields] = await connection.query(sql, params);
    return rows;
  } catch (error) {
    console.error("Error in executing MySQL query:", error);
    throw error;
  }
}

async function executeQueries(connection, queries, params) {
  for (let i = 0; i < queries.length; i++) {
    await queryDatabase(connection, queries[i], params[i]);
  }
}

const removeSystemMessagesAndFormat = (conversation) => {
  return conversation
    .filter((item) => item.role !== "system")
    .map((item) => item.content);
};

const fetchRatings = () => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM ratings WHERE expired = 0",
      (err, results, fields) => {
        if (err) {
          console.log("Error while fetching ratings from db - ", err);
          reject(err);
        } else {
          const availableRatings = results.map((rating) => {
            return `id:${rating.id}, rating_title: "${rating.rating_title}", rating_value: <0-10>`;
          });

          resolve(availableRatings);
        }
      }
    );
  });
};

const fetchInitialQuestions = () => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM initial_questions WHERE expired = 0",
      (err, results, fields) => {
        if (err) {
          console.log("Error while fetching initial questions from db", err);
          reject(err);
        } else {
          const availableQuestions = results.map((obj) => {
            return obj.question;
          });

          resolve(availableQuestions);
        }
      }
    );
  });
};

const fetchAllSkills = () => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT programming FROM programming_language",
      (err, results, fields) => {
        if (err) {
          console.log("Error while fetching skills from db", err);
          reject(err);
        } else {
          const availableSkills = results.map((obj) => {
            return obj.programming;
          });
          resolve(availableSkills);
        }
      }
    );
  });
};

const fetchSubmissionDetails = async (submissionId) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM assignment_submissions WHERE id = ?",
      [submissionId],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while fetching submission from db for submissionId - ",
            submissionId,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const getRoomData = async (roomId) => {
  return new Promise((resolve, reject) => {
    pool.query(
      `SELECT 
          rooms.is_demo, 
          rooms.demo_length, 
          rooms.user_email, 
          rooms.roomid, 
          rooms.submission_id, 
          time_slots.start_time, 
          time_slots.end_time, 
          languages.language, 
          assignment_submissions.uid,
          GROUP_CONCAT(programming_language.programming) AS programming_languages
      FROM 
          rooms
      INNER JOIN 
          time_slots ON rooms.roomid = time_slots.roomid
      INNER JOIN 
          languages ON rooms.language_id = languages.id
      LEFT JOIN
          assignment_submissions ON rooms.submission_id = assignment_submissions.id
      LEFT JOIN
          user_assignments ON assignment_submissions.user_assignment_id = user_assignments.id
      LEFT JOIN
          programming_language ON FIND_IN_SET(programming_language.language_id, user_assignments.programming_language_ids)
      WHERE 
          rooms.roomid = ?
      GROUP BY
          rooms.is_demo, 
          rooms.demo_length, 
          rooms.user_email, 
          rooms.roomid, 
          rooms.submission_id, 
          time_slots.start_time, 
          time_slots.end_time, 
          languages.language, 
          assignment_submissions.uid;`,
      [roomId],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while fetching rooom info from db for roomid",
            roomId,
            err
          );
          reject(err);
        } else {
          resolve([
            {
              ...results[0],
              programming_languages:
                results[0].programming_languages.split(","),
            },
          ]);
        }
      }
    );
  });
};

const generateConversationRatings = async (roomId) => {
  try {
    const ratings = RATINGS.getRatings();

    const conversation = removeSystemMessagesAndFormat(
      ROOMS.getConversation(roomId)
    );
    const lastIntervieweeResponse = conversation[conversation.length - 1];
    const lastInterviewerResponse = conversation[conversation.length - 2];

    const prompt = conversationRatingPrompt(
      ratings,
      conversation,
      lastInterviewerResponse,
      lastIntervieweeResponse
    );

    const response = await getJsonFromPromptWithRetries(
      GENAI,
      prompt,
      "getResultFromPrompt",
      5,
      roomId,
      "{",
      "}"
    );

    if (!response) {
      console.log("Error generating conversation ratings: ", roomId);
      return [];
    }

    return response.ratings;
  } catch (err) {
    console.log("Error generating conversation ratings: ", roomId, err);
    return [];
  }
};

const insertConversationRatings = async (roomId, conversation_id, ratings) => {
  try {
    const values = ratings.map((rating) => [
      conversation_id,
      rating.rating_id,
      rating.rating_value,
      rating.rating_reason,
    ]);

    return new Promise((resolve, reject) => {
      pool.query(
        "INSERT INTO conversation_ratings (conversation_id, rating_id, rating_value, rating_reason) VALUES ?",
        [values],
        (err, results, fields) => {
          if (err) {
            console.log(
              "Error while inserting conversation rating into db for roomid - ",
              roomId,
              err
            );
            reject(err);
          } else {
            resolve(results);
          }
        }
      );
    });
  } catch (err) {
    console.log(
      "Error while inserting conversation rating for roomid - ",
      roomId,
      err
    );
  }
};

const insertConversation = (roomId, speaker, text) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "INSERT INTO conversation (roomid, speaker, text) VALUES (?, ?, ?)",
      [roomId, speaker, text],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while inserting conversation into db for roomid - ",
            roomId,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const insertCodingConversation = (roomId, question) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "INSERT INTO coding_conversation (roomid, question) values (?, ?)",
      [roomId, question],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while inserting coding conversation into db for roomid - ",
            roomId,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const updateCodingConversationAndAddRating = async (
  roomId,
  coding_conversation_id,
  answer,
  rating_id,
  rating_value,
  rating_reason = ""
) => {
  const connection = await pool.promise().getConnection();
  try {
    await connection.beginTransaction();

    const [updateResult] = await connection.execute(
      "UPDATE coding_conversation SET answer = ? WHERE id = ?",
      [answer, coding_conversation_id]
    );

    const [insertResult] = await connection.execute(
      "INSERT INTO coding_conversation_ratings (coding_conversation_id, rating_id, rating_value, rating_reason) VALUES (?, ?, ?, ?)",
      [coding_conversation_id, rating_id, rating_value, rating_reason]
    );

    await connection.commit();
    console.log("Updated coding conversation and added rating");
  } catch (error) {
    await connection.rollback();
    console.error(`Error in transaction for roomId ${roomId}:`, error);
    throw error;
  } finally {
    connection.release();
  }
};

const insertCodingHint = (
  roomId,
  coding_conversation_id,
  query,
  hint,
  case_triggered
) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "INSERT INTO coding_hints (coding_conversation_id, query, hint, case_triggered) values (?, ?, ?, ?)",
      [coding_conversation_id, query, hint, case_triggered],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while inserting coding hint into db for roomid - ",
            roomId,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const findPendingAssignmentById = async (id) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM user_assignments WHERE id = ? and deleted = 0",
      [id],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while fetching assignment from db for id - ",
            id,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const findGenericAssignmentByAlias = async (alias) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM generic_assignments WHERE alias = ?",
      [alias],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while fetching generic assignment from db for alias - ",
            alias,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const findSubmittedAssignmentById = async (id) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM assignment_submissions WHERE id = ?",
      [id],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while fetching assignment from db for id - ",
            id,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const findSubmittedAssignmentByAssignmentId = async (assignmentId) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM assignment_submissions WHERE user_assignment_id = ?",
      [assignmentId],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while fetching assignment from db for id - ",
            assignmentId,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const findSubmittedGenericAssignment = async (alias, uid) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM assignment_submissions WHERE user_assignment_id = ? AND uid = ?",
      [alias, uid],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while fetching generic assignment submission from db for alias - ",
            alias,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const findSubmittedAssignmentByLink = async (github_link) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT * FROM assignment_submissions WHERE github_link = ?",
      [github_link],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while fetching assignment from db for link - ",
            github_link,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const insertIntoAssignmentSubmissions = async (
  githubLink,
  branch,
  assignmentId
) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "INSERT INTO assignment_submissions (github_link, branch, user_assignment_id) VALUES (?, ?, ?, ?)",
      [githubLink, branch, assignmentId],
      (err, results, fields) => {
        if (err) {
          console.log("Error while inserting last response in db- ", err);
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const insertIntoAssignmentPool = async (uid, assignmentId) => {};

const generateInterviewSummary = async (roomId) => {
  try {
    if (!(await ROOMS.hasRoom(roomId))) {
      console.log("Room has already been deleted. Cannot generate summary.");
      return;
    }

    const conversation = removeSystemMessagesAndFormat(
      ROOMS.getConversation(roomId)
    );
    const tabOutOfFocusTimestamps = ROOMS.getTabOutOfFocusTimestamps(roomId);

    const otherInfo = JSON.stringify({
      tabOutOfFocusTimestamps,
    });

    const prompt = `The Interview conversation is as follows:
      ${conversation.join("\n")}
      
      Please provide a summary of the interview.
      
      Structure your response in the following format: (use single quotes inside summary to escape double quotes)
      
      {
        "summary": "<Your summary here>"
      }`;

    const response = await getJsonFromPromptWithRetries(
      GENAI,
      prompt,
      "getResultFromPrompt",
      5,
      roomId,
      "{",
      "}"
    );

    if (!response) {
      console.log("Error while getting response for summary");
      return;
    }

    return {
      summary: response.summary,
      otherInfo,
    };
  } catch (err) {
    console.log("Error while generating summary for room id - ", roomId, err);
    return;
  }
};

const insertSummaryAndUpdateInterview = async (roomId, data) => {
  const connection = await pool.promise().getConnection();
  try {
    await connection.beginTransaction();
    const { summary, otherInfo } = data;
    console.log("insert summart for room id - ", roomId);

    connection.query(
      "INSERT INTO summary (roomid, summary, other_info) VALUES (?, ?, ?)",
      [roomId, summary, otherInfo],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error occured while inserting into summary for room id - ",
            roomId,
            err
          );
        }
      }
    );

    connection.query(
      "UPDATE rooms SET interview_taken = 1 WHERE roomId = ?",
      [roomId],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error occured while updating into rooms for roomId - ",
            roomId
          );
        }
      }
    );

    await connection.commit();
  } catch (err) {
    await connection.rollback();
    console.log(
      "Error while inserting into summary for room id - ",
      roomId,
      err
    );
  } finally {
    connection.release();
  }
};

const insertTimeSlot = (roomId, startTime, endTime) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "INSERT INTO time_slots (roomid, start_time, end_time) VALUES (?, ?, ?)",
      [roomId, startTime, endTime],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error while inserting time slot into db for roomid - ",
            roomId,
            err
          );
          reject(err);
        } else {
          resolve(results);
        }
      }
    );
  });
};

const createRoom = async (connection, roomData) => {
  const {
    roomId,
    languageId,
    languageType,
    userEmail,
    createdBy,
    isDemo,
    demoLength,
    submissionId,
  } = roomData;
  console.log("creating new room", roomData);

  return await queryDatabase(
    connection,
    "INSERT INTO rooms (roomid, language_id, language_type, user_email, created_by, is_demo, demo_length, submission_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
    [
      roomId,
      languageId,
      languageType,
      userEmail,
      createdBy,
      isDemo,
      demoLength,
      submissionId,
    ]
  );
};

const storeEmbeddings = (uid, interview_id, embeddings) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "INSERT INTO interviewee_voice_embeddings (uid, interview_id, embeddings) VALUES (?, ?, ?)",
      [uid, interview_id, JSON.stringify(embeddings)],
      (err, results, fields) => {
        if (err) {
          console.log(
            "Error occured while inserting into interviewee_voice_embeddings for interview_id - ",
            interview_id
          );
          reject(err);
        } else {
          console.log("Embeddings stored successfully");
          resolve(results);
        }
      }
    );
  });
};

const isDemo = async (roomId) => {
  return new Promise(async (resolve, reject) => {
    try {
      pool.query(
        "SELECT is_demo FROM interviews.rooms WHERE roomid = ?",
        [roomId],
        (err, results, fields) => {
          if (err) {
            console.log("Error while finding roomid : ", err);
          }
          return resolve(results[0].is_demo);
        }
      );
    } catch (error) {
      console.error("Error while fetching", error);
      reject(error);
    }
  });
};

const fetchEmbeddings = (uid) => {
  return new Promise((resolve, reject) => {
    pool.query(
      "SELECT interview_id, embeddings, created_at FROM interviewee_voice_embeddings WHERE uid = ?",
      [uid],
      (err, results, fields) => {
        if (err) {
          console.log("Error while fetching : ", err);
          reject(err);
        }
        resolve(results);
      }
    );
  });
};

async function createTimeSlot(
  connection,
  roomId,
  date,
  roomStartTime,
  roomEndTime,
  preferred_time_range_start,
  preferred_time_range_end
) {
  const startTimeStamp = toTimeStamp(date, toTimeString(roomStartTime));
  const endTimeStamp = toTimeStamp(date, toTimeString(roomEndTime));

  return await queryDatabase(
    connection,
    `INSERT INTO time_slots (roomid, start_time, end_time, preferred_time_range_start, preferred_time_range_end)
  VALUES (?, ?, ?, ?, ?);`,
    [
      roomId,
      startTimeStamp,
      endTimeStamp,
      preferred_time_range_start,
      preferred_time_range_end,
    ]
  );
}

async function hasRoomWithSubmissionId(submissionId) {
  console.log("Checking if room exists for submission id - ", submissionId);
  const result = await queryDatabase(
    promisePool,
    "SELECT EXISTS(SELECT 1 FROM rooms WHERE submission_id = ?) AS isPresent",
    [submissionId]
  );
  return result[0].isPresent;
}

const fetchFormCandidatesV1 = async (
  formId,
  offset,
  pageSize,
  orderBy,
  columnFilters,
  assignmentStatusQuery,
  interviewStatusQuery,
  interviewStartTimeQuery,
  resultQuery
) => {
  return new Promise(async (resolve, reject) => {
    const columnFiltersArray = columnFilters ? JSON.parse(columnFilters) : [];

    const filters = columnFiltersArray
      .map(({ id, value }) => {
        if (id === "email" || id === "name" || id === "id") {
          return `AND c.${id} LIKE '%${value}%'`;
        } else if (id === "assignmentScore") {
          return `AND as2.score LIKE "%${value}%"`;
        }
      })
      .join(" ");

    let filterQuery = "";
    if (filters) {
      filterQuery = `${filters}`;
    }

    try {
      pool.query(
        `SELECT 
        c.email, c.name, c.id, ua.id AS assignmentId,
        as2.id AS submissionId, as2.score AS assignmentScore,
        r.interview_taken AS interviewTaken, 
        ts.start_time AS startTime,
        AVG(COALESCE(ccr.rating_value, 0)) AS rating,
        CASE WHEN AVG(COALESCE(ccr.rating_value, 0)) > 6 THEN 'pass' ELSE 'fail' END AS result,
        COUNT(*) OVER() AS totalCandidates
        FROM interviews.candidate_form_mapping cfm
        LEFT JOIN interviews.candidate c ON c.id = cfm.candidateId 
        LEFT JOIN interviews.user_assignments ua ON ua.applicationid = cfm.id
        LEFT JOIN interviews.assignment_submissions as2 ON as2.user_assignment_id = ua.id 
        LEFT JOIN interviews.rooms r ON r.submission_id = as2.id 
        LEFT JOIN interviews.time_slots ts ON r.roomid = ts.roomid
        LEFT JOIN interviews.coding_conversation cc ON r.roomid = cc.roomid 
        LEFT JOIN interviews.coding_conversation_ratings ccr ON cc.id = ccr.coding_conversation_id
        WHERE cfm.formId = ? ${filterQuery} ${assignmentStatusQuery} ${interviewStatusQuery} ${interviewStartTimeQuery}
        GROUP BY c.email, c.name, c.id, ua.id, as2.id, as2.score, r.interview_taken, ts.start_time ${resultQuery}
        ${orderBy}
        LIMIT ? OFFSET ?`,
        [formId, pageSize, offset],
        (err, results, fields) => {
          if (err) {
            console.log("Error while fetching : ", err);
            return reject({ success: false, error: err });
          }
          resolve({
            success: true,
            data: {
              candidates: results,
              total: results.length > 0 ? results[0].totalCandidates : 0,
            },
          });
        }
      );
    } catch (error) {
      console.error("Error while fetching", error);
      reject({ success: false, error: error });
    }
  });
};

const fetchFormCandidatesV2 = async (
  formId,
  offset,
  pageSize,
  orderBy,
  columnFilters,
  interviewStatusQuery,
  interviewStartTimeQuery,
  resultQuery
) => {
  return new Promise(async (resolve, reject) => {
    const columnFiltersArray = columnFilters ? JSON.parse(columnFilters) : [];

    const filters = columnFiltersArray
      .map(({ id, value }) => {
        if (id === "email" || id === "name" || id === "id") {
          return `AND c.${id} LIKE '%${value}%'`;
        } else if (id === "assignmentScore") {
          return `AND as2.score LIKE "%${value}%"`;
        }
      })
      .join(" ");

    let filterQuery = "";
    if (filters) {
      filterQuery = `${filters}`;
    }

    try {
      pool.query(
        `SELECT 
        c.email, c.name, c.id, ua.id AS assignmentId,
        as2.id AS submissionId, as2.score AS assignmentScore,
        r.interview_taken AS interviewTaken, 
        ts.start_time AS startTime,
        AVG(COALESCE(ccr.rating_value, 0)) AS rating,
        CASE WHEN AVG(COALESCE(ccr.rating_value, 0)) > 6 THEN 'pass' ELSE 'fail' END AS result,
        COUNT(*) OVER() AS totalCandidates
        FROM interviews.candidate_form_mapping cfm
        LEFT JOIN interviews.candidate c ON c.id = cfm.candidateId 
        LEFT JOIN interviews.user_assignments ua ON ua.applicationid = cfm.id
        LEFT JOIN interviews.assignment_submissions as2 ON as2.user_assignment_id = ua.id 
        LEFT JOIN interviews.rooms r ON r.submission_id = as2.id 
        LEFT JOIN interviews.time_slots ts ON r.roomid = ts.roomid
        LEFT JOIN interviews.coding_conversation cc ON r.roomid = cc.roomid 
        LEFT JOIN interviews.coding_conversation_ratings ccr ON cc.id = ccr.coding_conversation_id
        WHERE cfm.formId = ? ${filterQuery} ${interviewStatusQuery} ${interviewStartTimeQuery}
        GROUP BY c.email, c.name, c.id, ua.id, as2.id, as2.score, r.interview_taken, ts.start_time ${resultQuery}
        ${orderBy}
        LIMIT ? OFFSET ?`,
        [formId, pageSize, offset],
        (err, results, fields) => {
          if (err) {
            console.log("Error while fetching : ", err);
            return reject({ success: false, error: err });
          }
          resolve({
            success: true,
            data: {
              candidates: results,
              total: results.length > 0 ? results[0].totalCandidates : 0,
            },
          });
        }
      );
    } catch (error) {
      console.error("Error while fetching", error);
      reject({ success: false, error: error });
    }
  });
};

const fetchTechStacks = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      pool.query(
        `WITH language_stacks AS (
        SELECT 
          plg.programming AS language, 
          plg_group.name AS stack
        FROM programming_language_group plg_group
        INNER JOIN programming_language_group_mapping m ON plg_group.id = m.programming_language_group_id
        INNER JOIN programming_language plg ON m.programming_language_id = plg.language_id
      )
      SELECT 
        stack,
        GROUP_CONCAT(DISTINCT language SEPARATOR ', ') AS languages
      FROM language_stacks
      GROUP BY stack
      ORDER BY stack;
    `,
        (err, results, fields) => {
          if (err) {
            console.log("Error while fetching : ", err);
            return reject(err);
          }
          resolve(results);
        }
      );
    } catch (error) {
      console.error("Error while fetching", error);
      reject(error);
    }
  });
};

const checkInterviewTaken = (roomId) => {
  return new Promise((resolve, reject) => {
    try {
      pool.query(
        `SELECT interview_taken FROM rooms WHERE roomId = ?;`, [roomId],
        (err, results, fields) => {
          if (err) {
            console.log("Error while fetching : ", err);
            return reject(err);
          }
          resolve(results[0]);
        }
      );
    } catch (error) {
      console.error("Error while fetching", error);
      reject(error);
    }
  })
}

const checkMergingStatusData = (roomId) => {
  return new Promise((resolve, reject) => {
    try {
      pool.query(
        `SELECT file_create_time FROM rooms WHERE roomId = ?;`, [roomId],
        (err, results, fields) => {
          if (err) {
            console.log("Error while fetching : ", err);
            return reject(err);
          }
          resolve(results[0]);
        }
      );
    } catch (error) {
      console.error("Error while fetching", error);
      reject(error);
    }
  })
}

const updateTimeSlotOccupiedCount = async (slot) => {
  return new Promise((resolve, reject) => {
    try {
      const date = slot.start_time.toISOString().split("T")[0];
      const start_time = slot.start_time
        .toISOString()
        .split("T")[1]
        .split(".")[0];
      const end_time = slot.end_time.toISOString().split("T")[1].split(".")[0];

      pool.query(
        `
            UPDATE time_slots_availability
            SET occupied_count = occupied_count - 1
            WHERE date = ?
              AND start_time >= ?
              AND end_time <= ?
          `,
        [date, start_time, end_time],
        (err, results, fields) => {
          if (err) {
            console.log("Error while updating time slot availability : ", err);
            return reject(err);
          }
          resolve(results[0]);
        }
      );
    } catch (error) {}
  });
};

module.exports = {
  pool,
  promisePool,
  queryDatabase,
  executeQueries,
  fetchRatings,
  fetchInitialQuestions,
  fetchSubmissionDetails,
  getRoomData,
  insertCodingConversation,
  updateCodingConversationAndAddRating,
  insertConversation,
  generateConversationRatings,
  insertConversationRatings,
  insertCodingHint,
  generateInterviewSummary,
  insertSummaryAndUpdateInterview,
  insertIntoAssignmentSubmissions,
  findPendingAssignmentById,
  findGenericAssignmentByAlias,
  findSubmittedAssignmentById,
  findSubmittedAssignmentByAssignmentId,
  findSubmittedAssignmentByLink,
  insertTimeSlot, //same
  createRoom,
  createTimeSlot, //same
  hasRoomWithSubmissionId,
  findSubmittedGenericAssignment,
  storeEmbeddings,
  isDemo,
  fetchEmbeddings,
  fetchFormCandidatesV1,
  fetchFormCandidatesV2,
  fetchTechStacks,
  checkInterviewTaken,
  checkMergingStatusData,
  updateTimeSlotOccupiedCount,
  fetchAllSkills,
};
