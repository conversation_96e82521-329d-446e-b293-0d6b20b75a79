const prisma = require("../utils/prisma");

//* check company name is taken or not
const getCompany = async (company) => {
	try {
		const checkCompanyName = await prisma.company.findUnique({ where: { name: company } });
		return checkCompanyName ? { success: false, msg: "Company name is taken" } : { success: true, msg: "Company name is available" };
	} catch (error) {
		console.error(error);
		throw error;
	}
};

//* insert company in database
const insertCompany = async (company) => {
	try {
		const createCompany = await prisma.company.create({
			data: {
				name: company,
				company_techyrr: 1,
			},
		});
		return createCompany;
	} catch (error) {
		console.error(error);
		throw error;
	}
};

module.exports = { getCompany, insertCompany };
