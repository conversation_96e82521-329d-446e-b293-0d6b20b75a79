class BookingSlotCache {
  constructor(redisClient) {
    this.redisClient = redisClient;
  }

  async set(submissionId, data, expirySeconds = 600) {
    return await this.redisClient.setBookingSlotCache(
      submissionId,
      data,
      expirySeconds
    );
  }

  async get(submissionId) {
    return await this.redisClient.getBookingSlotCache(submissionId);
  }
}

module.exports = BookingSlotCache;
