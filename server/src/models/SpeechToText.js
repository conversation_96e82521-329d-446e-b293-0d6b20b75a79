const sdk = require("microsoft-cognitiveservices-speech-sdk");
const { createClient, LiveTranscriptionEvents } = require("@deepgram/sdk");
const { ROOMS } = require("../cache");

class DeepgramSST {
  constructor(roomId) {
    this.deepgramClient = createClient(process.env.DEEPGRAM_API_KEY);
    this.deepgram = null;
    this.keepAliveInterval = null;
    this.roomId = roomId;
  }

  setup() {
    return new Promise((resolve, reject) => {
      this.deepgram = this.deepgramClient.listen.live({
        language: "en-IN",
        punctuate: true,
        smart_format: true,
        model: "nova-2-general",
      });

      if (this.keepAliveInterval) clearInterval(this.keepAliveInterval);

      this.keepAliveInterval = setInterval(() => {
        if (this.deepgram?.getReadyState() === 1) this.deepgram.keepAlive();
      }, 10 * 1000);

      this.addListeners();

      this.deepgram.once("open", () => {
        resolve();
      });
      this.deepgram.once("error", (error) => {
        reject(error);
      });
    });
  }

  addListeners() {
    this.deepgram.addListener(LiveTranscriptionEvents.Open, async () => {
      console.log("deepgram: connected");
    });

    this.deepgram.addListener(LiveTranscriptionEvents.Close, async () => {
      console.log("deepgram: disconnected");
      clearInterval(this.keepAliveInterval);
    });

    this.deepgram.addListener(LiveTranscriptionEvents.Error, async (error) => {
      console.log("deepgram: error recieved");
      ROOMS.setResponse(this.roomId, {
        error_type: "DEEPGRAM",
        error_details: error,
      });
    });

    this.deepgram.addListener(LiveTranscriptionEvents.Warning, async (warning) => {
      console.log("deepgram: warning recieved");
      console.warn(warning);
    });

    this.deepgram.addListener(LiveTranscriptionEvents.Transcript, (data) => {
      const transcript = data.channel.alternatives[0].transcript ?? "";
      console.log("deepgram: transcript received", transcript);
      ROOMS.setResponse(this.roomId, {
        answer: (ROOMS.getRoom(this.roomId).response.answer + " " + transcript).replace(/\s+/g, " "),
      });
    });
  }

  transcribeAudio(audioBuffer) {
    if (this.deepgram?.getReadyState() === 1 /* OPEN */) {
      this.deepgram.send(audioBuffer);
    } else if (this.deepgram?.getReadyState() >= 2 /* CLOSING */) {
      console.log("socket: data couldn't be sent to deepgram");
      console.log("socket: retrying connection to deepgram");
      /* Attempt to reopen the Deepgram connection */
      this.deepgram.finish();
      this.deepgram.removeAllListeners();
      this.setup();
    } else {
      console.log("socket: data couldn't be sent to deepgram");
    }
  }

  clearTranscriptions() {}

  close() {
    this.deepgram.finish();
    this.deepgram.removeAllListeners();
    clearInterval(this.keepAliveInterval);
  }
}

class AzureSTT {
  constructor(roomId, deviceConfig) {
    const speechConfig = sdk.SpeechConfig.fromSubscription(
      process.env.AZURE_SPEECH_KEY,
      process.env.AZURE_SPEECH_REGION
    );
    this.roomId = roomId;
    this.transcriptions = [];
    console.log("device config", deviceConfig);
    this.audioStream = sdk.AudioInputStream.createPushStream(
      sdk.AudioStreamFormat.getWaveFormatPCM(
        deviceConfig.sampleRate,
        deviceConfig.sampleSize,
        deviceConfig.channelCount
      )
    );
    this.audioConfig = sdk.AudioConfig.fromStreamInput(this.audioStream);
    this.recognizer = new sdk.SpeechRecognizer(speechConfig, this.audioConfig);
    this.recognizer.recognizing = (s, e) => {
      const transcript = e.result.text;
      // console.log(e.result);
      console.log(`RECOGNIZING: Text=${e.result.text}`);
      this.transcriptions.push(transcript);

      let transcriptionsResampled = "";
      console.log(this.transcriptions);
      for (let i = 0; i < this.transcriptions.length - 1; i++) {
        if (!this.transcriptions[i + 1].startsWith(this.transcriptions[i])) {
          transcriptionsResampled += this.transcriptions[i] + " ";
        }
      }
      transcriptionsResampled += this.transcriptions[this.transcriptions.length - 1];
      ROOMS.setResponse(this.roomId, {
        answer: transcriptionsResampled,
      });
    };
    this.recognizer.recognized = (s, e) => {
      if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
        const transcript = e.result.text;
        console.log(`RECOGNIZED: Text=${transcript}`);
      } else if (e.result.reason === sdk.ResultReason.NoMatch) {
        console.log("NOMATCH: Speech could not be recognized.");
      }
    };
    this.recognizer.sessionStarted;
    this.recognizer.canceled = (s, e) => {
      console.log(`CANCELED: Reason=${e.reason}`);
      if (e.reason === sdk.CancellationReason.Error) {
        console.log(`CANCELED: ErrorCode=${e.errorCode}`);
        console.log(`CANCELED: ErrorDetails=${e.errorDetails}`);
      }
      this.recognizer.stopContinuousRecognition();
    };
    this.recognizer.sessionStopped = (s, e) => {
      console.log("Azure STT Session stopped");
      this.recognizer.stopContinuousRecognition();
    };
  }

  transcribeAudio(audioBuffer) {
    const buffer = audioBuffer.buffer.slice(audioBuffer.byteOffset, audioBuffer.byteOffset + audioBuffer.byteLength);
    this.audioStream.write(buffer);
  }

  clearTranscriptions() {
    this.transcriptions = [];
  }

  setup() {
    return new Promise((resolve, reject) => {
      this.recognizer.startContinuousRecognitionAsync(
        () => resolve(),
        (err) => reject(err)
      );
    });
  }

  close() {
    this.recognizer.close();
  }
}

class SpeechToTextFactory {
  static createSpeechToTextService(service, roomId, deviceConfig) {
    switch (service) {
      case "azure":
        return new AzureSTT(roomId, deviceConfig);
      case "deepgram":
        return new DeepgramSST(roomId);
      default:
        return new AzureSTT(roomId, deviceConfig);
    }
  }
}

module.exports = { SpeechToTextFactory };
