const { v4: uuidv4 } = require("uuid");

class MediasoupMessenger {
  constructor(roomId) {
    this.roomId = roomId;
    this.dataProducer = null;
    this.callbacks = new Map();
    this.onNewMessage = null;
  }

  setProducer = async (dataProducer) => {
    this.dataProducer = dataProducer;

    if (dataProducer.readyState !== "open") {
      await new Promise((resolve) => dataProducer.once("open", resolve));
      console.log("Data producer is open");
    } else {
      console.log("Data producer is open");
    }

    dataProducer.on("close", () => {
      console.log("Data producer closed");
      this.dataProducer = null;
      this.callbacks.clear();
    });

    dataProducer.on("error", (error) => {
      console.error("Data producer error:", error);
    });
  };

  emit = (event, data, callback = null) => {
    return new Promise((resolve, reject) => {
      const messageId = uuidv4();
      const waitForResponse = typeof callback === "function";
      const message = {
        id: messageId,
        event,
        data,
        waitForResponse,
      };

      if (this.dataProducer && this.dataProducer.readyState === "open") {
        if (waitForResponse) {
          const handleResponse = (response) => {
            if (callback && typeof callback === "function") {
              callback(response);
            }
            resolve(response);
          };
          this.callbacks.set(messageId, handleResponse);
        }
        this.dataProducer.send(JSON.stringify(message));
        console.log("Data message sent:", message);
        if (!waitForResponse) {
          resolve();
        }
      } else {
        const error = new Error("Data producer is not open");
        if (callback && typeof callback === "function") {
          callback({ success: false, error: error.message });
        }
        reject(error);
      }
    });
  };

  sendResponse = (originalMessageId, responseData) => {
    if (this.dataProducer && this.dataProducer.readyState === "open") {
      const responseMessage = {
        id: originalMessageId,
        data: responseData,
      };
      this.dataProducer.send(JSON.stringify(responseMessage));
      console.log("Response sent:", responseMessage);
    } else {
      throw new Error("Data producer is not open, cannot send response");
    }
  };

  handleIncomingMessage = async (rawMessage) => {
    const message = JSON.parse(rawMessage);
    console.log("Received message:", message);

    if (this.callbacks.has(message.id)) {
      // This is a response to a message we sent earlier
      const handleResponse = this.callbacks.get(message.id);
      handleResponse(message.data);
      this.callbacks.delete(message.id);
    } else if (this.onNewMessage) {
      // This is a new message
      this.onNewMessage(message, (responseData) => {
        if (message.waitForResponse) {
          this.sendResponse(message.id, responseData);
        }
      });
    }
  };
}

module.exports = MediasoupMessenger;
