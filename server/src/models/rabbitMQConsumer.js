const amqp = require("amqp-connection-manager");
const { deleteDir } = require("../utils/deleteDir");
const fetchCandidateDataForDownload = require("../apis/fetchCandidateDataForDownload");

class RabbitMQConsumer {
  static connection = null;

  static connect(url) {
    return new Promise((resolve, reject) => {
      try {
        this.connection = amqp.connect(url || process.env.AMQP_URL);

        this.connection.on("connect", () => {
          console.log("Successfully connected to RabbitMQ");
          resolve(this);
        });

        this.connection.on("disconnect", (err) => {
          console.error("Disconnected from RabbitMQ:", err);
          reject(err);
        });
      } catch (error) {
        console.error("Error connecting to RabbitMQ:", error);
        reject(error);
      }
    });
  }

  static getRoutingKey(queueName) {
    return `${queueName}.${process.env.CONTAINER_APP_REVISION}`;
  }

  static consumeQueue(
    queueName,
    routingKey = null,
    exchangeName = null,
    processMessage
  ) {
    if (!this.connection) {
      throw new Error(
        `Not connected to RabbitMQ. Call connect() first. Queue: ${queueName}`
      );
    }

    if (!routingKey) {
      routingKey = queueName;
    }

    const channelWrapper = this.connection.createChannel({
      json: true,
      heartbeatIntervalInSeconds: process.env.HEARTBEAT_INTERVAL_IN_SECONDS,
      reconnectTimeInSeconds: process.env.RECONNECT_TIME_IN_SECONDS,
      setup: async (channel) => {
        console.log(
          `Setting up consumer for queue: ${queueName} with routing key: ${routingKey}`
        );

        await channel.assertQueue(queueName, { durable: true });
        if (exchangeName) {
          await channel.bindQueue(queueName, exchangeName, routingKey);
        }
        await await channel.prefetch(1);

        await channel.consume(queueName, async (msg) => {
          if (msg) {
            let message;
            try {
              message = JSON.parse(msg.content);
            } catch (error) {
              console.error(`Invalid JSON message from ${queueName}:`, error);
              channel.ack(msg);
              return;
            }

            try {
              console.log(`Received message from ${queueName}:`, message);
              await processMessage(message);
              channel.ack(msg);
              console.log(`Successfully processed message from ${queueName}`);
            } catch (error) {
              console.error(
                `Error processing message from ${queueName}:`,
                error
              );
              channel.ack(msg);
            }
          }
        });

        console.log(`Started consuming messages from queue: ${queueName}`);
      },
    });

    return channelWrapper;
  }
}

async function connectRabbitMQ() {
  try {
    await RabbitMQConsumer.connect();

    RabbitMQConsumer.consumeQueue(
      process.env.DELETE_REPO_QUEUE,
      RabbitMQConsumer.getRoutingKey(process.env.DELETE_REPO_QUEUE),
      process.env.DELAYED_EXCHANGE,
      deleteDir
    );

    RabbitMQConsumer.consumeQueue(
      process.env.QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA,
      null,
      null,
      fetchCandidateDataForDownload
    );

    process.on("SIGINT", () => {
      console.log("Closing connection to RabbitMQ");
      RabbitMQConsumer.connection.close();
    });
  } catch (error) {
    console.error("Failed to set up RabbitMQ consumers:", error);
  }
}

module.exports = connectRabbitMQ;
