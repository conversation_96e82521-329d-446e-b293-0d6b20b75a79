const EventEmitter = require("events");

class ResponseQueue extends EventEmitter {
  constructor() {
    super();
    this.queue = [];
    this.promiseQueue = [];
    this.responseHandler = null;
    this.storedArgs = [];
    this.cleanedUp = false;

    this.on("promiseResolved", this.checkAndHandleResponses.bind(this));
  }

  addResponse(response, responseHandler, ...args) {
    if (this.cleanedUp) return;

    this.queue.push(response);
    if (this.promiseQueue.length === 0) {
      const responsesToSend = this.flushQueue();
      this.pushPromise(responseHandler(responsesToSend, ...args)).finally(
        () => {
          this.checkAndHandleResponses();
        }
      );
    } else {
      this.responseHandler = responseHandler;
      this.storedArgs = args;
      // this.checkAndHandleResponses();
    }
  }

  pushPromise(promise) {
    if (this.cleanedUp) return promise;

    const trackedPromise = this.trackPromise(promise);
    this.promiseQueue.push(trackedPromise);
    return promise;
  }

  trackPromise(promise) {
    const id = Date.now();
    const state = { id, isFulfilled: false, isRejected: false };
    const controller = new AbortController();

    const wrappedPromise = Promise.race([
      promise,
      new Promise((_, reject) => {
        controller.signal.addEventListener("abort", () => {
          reject(new Error("Operation cancelled"));
        });
      }),
    ])
      .then(
        (result) => {
          state.isFulfilled = true;
          return result;
        },
        (error) => {
          state.isRejected = true;
        }
      )
      .finally(() => {
        if (!this.cleanedUp) {
          this.emit("promiseResolved");
        }
        this.promiseQueue = this.promiseQueue.filter((p) => p.state.id !== id);
      });

    return { promise: wrappedPromise, state, controller };
  }

  allPromisesResolved() {
    return this.promiseQueue.every(
      (p) => p.state.isFulfilled || p.state.isRejected
    );
  }

  checkAndHandleResponses() {
    if (this.cleanedUp) return;

    if (this.allPromisesResolved() && this.responseHandler) {
      const responsesToSend = this.flushQueue();

      this.pushPromise(
        this.responseHandler(responsesToSend, ...this.storedArgs)
      ).finally(() => {
        this.responseHandler = null;
        this.storedArgs = [];
        this.checkAndHandleResponses();
      });
    }
  }

  cancel() {
    this.promiseQueue.forEach((tracked) => {
      tracked.controller.abort();
    });

    this.queue = [];
    this.responseHandler = null;
    this.storedArgs = [];

    this.emit("cancelled");
  }

  cleanup(final = false) {
    this.cleanedUp = final;
    this.cancel();
  }

  flushQueue() {
    const responsesToSend = {
      response: this.queue.reduce(
        (prev, curr) => prev + " " + curr.response,
        ""
      ),
      filesData:
        this.queue.length > 0
          ? this.queue[this.queue.length - 1].filesData
          : "",
    };
    this.queue = [];
    return responsesToSend;
  }
}

module.exports = ResponseQueue;
