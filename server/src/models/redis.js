const { Redis } = require("ioredis");
const { redisRoomPrefix } = require("../constants/roomConstants");

class RedisClient {
  constructor() {
    this.client = new Redis({
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || "6379"),
      password: process.env.REDIS_PASSWORD,
      tls:
        process.env.NODE_ENV === "production" ||
        process.env.NODE_ENV === "staging"
          ? {}
          : undefined,
      lazyConnect: true,
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
    });

    this.client.on("error", (error) => {
      console.error("Redis connection error:", error);
    });

    this.client.on("connect", () => {
      console.log("Successfully connected to Redis");
    });

    this.client.on("ready", () => {
      console.log("Redis is ready to accept commands");
    });

    this.client.on("close", () => {
      console.log("Redis connection closed");
    });
  }

  _getRoomKey(roomId) {
    return `${redisRoomPrefix}${roomId}`;
  }

  _stringifyValue(value) {
    return typeof value === "object" ? JSON.stringify(value) : value;
  }

  _parseValue(value, isJson = false) {
    if (!value) return isJson ? {} : value;
    try {
      return isJson ? JSON.parse(value) : value;
    } catch {
      return value;
    }
  }

  async exists(roomId) {
    const roomKey = this._getRoomKey(roomId);
    console.log("checking if room exists in redis", roomKey);
    return await this.client.exists(roomKey);
  }

  async setRoomData(roomId, data, expirySeconds = 0) {
    const roomKey = this._getRoomKey(roomId);
    const exists = await this.exists(roomId);
    if (exists) {
      console.log("room already exists in redis", roomId);
      return;
    }
    const processedData = Object.entries(data).reduce((acc, [key, value]) => {
      acc[key] = this._stringifyValue(value);
      return acc;
    }, {});

    console.log("setRoomData processedData", processedData);

    await this.client.hset(roomKey, processedData);
    if (expirySeconds > 0) {
      await this.client.expire(roomKey, expirySeconds);
    }
  }

  async getRoomData(roomId) {
    const roomKey = this._getRoomKey(roomId);
    const data = await this.client.hgetall(roomKey);

    if (Object.keys(data).length === 0) {
      return null;
    }

    const processedData = Object.entries(data).reduce((acc, [key, value]) => {
      if (value === "true" || value === "false") {
        acc[key] = value === "true";
      } else if (!isNaN(value) && value !== "") {
        acc[key] = Number(value);
      } else {
        try {
          acc[key] = JSON.parse(value);
        } catch {
          acc[key] = value;
        }
      }
      return acc;
    }, {});

    console.log("REDIS room data:", roomKey, processedData);

    return processedData;
  }

  async updateRoomField(roomId, field, value) {
    const roomKey = this._getRoomKey(roomId);
    return await this.client.hset(roomKey, {
      [field]: this._stringifyValue(value),
    });
  }

  async deleteRoom(roomId) {
    const roomKey = this._getRoomKey(roomId);
    console.log("REDIS: deleting room:", roomKey);
    return await this.client.del(roomKey);
  }

  // Booking Slot Cache
  async setBookingSlotCache(submissionId, data, expirySeconds = 0) {
    const key = `bookingSlotCache:${submissionId}`;
    data = Object.fromEntries(data);
    const processedData = Object.entries(data).reduce((acc, [key, value]) => {
      acc[key] = this._stringifyValue(value);
      return acc;
    }, {});

    await this.client.hset(key, processedData);
    if (expirySeconds > 0) {
      await this.client.expire(key, expirySeconds);
    }
  }

  async getBookingSlotCache(submissionId) {
    const key = `bookingSlotCache:${submissionId}`;
    const data = await this.client.hgetall(key);

    if (Object.keys(data).length === 0) {
      return null;
    }

    const processedData = new Map(
      Object.entries(data).map(([key, value]) => [key, value])
    );

    processedData.originalSetMethod = processedData.set;
    processedData.set = async (key, value) => {
      await this.setBookingSlotCache(
        submissionId,
        processedData.originalSetMethod(key, value),
        600
      );
    };

    return processedData;
  }

  async deleteBookingSlotCache(submissionId) {
    const key = `bookingSlotCache:${submissionId}`;
    console.log(`Deleting booking slot cache for key: ${key}`);
    return await this.client.del(key);
  }

  // Locks Cache
  async setLock(identifier, expirySeconds = 0) {
    const key = `lock:${identifier}`;
    const value = "true";
    if (expirySeconds > 0) {
      await this.client.setex(key, expirySeconds, value);
    } else {
      await this.client.set(key, value);
    }
  }

  async getLock(identifier) {
    const key = `lock:${identifier}`;
    return await this.client.get(key);
  }

  async deleteLock(identifier) {
    const key = `lock:${identifier}`;
    return await this.client.del(key);
  }

  async getMaxRoomCount() {
    return parseInt(
      (await this.client.get("max-room-count")) ??
        process.env.NUMBER_OF_INTERVIEWS_OVERLAP ??
        1
    );
  }
}

module.exports = RedisClient;
