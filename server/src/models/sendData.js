const sendMessage = require("../utils/udp.js");
const fetchWithRetry = require("../utils/fetchWithRetry.js");
const { ROOMS } = require("../cache/index.js");

class UDPDataSender {
  sendData(data) {
    const { audioData, roomId, audioId, audioFormat, deviceConfig } = data;
    const audioBuffer = new Buffer.from(audioData).toString("base64");
    const buffer = JSON.stringify({
      chunk: audioBuffer,
      roomId: roomId,
      audioId: audioId,
      audioFormat: audioFormat,
      deviceConfig: deviceConfig,
    });
    sendMessage(buffer);
  }
}

class TCPDataSender {
  sendData(data) {
    const { roomId, start_end } = data;

    fetchWithRetry(
      `http://${process.env.UDP_SERVER_ADDRESS}:5000/${start_end}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ roomId: roomId }),
      },
      start_end === "start" ? 0 : 3
    )
      .then((response) => {
        console.log("Connnection to UDP server succeed");
        if (start_end === "start") ROOMS.setShouldSendAudioToUDPServer(roomId, true);
      })
      .catch((error) => {
        console.error("Connnection to UDP server failed:", error);
        if (start_end === "start") ROOMS.setShouldSendAudioToUDPServer(roomId, false);
      });
  }
}

class DataSenderFactory {
  static createDataSender(type) {
    switch (type) {
      case "udp":
        return new UDPDataSender();
      case "tcp":
        return new TCPDataSender();
      default:
        throw new Error(`Invalid type: ${type}`);
    }
  }
}

module.exports = DataSenderFactory;
