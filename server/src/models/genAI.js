class VertexAIModel {
  constructor(model) {
    const { VertexAI } = require("@google-cloud/vertexai");
    this.model = model || process.env.VERTEXAI_MODEL || "gemini-1.0-pro";
    this.client = new VertexAI({
      project: process.env.VERTEXAI_PROJECT_ID,
      location: process.env.VERTEXAI_LOCATION,
    }).getGenerativeModel({
      model: this.model,
      generationConfig: {
        temperature: 0,
        responseMimeType: "application/json",
        candidateCount: 1,
      },
    });
  }

  async getResultFromPrompt(roomId, prompt) {
    if (Array.isArray(prompt)) {
      prompt = prompt.reduce(
        (acc, message) => acc + message.content + "\n",
        ""
      );
    }
    console.log("gpt prompt:", roomId, JSON.stringify(prompt, null, 2));

    try {
      const request = {
        contents: [
          {
            role: "user",
            parts: [{ text: prompt }],
          },
        ],
      };
      const res = await this.client.generateContent(request);
      const result = res.response.candidates[0].content.parts[0].text;

      console.log(roomId, "gpt response:", result);
      return result;
    } catch (er) {
      console.log("Error while running prompt - ", er);
      return null;
    }
  }
}

class AzureOpenAIModel {
  constructor(model) {
    const { AzureOpenAI } = require("openai");
    this.model = model || process.env.AZURE_OPENAI_DEPLOYMENT || "gpt-4o";
    this.client = new AzureOpenAI({
      endpoint: process.env.AZURE_OPENAI_ENDPOINT,
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      apiVersion: process.env.AZURE_OPENAI_MODEL,
      deployment: this.model,
    });
  }

  async getResultFromPrompt(roomId, prompt, jsonResponseRequired = false) {
    if (Array.isArray(prompt)) {
      prompt = prompt.reduce(
        (acc, message) => acc + message.content + "\n",
        ""
      );
    }

    if (jsonResponseRequired) {
      prompt =
        "You must provide a JSON response structure as mentioned.\n" + prompt;
    }

    const messages = [
      {
        role: "system",
        content: prompt,
      },
    ];

    console.log("gpt prompt azure:", roomId, JSON.stringify(messages, null, 2));

    try {
      const res = await this.client.chat.completions.create({
        messages: messages,
        model: this.model,
        ...(this.model.startsWith("o")
          ? {
              reasoning_effort:
                process.env.AZURE_OPENAI_REASONING_EFFORT || "high",
            }
          : {
              temperature: 0,
            }),
        response_format: {
          type: jsonResponseRequired ? "json_object" : "text",
        },
      });
      const result = res.choices[0].message.content;
      console.log(roomId, "gpt response azure:", result);
      return result;
    } catch (er) {
      console.log("Error while running prompt - ", er);
      return null;
    }
  }
}

class ClaudeModel {
  constructor(model) {
    const Anthropic = require("@anthropic-ai/sdk");
    this.model =
      model || process.env.CLAUDE_MODEL || "claude-3-5-sonnet-v2@20241022";
    this.client = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });
  }

  async getResultFromPrompt(roomId, prompt) {
    if (Array.isArray(prompt)) {
      prompt = prompt.reduce(
        (acc, message) => acc + message.content + "\n",
        ""
      );
    }

    const messages = [
      {
        role: "user",
        content: prompt,
      },
    ];

    console.log(
      "gpt prompt claude:",
      roomId,
      JSON.stringify(messages, null, 2)
    );

    try {
      const stream = await this.client.messages.create({
        model: this.model,
        messages: messages,
        max_tokens: 8192,
        stream: true,
      });

      let fullContent = "";

      for await (const chunk of stream) {
        if (chunk.type == "message_start") {
          console.log("input token count:", chunk.message.usage.input_tokens);
        }
        if (chunk.type == "content_block_start") {
          fullContent += chunk.content_block.text;
        }
        if (chunk.type == "content_block_delta") {
          fullContent += chunk.delta.text;
        }
        if (chunk.type == "message_delta") {
          console.log("output token count:", chunk.usage.output_tokens);
        }
      }

      console.log(roomId, "gpt response claude:", fullContent);
      return fullContent;
    } catch (er) {
      console.log("Error while running prompt - ", er);
      return null;
    }
  }
}

const {
  BedrockRuntimeClient,
  InvokeModelWithResponseStreamCommand,
} = require("@aws-sdk/client-bedrock-runtime");

class BedrockModel {
  constructor(model) {
    this.client = new BedrockRuntimeClient({
      region: process.env.AWS_REGION || "us-east-2",
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });

    this.model = model || process.env.BEDROCK_MODEL;
    this.claudeFallback = ModelFactory.getInstance("Claude");
  }

  async getResultFromPrompt(roomId, prompt) {
    try {
      if (Array.isArray(prompt)) {
        prompt = prompt.reduce(
          (acc, message) => acc + message.content + "\n",
          ""
        );
      }
      console.log(
        "gpt prompt bedrock:",
        roomId,
        JSON.stringify(prompt, null, 2)
      );

      const payload = {
        anthropic_version: "bedrock-2023-05-31",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: prompt,
              },
            ],
          },
        ],
        max_tokens: 64000,
        stop_sequences: ["\n\nHuman:"],
      };

      const command = new InvokeModelWithResponseStreamCommand({
        modelId: this.model,
        contentType: "application/json",
        accept: "application/json",
        body: JSON.stringify(payload),
      });

      let fullContent = "";
      const response = await this.client.send(command);

      for await (const chunk of response.body) {
        try {
          const decoded = new TextDecoder().decode(chunk.chunk.bytes);
          try {
            const parsedChunk = JSON.parse(decoded);

            if (parsedChunk.type == "message_start") {
              console.log(
                "input token count:",
                parsedChunk.message.usage.input_tokens
              );
            }
            if (parsedChunk.type == "content_block_start") {
              fullContent += parsedChunk.content_block.text;
            }
            if (parsedChunk.type == "content_block_delta") {
              fullContent += parsedChunk.delta.text;
            }
            if (parsedChunk.type == "message_delta") {
              console.log(
                "output token count:",
                parsedChunk.usage.output_tokens
              );
            }
          } catch (parseError) {
            console.log(
              "Error parsing chunk:",
              parseError,
              "Raw decoded:",
              decoded
            );
          }
        } catch (e) {
          continue;
        }
      }

      console.log(roomId, "gpt response bedrock:", fullContent);

      return fullContent;
    } catch (er) {
      console.log("Bedrock error while running prompt - ", er);
      console.log("Falling back to Claude");
      const fallbackResponse = await this.claudeFallback.getResultFromPrompt(
        prompt
      );

      return fallbackResponse;
    }
  }
}

class ModelFactory {
  static instances = {};

  static getInstance(provider, model) {
    const key = model ? `${provider}:${model}` : provider;

    if (!ModelFactory.instances[key]) {
      let instance;
      switch (provider) {
        case "VertexAI":
          instance = new VertexAIModel(model);
          break;
        case "AzureOpenAI":
          instance = new AzureOpenAIModel(model);
          break;
        case "Claude":
          instance = new ClaudeModel(model);
          break;
        case "Bedrock":
          instance = new BedrockModel(model);
          break;
        default:
          throw new Error(`Invalid model provider: ${provider}`);
      }
      ModelFactory.instances[key] = instance;
    }
    return ModelFactory.instances[key];
  }
}

module.exports = ModelFactory;
