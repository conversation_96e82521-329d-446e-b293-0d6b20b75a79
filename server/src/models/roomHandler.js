const { ROOMS, GENAI } = require("../cache/index.js");
const moment = require("moment-timezone");
const callDataLayer = require("../utils/callDataLayer.js");
const {
  fetchSubmissionDetails,
  insertCodingConversation,
  insertConversation,
  insertCodingHint,
} = require("../database/index.js");
const {
  getFromRealtimeDatabase,
  storeInRealtimeDatabase,
} = require("../firebase/index.js");
const {
  roomStates,
  nonRepeatingCases,
  modes,
  botStates,
} = require("../constants/roomConstants");
const {
  giveHintOnSubmissionPrompt,
  hintPrompt,
  introductionScorePrompt,
  nextCodingQuestionPrompt,
  repeatLastCodingQuestionPrompt,
  taskEvaluationPrompt,
  generateSyntaxPrompt,
  hintDSAPrompt,
  taskEvaluationDSAPrompt,
} = require("../utils/prompts");
const {
  streamAIAudio,
  getJsonFromPromptWithRetries,
} = require("../utils/index.js");
const staticTexts = require("../utils/staticTexts.js");
const ModelFactory = require("../models/genAI.js");
const isCompanyTechyrr = require("../utils/checkTechyrrCompany.js");

class BaseRoomHandler {
  constructor(roomId) {
    this.roomId = roomId;
  }

  async generateRoom(roomData) {
    const { roomid, ...other } = roomData;
    await ROOMS.createRoom({
      roomId: this.roomId,
      ...other,
    });
  }

  async initialize() {
    const initialPrompt = "INTRO PROMPT HERE (ENGLISH)";

    await ROOMS.pushToConversation(this.roomId, {
      role: "assistant",
      content: initialPrompt,
    });

    ROOMS.setResponse(roomId, { question: initialPrompt });
  }

  async handleRejoin() {
    //need to implement for introduction and english room
  }

  isLastResponse() {
    const roomEndTime = ROOMS.getRoom(this.roomId).end_time;
    return roomEndTime - moment().valueOf() < 120000;
  }

  async generateNextResponse(prompt, genAIModel) {
    const result = await getJsonFromPromptWithRetries(
      genAIModel || GENAI,
      prompt,
      "getResultFromPrompt",
      5,
      this.roomId
    );
    if (!result) return { response: staticTexts.errorResponse };
    return result;
  }

  async streamAIAudio(text) {
    await streamAIAudio(
      this.roomId,
      text || ROOMS.getResponse(this.roomId).question
    );
  }

  async sendResponse(text, sendTextWithVoice = true, useBothModes = false) {
    const mode = ROOMS.getMode(this.roomId);
    const response = text || ROOMS.getResponse(this.roomId).question;

    console.log("Mode of response:", useBothModes ? "Both" : mode);

    if (!response) {
      console.log("No text to synthesize");
      return;
    }

    if (useBothModes) {
      ROOMS.sendChatMessage(this.roomId, response);
      await this.streamAIAudio(response);
      return;
    }

    if (mode === modes.text) {
      ROOMS.sendChatMessage(this.roomId, response);
      return;
    }

    if (mode === modes.voice) {
      if (sendTextWithVoice) {
        ROOMS.sendChatMessage(this.roomId, response);
      }

      await this.streamAIAudio(response);
    }
  }

  async pushToConversation(content) {
    ROOMS.setResponse(this.roomId, { question: content });
    await ROOMS.pushToConversation(this.roomId, {
      role: "assistant",
      content: content,
    });
    const companyId = ROOMS.getCompanyId(this.roomId);
    if (companyId) {
      callDataLayer("insertConversation", companyId, "POST", {
        roomId: this.roomId,
        speaker: 0,
        text: content,
      }).catch((err) =>
        console.log("introductionHandler generateNextResponse", err)
      );
    } else {
      insertConversation(this.roomId, 0, content);
    }
  }
}

class IntroductionHandler extends BaseRoomHandler {
  constructor(roomId) {
    super(roomId);
  }

  getPrompt() {
    const { answer } = ROOMS.getResponse(this.roomId);
    return introductionScorePrompt(answer);
  }

  async generateNextResponse(prompt) {
    const result = await super.generateNextResponse(prompt);
    if (!Object.keys(result).includes("askAgain") || result.askAgain) {
      await this.pushToConversation(result.response);
    } else {
      if (ROOMS.getLanguage(this.roomId) == "en") {
        await this.handleEnglishRoomTransition();
      } else {
        await this.handleCodingRoomTransition();
      }
    }

    return result.response;
  }

  async handleEnglishRoomTransition() {
    await ROOMS.changeRoomState(this.roomId, roomStates.english_conversation);
    const englishRoomHandler = new EnglishRoomHandler(this.roomId);
    const prompt = englishRoomHandler.getPrompt();
    await englishRoomHandler.generateNextResponse(prompt);
  }

  //needs a revist after introduction is included in the conversation
  async handleCodingRoomTransition() {
    const codingRoomHandler = new CodeRoomHandler(this.roomId);
    const { question: nextQuestion } =
      await codingRoomHandler.getNextCodingQuestion();
    const prompt = staticTexts.codingAfterIntroPrompt(nextQuestion); //make static when intro is included in conversation
    await ROOMS.changeRoomState(this.roomId, roomStates.coding_conversation);
    // ROOMS.getUserSocket(this.roomId).emit("introduction-finished"); //move to messenger
    ROOMS.setResponse(this.roomId, { question: prompt });
  }
}

class EnglishRoomHandler extends BaseRoomHandler {
  constructor(roomId) {
    super(roomId);
  }

  //initialize method of base class is being called

  getPrompt() {
    return ROOMS.getConversation(this.roomId);
  }

  async generateNextResponse(prompt) {
    console.log("english room handler prompt:", prompt);
    const result = await super.generateNextResponse(prompt);
    console.log("english room handler result:", result);
    await this.pushToConversation(result.response);
    return result;
  }
}

class CodeRoomHandler extends BaseRoomHandler {
  constructor(roomId) {
    super(roomId);
  }

  async generateRoom(roomData) {
    const { submission_id, companyId, room_state } = roomData;
    if (!roomData.recommended_questions) {
      const { recommendedQuestions, plantUMLDiagram = "" } = await this.getRecommendedQuestions(
        submission_id,
        companyId,
        room_state
      );
      roomData.recommended_questions = recommendedQuestions || [];
      roomData.plantUMLDiagram = plantUMLDiagram || "";
    }
    await super.generateRoom(roomData);
  }

  async initialize() {
    const { question: nextQuestion, example } =
      await this.getNextCodingQuestion();
    const questionHasExample = example?.length;
    const prompt = staticTexts.codingWithoutIntroPrompt(
      nextQuestion,
      questionHasExample,
      isCompanyTechyrr(ROOMS.getCompanyId(this.roomId))
        ? "b2c"
        : ROOMS.getLicence(this.roomId)
    );
    await this.pushToConversation(prompt);
    await ROOMS.changeRoomState(this.roomId, ROOMS.getRoomState(this.roomId));
    // ROOMS.getUserSocket(this.roomId).emit("introduction-finished"); //move to messenger
  }

  async handleRejoin() {
    const rejoinPrompt = staticTexts.codingReconnectedPrompt();
    await this.pushToConversation(rejoinPrompt);
  }

  getPrompt(codeChanges) {
    const languages = ROOMS.getProgrammingLanguages(this.roomId);
    const lastQuesAnsPair = ROOMS.getLastCodingQuestionAnswer(this.roomId);
    const {
      question: codingQuestion,
      answer: codingAnswer,
      id,
      exampleGiven,
      example,
    } = lastQuesAnsPair; //coding question and answer
    const { answer } = ROOMS.getResponse(this.roomId); //verbal response from user
    const allHints = ROOMS.getHints(this.roomId);
    const previousHints = allHints.filter((hint) => hint.id === id);

    const remainingRecommendedQuestionsCount = ROOMS.getRecommendedQuestions(
      this.roomId
    ).length;
    const totalRecommendedQuestionsCount = ROOMS.getRecommendedQuestionsCount(
      this.roomId
    );
    const roomEndTime = ROOMS.getRoom(this.roomId).end_time; // Should be in IST (ms)
    const currentTimeMS = moment.utc().valueOf(); // Current IST time in ms
    const timeRemaining = parseInt(
      (roomEndTime - (currentTimeMS + 19800000)) / 60000
    );

    const excludedCases = ROOMS.getExcludedCases(this.roomId);

    switch (ROOMS.getRoomState(this.roomId)) {
      case roomStates.coding_conversation:
        return hintPrompt(
          languages,
          answer,
          codingQuestion,
          previousHints,
          codeChanges,
          excludedCases,
          example,
          timeRemaining,
          remainingRecommendedQuestionsCount,
          totalRecommendedQuestionsCount
        );
      case roomStates.dsa_coding_conversation:
        const plantUMLDiagram = ROOMS.getPlantUMLDiagram(this.roomId);
        const currentQuestionIndex = ROOMS.getCurrentQuestionIndex(this.roomId);
        const { expectedComplexity = "", constraints = "" } = ROOMS.getRecommendedQuestions(this.roomId)[currentQuestionIndex];
        return hintDSAPrompt(
          answer,
          codingQuestion,
          plantUMLDiagram,
          previousHints,
          codeChanges,
          excludedCases,
          expectedComplexity,
          constraints,
          timeRemaining,
          remainingRecommendedQuestionsCount,
          totalRecommendedQuestionsCount,
          currentQuestionIndex + 1,
        );
    }
  }

  async generateNextResponse(prompt) {
    const { answer } = ROOMS.getResponse(this.roomId);
    const { room_state } = ROOMS.getRoom(this.roomId);
    const { id } = ROOMS.getLastCodingQuestionAnswer(this.roomId);

    let result = await super.generateNextResponse(prompt);

    const companyId = ROOMS.getCompanyId(this.roomId);

    if (companyId) {
      callDataLayer("insertCodingHint", companyId, "POST", {
        roomId: this.roomId,
        coding_conversation_id: id,
        query: answer,
        hint: result.response,
        case_triggered: result.case,
      }).catch((err) =>
        console.log("codeRoomHandler generateNextResponse", err)
      );
    } else {
      insertCodingHint(this.roomId, id, answer, result.response, result.case);
    }
    const lastCase = ROOMS.getLastCase(this.roomId);
    await ROOMS.setLastCase(this.roomId, result.case);

    if (result.case == lastCase && nonRepeatingCases.includes(result.case))
      return result;

    switch (result.case) {
      // case "example":
      //   ROOMS.markCodingExampleGiven(this.roomId);
      //   break;
      // case "concept":
      //   ROOMS.popQuestionConcept(this.roomId);
      //   break;
      case "code_location":
        await ROOMS.addExcludedCase(this.roomId, "code_location");

      default:
        break;
    }

    ROOMS.setResponse(this.roomId, { question: result.response });

    if (room_state === roomStates.coding_conversation || room_state === roomStates.dsa_coding_conversation) {
      await ROOMS.pushToConversation(this.roomId, {
        role: "assistant",
        content: result.response,
      });
      await ROOMS.pushToHints(this.roomId, {
        query: answer,
        hint: result.response,
        id: id,
      });
    }

    return result;
  }

  async getRecommendedQuestions(submissionId, companyId, roomState) {
    try {
      const submissionDetails = companyId
        ? [
            (
              await callDataLayer(
                `submittedAssignment/${submissionId}`,
                companyId
              )
            ).data,
          ]
        : await fetchSubmissionDetails(submissionId);

      let recommendedQuestions = await getFromRealtimeDatabase(
        `${submissionDetails[0].firebase_link}/recommendedQuestions`
      );
      let plantUMLDiagram = "";
      if (roomState === roomStates.dsa_coding_conversation) {
        plantUMLDiagram = await getFromRealtimeDatabase(
          `${submissionDetails[0].firebase_link}/plantUMLDiagram`
        );
      }
      if (!recommendedQuestions || recommendedQuestions.length === 0) {
        console.log(
          "No recommended questions found for submission:",
          submissionId
        );
        const questionsV2 = await getFromRealtimeDatabase(
          `${submissionDetails[0].firebase_link}/questionsV2`
        );

        if (questionsV2) {
          console.log("questionsV2 found");
          const index = Math.floor(Math.random() * questionsV2.length);
          recommendedQuestions = questionsV2[index];
        } else {
          const questions = await getFromRealtimeDatabase(
            `${submissionDetails[0].firebase_link}/questions`
          );

          recommendedQuestions = [];

          questions.forEach((questionsArray) => {
            const index = Math.floor(Math.random() * questionsArray.length);
            recommendedQuestions.push(questionsArray[index]);
          });

          if (recommendedQuestions.length > 3)
            recommendedQuestions = recommendedQuestions.slice(0, 3);

          if (recommendedQuestions.length < 3) {
            const remainingQuestions = 3 - recommendedQuestions.length;
            const questionsArray = questions.flat();
            const remainingQuestionsArray = questionsArray.filter(
              (question) => !recommendedQuestions.includes(question)
            );
            for (let i = 0; i < remainingQuestions; i++) {
              if (remainingQuestionsArray.length === 0) break;
              const index = Math.floor(
                Math.random() * remainingQuestionsArray.length
              );
              recommendedQuestions.push(remainingQuestionsArray[index]);
              remainingQuestionsArray.splice(index, 1);
            }
          }
        }

        await storeInRealtimeDatabase(
          `${submissionDetails[0].firebase_link}/recommendedQuestions`,
          recommendedQuestions
        );
      }

      console.log("Recommended questions from firebase:", recommendedQuestions);
      console.log("PlantUML diagram from firebase:", plantUMLDiagram);
      return { recommendedQuestions, plantUMLDiagram };
    } catch (error) {
      console.log("Error fetching recommended questions:", error);
      throw error;
    }
  }

  displayCodingQuestion() {
    //mark recommended question as asked, (to get total count, remaing count,question in same method)
    const totalRecommendedQuestionsCount = ROOMS.getRecommendedQuestionsCount(
      this.roomId
    );

    const { question, fileName = "" } = ROOMS.getLastCodingQuestionAnswer(
      this.roomId
    );

    const recommendedQuestions = ROOMS.getRecommendedQuestions(this.roomId);
    const currentQuestionIndex = ROOMS.getCurrentQuestionIndex(this.roomId);

    console.log(
      "current question index",
      currentQuestionIndex
    );
    console.log("DISPLAY QUESTION:", question);

    const questionNumber = currentQuestionIndex + 1;

    const currentQuestionData = recommendedQuestions?.[currentQuestionIndex];

    const DSAQuestionData = ROOMS.getRoomState(this.roomId) === roomStates.dsa_coding_conversation ? {
      expectedComplexity: currentQuestionData?.expectedComplexity,
      exampleInputOutput: currentQuestionData?.exampleInputOutput,
      constraints: currentQuestionData?.constraints,
    } : {}

    const questionData = {
      question,
      fileName,
      ...DSAQuestionData,
      questionNumber,
      totalQuestions: totalRecommendedQuestionsCount,
    };

    ROOMS.sendQuestion(this.roomId, questionData);

    return questionData;
  }

  async getNextCodingQuestion() {
    const questionToBeAskedObj = await ROOMS.moveToNextQuestion(this.roomId);

    let results;

    const companyId = ROOMS.getCompanyId(this.roomId);
    if (companyId) {
      results = await callDataLayer(
        "insertCodingConversation",
        companyId,
        "POST",
        {
          roomId: this.roomId,
          question: questionToBeAskedObj.question,
        }
      );
    } else {
      results = await insertCodingConversation(
        this.roomId,
        questionToBeAskedObj.question
      );
    }

    await ROOMS.setLastGitDiff(this.roomId, "");
    await ROOMS.pushCodingQuestion(
      this.roomId,
      questionToBeAskedObj,
      companyId ? results.id : results.insertId
    );
    this.displayCodingQuestion();
    return questionToBeAskedObj;
  }

  async handleLastResponse() {
    ROOMS.getMessenger(this.roomId)?.emit("lastResponse", {});
    ROOMS.sendQuestion(this.roomId, { question: "" });
    const endPrompt = staticTexts.codingEndPrompt();
    await this.pushToConversation(endPrompt);
    return endPrompt;
  }

  async moveToNextQuestion(hasAnsweredCurrentQuestion) {
    // const languages = ROOMS.getProgrammingLanguages(this.roomId);
    const { question, example } = await this.getNextCodingQuestion(); // need to insert into db
    // const questionHasExample = example.length;
    // const result = await super.generateNextResponse(
    //   nextCodingQuestionPrompt(languages, question, hasAnsweredCurrentQuestion)
    // );

    const response = staticTexts.moveToNextCodingQuestion(
      hasAnsweredCurrentQuestion
    );

    await ROOMS.setLastCase(this.roomId, "");
    ROOMS.setResponse(this.roomId, { question: response });
    await ROOMS.pushToConversation(this.roomId, {
      role: "assistant",
      content: response,
    });
    return response;
  }

  async getAnswerScore(question, questionSkills = [], diff) {
    if (!diff) return 0;

    const languages = ROOMS.getProgrammingLanguages(this.roomId);
    const programmingSkills = ROOMS.getProgrammingSkills(this.roomId);
    const programmingCustomSkills = ROOMS.getProgrammingCustomSkills(
      this.roomId
    );
    const allSkills = programmingSkills?.skills
      ?.map((skill) => ({
        skill: skill.skill,
        id: skill.id,
        is_custom_skill: false,
      }))
      .concat(
        programmingCustomSkills.skills.map((skill) => ({
          skill: skill.skill,
          id: skill.id,
          is_custom_skill: true,
        }))
      );

    const filteredSkills =
      questionSkills.length > 0
        ? allSkills.filter((skillObj) =>
            questionSkills.includes(skillObj.skill)
          )
        : allSkills;

    if (!filteredSkills.some((skill) => skill.id === 1)) {
      filteredSkills.unshift({
        skill: "Overall",
        id: 1,
        is_custom_skill: false,
      });
    }

    let prompt = "";
    switch (ROOMS.getRoomState(this.roomId)) {
      case roomStates.coding_conversation:
        prompt = taskEvaluationPrompt(
          languages,
          question,
          diff,
          filteredSkills
        );
        break;
      case roomStates.dsa_coding_conversation:
        const plantUMLDiagram = ROOMS.getPlantUMLDiagram(this.roomId);
        const currentQuestionIndex = ROOMS.getCurrentQuestionIndex(this.roomId);
        const { expectedComplexity = "", constraints = "" } = ROOMS.getRecommendedQuestions(this.roomId)[currentQuestionIndex];
        prompt = taskEvaluationDSAPrompt(
          question,
          plantUMLDiagram,
          diff,
          expectedComplexity,
          constraints,
          currentQuestionIndex + 1
        );
        break;
    }

    const GENAI = ModelFactory.getInstance("Bedrock");
    const isDemoRoom = ROOMS.isDemoRoom(this.roomId);

    let retries = 0;
    let retriesLimit = 1;
    let result;

    while (retries <= retriesLimit) {
      result = await super.generateNextResponse(
        prompt,
        isDemoRoom ? null : GENAI
      );

      const overallScore = result.ratings?.find(
        (score) => score.id === 1
      )?.rating_value;

      if (typeof overallScore === "number" && !isNaN(overallScore)) {
        break;
      } else {
        retries++;
        if (retries > retriesLimit) {
          console.warn(
            `Failed to generate valid overall score after ${retries} attempts`
          );
          break;
        }
      }
    }

    return result;
  }

  async onIncorrectAnswer() {
    const currentCodingQuestionObj = ROOMS.getLastCodingQuestionAnswer(
      this.roomId
    );
    let results;

    const companyId = ROOMS.getCompanyId(this.roomId);
    if (companyId) {
      results = await callDataLayer(
        "insertCodingConversation",
        companyId,
        "POST",
        {
          roomId: this.roomId,
          question: currentCodingQuestionObj.question,
        }
      );
    } else {
      results = await insertCodingConversation(
        this.roomId,
        currentCodingQuestionObj.question
      );
    }

    await ROOMS.pushCodingQuestion(
      this.roomId,
      currentCodingQuestionObj,
      companyId ? results.id : results.insertId
    );
  }

  async giveHintOnSubmission(hint) {
    const companyId = ROOMS.getCompanyId(this.roomId);
    const { answer } = ROOMS.getResponse(this.roomId);
    const room_state = ROOMS.getRoomState(this.roomId);
    const { id } = ROOMS.getLastCodingQuestionAnswer(this.roomId);

    callDataLayer("insertCodingHint", companyId, "POST", {
      roomId: this.roomId,
      coding_conversation_id: id,
      query: answer,
      hint: hint,
      case_triggered: null,
    }).catch((err) => console.log("codeRoomHandler generateNextResponse", err));

    ROOMS.setResponse(this.roomId, { question: hint });

    if (room_state === roomStates.coding_conversation || room_state === roomStates.dsa_coding_conversation) {
      await ROOMS.pushToConversation(this.roomId, {
        role: "assistant",
        content: hint,
      });
      await ROOMS.pushToHints(this.roomId, {
        query: answer,
        hint: hint,
        id: id,
      });
    }

    await this.onIncorrectAnswer();
  }

  async getSyntax(language, query) {
    const { id } = ROOMS.getLastCodingQuestionAnswer(this.roomId);
    const companyId = ROOMS.getCompanyId(this.roomId);

    const GENAI = ModelFactory.getInstance("VertexAI");
    const prompt = generateSyntaxPrompt(language, query);
    const result = await getJsonFromPromptWithRetries(
      GENAI,
      prompt,
      "getResultFromPrompt",
      5,
      this.roomId
    );

    if (!result?.response) {
      throw new Error("Invalid Response");
    }

    if (companyId) {
      callDataLayer("insertCodingHint", companyId, "POST", {
        roomId: this.roomId,
        coding_conversation_id: id,
        query: query,
        hint: result.response,
        case_triggered: "syntax",
      }).catch((err) => console.log("codeRoomHandler getSyntax", err));
    } else {
      insertCodingHint(this.roomId, id, query, result.response, "syntax");
    }

    return result.response;
  }
}

const tutorialInstructions = require("../utils/tutorialInstructions");

class TutorialHandler extends BaseRoomHandler {
  constructor(roomId, stepNumber = 1) {
    super(roomId);
    this.stepNumber = stepNumber;
  }

  initialize() {
    this.stepNumber = 1;
    return this.sendStep();
  }

  getStepPrompt() {
    return tutorialInstructions[this.stepNumber];
  }

  getQuestionData() {
    return {
      question: this.getStepPrompt().main,
    };
  }

  sendStep() {
    const prompt = this.getStepPrompt();

    if (!prompt) return;

    const questionData = {
      question: prompt.main,
    };

    ROOMS.setResponse(this.roomId, questionData);
    ROOMS.sendQuestion(this.roomId, questionData);
    this.streamAIAudio(prompt.main);
    this.startRepeatInterval();
    return questionData;
  }

  async moveToNextStep() {
    this.stepNumber++;
    await ROOMS.setTutorialStep(this.roomId, this.stepNumber);
    this.sendStep();
  }

  repeatCurrentStep() {
    const prompt = this.getStepPrompt();
    this.streamAIAudio(prompt.repeat);
  }

  startRepeatInterval() {
    ROOMS.clearTutorialInterval(this.roomId);

    const prompt = this.getStepPrompt();
    const intervalId = setTimeout(() => {
      this.repeatCurrentStep();
    }, prompt.time * 1000);

    ROOMS.setTutorialInterval(this.roomId, intervalId);
  }

  getStepNumber() {
    return this.stepNumber;
  }
}

module.exports = {
  BaseRoomHandler,
  IntroductionHandler,
  EnglishRoomHandler,
  CodeRoomHandler,
  TutorialHandler,
};
