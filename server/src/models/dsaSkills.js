class DSASkills {
  constructor() {
    this.dsaSkills = {};
    this.question1DSASkills = [];
    this.question2And3DSASkills = [];
  }

  setDSASkills(newDSASkills) {
    this.dsaSkills = newDSASkills;
    this._buildSkillArrays();
  }

  getDSASkills() {
    return this.dsaSkills;
  }

  getSkillId(skillName) {
    return this.dsaSkills[skillName];
  }

  _buildSkillArrays() {
    this.question1DSASkills = [
      { skill: "Overall", id: 1, is_custom_skill: false },
      { skill: "Data Structure Implementation", id: this.dsaSkills["Data Structure Implementation"], is_custom_skill: false },
      { skill: "Problem Solving Approach", id: this.dsaSkills["Problem Solving Approach"], is_custom_skill: false },
      { skill: "Edge Case Handling", id: this.dsaSkills["Edge Case Handling"], is_custom_skill: false },
      { skill: "Code Readability", id: this.dsaSkills["Code Readability"], is_custom_skill: false },
    ];

    this.question2And3DSASkills = [
      { skill: "Overall", id: 1, is_custom_skill: false },
      { skill: "Time Complexity Analysis", id: this.dsaSkills["Time Complexity Analysis"], is_custom_skill: false },
      { skill: "Space Complexity Analysis", id: this.dsaSkills["Space Complexity Analysis"], is_custom_skill: false },
      { skill: "Data Structure Implementation", id: this.dsaSkills["Data Structure Implementation"], is_custom_skill: false },
      { skill: "Algorithm Design", id: this.dsaSkills["Algorithm Design"], is_custom_skill: false },
      { skill: "Problem Solving Approach", id: this.dsaSkills["Problem Solving Approach"], is_custom_skill: false },
      { skill: "Code Optimization", id: this.dsaSkills["Code Optimization"], is_custom_skill: false },
      { skill: "Edge Case Handling", id: this.dsaSkills["Edge Case Handling"], is_custom_skill: false },
      { skill: "Code Readability", id: this.dsaSkills["Code Readability"], is_custom_skill: false },
    ];
  }

  getQuestion1DSASkills() {
    return this.question1DSASkills;
  }

  getQuestion2And3DSASkills() {
    return this.question2And3DSASkills;
  }
}

module.exports = DSASkills;
