const amqp = require("amqplib");

class Producer {
  channel;
  delete_repo_queue = process.env.DELETE_REPO_QUEUE;

  async createChannel() {
    const connection = await amqp.connect(process.env.AMQP_URL);
    this.channel = await connection.createChannel();

    await this.channel.assertExchange(
      process.env.DELAYED_EXCHANGE,
      "x-delayed-message",
      {
        durable: true,
        arguments: {
          "x-delayed-type": "direct",
        },
      }
    );
    await this.channel.assertQueue(this.delete_repo_queue, { durable: true });

    await this.channel.bindQueue(
      this.delete_repo_queue,
      process.env.DELAYED_EXCHANGE,
      this.delete_repo_queue
    );
  }

  async publishMessage(exchange, routingKey, message) {
    if (!this.channel) {
      await this.createChannel();
    }

    await this.channel.publish(
      exchange,
      routingKey,
      Buffer.from(JSON.stringify(message))
    );

    console.log(
      `Sent message: ${JSON.stringify(
        message
      )} to exchange: ${exchange} with routing key: ${routingKey}`
    );
  }

  async sendDelayedMessage(queueName, message, delay = 60 * 1000 * 60) {
    if (!this.channel) {
      await this.createChannel();
    }

    this.channel.publish(
      process.env.DELAYED_EXCHANGE,
      queueName,
      Buffer.from(JSON.stringify(message)),
      {
        headers: {
          "x-delay": delay,
        },
        persistent: true,
      }
    );

    console.log(
      `Sent delayed message to ${queueName} with delay of ${
        delay / (1000 * 60)
      } minutes: ${JSON.stringify(message)}`
    );
  }
}

module.exports = Producer;
