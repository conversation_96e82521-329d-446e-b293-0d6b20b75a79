const Response = require("./response");
const { roomStates, botId, modes } = require("../constants/roomConstants");
const ResponseQueue = require("./responseQueue");
const { getRepoPath } = require("../utils/directoryUtils");
const fs = require("fs");
const Deque = require("denque");

class Rooms {
  constructor(redisClient) {
    this.redisClient = redisClient;
    this.rooms = {};
  }

  async createRoom({
    roomId,
    mediaserverEndpoint,
    language,
    start_time,
    end_time,
    is_demo,
    demo_length,
    user_name,
    user_email,
    submission_id,
    programming_languages,
    programming_skills,
    programming_custom_skills,
    candidate_id,
    form_id,
    recommended_questions,
    current_question_index,
    redisExpirySeconds,
    botAudio,
    uid,
    companyId,
    mode,
    licence,
    cheatingTimestamps,
    botPaused,
    lastGitDiff,
    tabOutOfFocusTimestamps,
    lastCase,
    casesToExclude,
    coding_conversation,
    conversation,
    room_state,
    tutorial_step,
    is_tutorial = false,
    plantUMLDiagram = "",
  }) {
    const redisRoom = {
      roomid: roomId,
      tabOutOfFocusTimestamps: tabOutOfFocusTimestamps || [],
      conversation: conversation || [],
      coding_conversation: coding_conversation || {
        ques_ans: [], // [{question:"", answer:"", isCorrect, id, concepts:[], fileName:"", unimplementedFeature:""}]
        hints: [], // [{query: "", hint:"", id}]
      },
      recommended_questions: recommended_questions || [],
      recommended_questions_count: recommended_questions?.length || 0,
      current_question_index:
        current_question_index != null ? parseInt(current_question_index) : -1,
      submission_id: submission_id,
      room_state: room_state || roomStates.introduction,
      language: language,
      is_demo: is_demo,
      programming_languages: programming_languages,
      programming_skills: programming_skills,
      programming_custom_skills: programming_custom_skills,
      demo_length: demo_length || 0,
      start_time: start_time,
      end_time: end_time,
      user_name: user_name,
      user_email: user_email,
      mediaserverEndpoint: mediaserverEndpoint,
      uid: uid,
      shouldSendAudioToUDPServer: true,
      ended: false,
      botPaused: botPaused || false,
      lastCase: lastCase || "",
      lastGitDiff: lastGitDiff || "",
      casesToExclude: casesToExclude || [],
      companyId: companyId || null,
      mode: mode || modes.voice,
      licence: licence || "v1",
      cheatingTimestamps: [],
      cheatingActive: false,
      tutorial_step: tutorial_step || 1,
      cheatingActive: false,
      candidate_id: candidate_id,
      form_id: form_id,
      is_tutorial: is_tutorial,
      plantUMLDiagram: plantUMLDiagram || "",
    };

    const room = {
      ...redisRoom,
      responseQueue: new ResponseQueue(),
      response: new Response(),
      botAudio: botAudio,
      AskForHelpTimeout: null,
      promptTimestamps: new Deque(),
      rateLimitActive: false,
      evaluating: false,
    };

    this.rooms[roomId] = room;

    if (!is_tutorial) {
      await this.redisClient.setRoomData(roomId, redisRoom, redisExpirySeconds);
    }

    console.log("created room with roomId:", roomId);
  }

  async _loadRoomFromRedis(roomId) {
    const redisRoom = await this.redisClient.getRoomData(roomId);
    if (!redisRoom) return null;

    const room = {
      ...redisRoom,
      responseQueue: new ResponseQueue(),
      response: new Response(),
      botAudio: null,
      AskForHelpTimeout: null,
      promptTimestamps: new Deque(),
      rateLimitActive: false,
      current_question_index:
        redisRoom.current_question_index != null
          ? parseInt(redisRoom.current_question_index)
          : -1,
    };

    this.rooms[roomId] = room;
    return room;
  }

  isRateLimitActive(roomId) {
    const room = this.getRoom(roomId);
    return room ? room.rateLimitActive : false;
  }

  setRateLimitActive(roomId, isActive) {
    const room = this.getRoom(roomId);
    if (room) {
      room.rateLimitActive = isActive;
    }
  }

  async hasRoom(roomId) {
    const doesLocalRoomCacheExist = this.rooms[roomId] != null;
    if (doesLocalRoomCacheExist) return true;

    const doesRedisRoomCacheExist = await this.redisClient.exists(roomId);

    if (doesRedisRoomCacheExist) {
      await this._loadRoomFromRedis(roomId);
      return true;
    }

    return false;
  }

  getPromptCount(roomId) {
    const room = this.getRoom(roomId);
    if (!room) return 0;

    const currentTime = Date.now();
    const promptTimestamps = room.promptTimestamps;

    while (
      promptTimestamps.length > 0 &&
      promptTimestamps.peekFront() <= currentTime - 60000
    ) {
      promptTimestamps.shift();
    }

    return promptTimestamps.length;
  }

  addPromptTimestamp(roomId) {
    const room = this.getRoom(roomId);
    if (!room) return;

    const currentTime = Date.now();
    const promptTimestamps = room.promptTimestamps;

    promptTimestamps.push(currentTime);
  }

  setCheatingState(roomId, isCheatingActive, timestamp) {
    const room = this.getRoom(roomId);
    if (!room) return;

    room.cheatingActive = isCheatingActive;

    room.cheatingTimestamps.push({
      type: isCheatingActive ? "start" : "end",
      timestamp,
    });
  }

  getCheatingState(roomId) {
    const room = this.getRoom(roomId);
    if (!room) return null;

    return {
      cheatingActive: room.cheatingActive,
      cheatingTimestamps: room.cheatingTimestamps,
    };
  }

  getRoom(roomId) {
    const room = this.rooms[roomId];
    if (!room) return null;
    return room;
  }

  getUID(roomId) {
    return this.rooms[roomId]?.uid;
  }

  getMode(roomId) {
    return this.rooms[roomId]?.mode;
  }

  isBotPaused(roomId) {
    return this.rooms[roomId]?.botPaused;
  }

  async setMode(roomId, mode) {
    this.rooms[roomId].mode = mode;
    await this.redisClient.updateRoomField(roomId, "mode", mode);
  }

  async pushNewAudioToBot(roomId, audioData) {
    await this.rooms[roomId]?.botAudio.onNewAudio(audioData);
  }

  getProgrammingLanguages(roomId) {
    return this.rooms[roomId]?.programming_languages;
  }

  getProgrammingSkills(roomId) {
    return this.rooms[roomId]?.programming_skills;
  }

  getProgrammingCustomSkills(roomId) {
    return this.rooms[roomId]?.programming_custom_skills;
  }

  getChatHistory(roomId) {
    return this.rooms[roomId]?.conversation
      .filter((convo) => convo.role != "system")
      .map((convo) => {
        return {
          peerId: convo.peerId,
          peerName: convo.role === "user" ? "You" : "Eval",
          message: convo.content
            .replace(/^<Interviewer>:\s*/, "")
            .replace(/^<Interviewee>:\s*/, ""),
        };
      });
  }

  clearAskForHelpTimeout(roomId) {
    if (this.rooms[roomId]?.AskForHelpTimeout) {
      console.log("clearing ask for help timeout", roomId);

      const timeoutId = this.rooms[roomId].AskForHelpTimeout;
      clearTimeout(timeoutId);
    }
  }

  setAskForHelpTimeout(roomId, timeout) {
    this.clearAskForHelpTimeout(roomId);
    this.rooms[roomId].AskForHelpTimeout = timeout;
  }

  setTutorialInterval(roomId, interval) {
    this.rooms[roomId].setTutorialInterval = interval;
  }

  clearTutorialInterval(roomId) {
    if (this.rooms[roomId]?.setTutorialInterval)
      clearTimeout(this.rooms[roomId].setTutorialInterval);
  }

  async setLastGitDiff(roomId, diff) {
    this.rooms[roomId].lastGitDiff = diff;
    await this.redisClient.updateRoomField(roomId, "lastGitDiff", diff);
  }

  getLastGitDiff(roomId) {
    return this.rooms[roomId]?.lastGitDiff;
  }

  getExcludedCases(roomId) {
    return this.rooms[roomId]?.casesToExclude;
  }

  async addExcludedCase(roomId, caseName) {
    this.rooms[roomId].casesToExclude = [
      ...this.rooms[roomId].casesToExclude,
      caseName,
    ];

    await this.redisClient.updateRoomField(
      roomId,
      "casesToExclude",
      this.rooms[roomId].casesToExclude
    );
  }

  isBotPaused(roomId) {
    return this.rooms[roomId].botPaused;
  }

  async playBotAudio(roomId) {
    await this.rooms[roomId]?.botAudio.play();
  }

  async clearRoom(roomId) {
    console.log("clearing room local cache with roomId:", roomId);
    if (!(await this.hasRoom(roomId))) return;

    const uid = this.rooms[roomId].uid;
    const repoPath = getRepoPath(uid);

    if (fs.existsSync(repoPath)) {
      fs.rm(repoPath, { recursive: true, force: true }, (err) => {
        if (err) console.log("Error deleting dir", roomId, err);
        else console.log("Deleted dir", roomId);
      });
    }

    delete this.rooms[roomId];
  }

  async deleteRoom(roomId) {
    await this.clearRoom(roomId);
    await this.redisClient.deleteRoom(roomId);
  }

  async deleteTutorialRoom(roomId) {
    console.log("deleting tutorial room with roomId:", roomId);
    if (!(await this.hasRoom(roomId))) return;
    delete this.rooms[roomId];
  }

  setResponse(roomId, args) {
    this.rooms[roomId].response.setResponse(args);
  }

  clearResponse(roomId) {
    this.rooms[roomId]?.response.clearResponse();
  }

  setShouldSendAudioToUDPServer(roomId, value) {
    this.rooms[roomId].shouldSendAudioToUDPServer = value;
  }

  getShouldSendAudioToUDPServer(roomId) {
    return this.rooms[roomId]?.shouldSendAudioToUDPServer;
  }

  async pushToConversation(roomId, obj) {
    const formatMap = {
      OpenAI: {
        user: `Interviewee's response: ${obj.content}`,
        assistant: obj.content,
        system: obj.content,
      },
      VertexAI: {
        user: `<Interviewee>: ${obj.content}`,
        assistant: `<Interviewer>: ${obj.content}`,
        system: `--${obj.content}--`,
      },
      AzureOpenAI: {
        user: `<Interviewee>: ${obj.content}`,
        assistant: `<Interviewer>: ${obj.content}`,
        system: `--${obj.content}--`,
      },
    };

    const format = formatMap[process.env.GENAI_TO_USE][obj.role];
    if (format) {
      this.rooms[roomId].conversation.push({
        role: obj.role,
        peerId: obj.role === "user" ? this.rooms[roomId].uid : botId,
        content: format,
      });

      await this.redisClient.updateRoomField(
        roomId,
        "conversation",
        this.rooms[roomId].conversation
      );
    }
  }

  setConversation(roomId, conversation) {
    this.rooms[roomId].conversation = conversation;
  }

  getConversation(roomId) {
    return this.rooms[roomId].conversation;
  }

  getCodingConversation(roomId) {
    console.log("ques ans", this.rooms[roomId].coding_conversation.ques_ans);
    return this.rooms[roomId].coding_conversation.ques_ans;
  }

  getLastCodingQuestionAnswer(roomId) {
    const codingConversation = this.rooms[roomId].coding_conversation.ques_ans;
    return codingConversation[codingConversation.length - 1];
  }

  getQuestionConcepts(roomId) {
    const questionObj = this.getLastCodingQuestionAnswer(roomId);
    return questionObj.concepts;
  }

  popQuestionConcept(roomId) {
    const index = this.rooms[roomId].coding_conversation.ques_ans.length - 1;
    this.rooms[roomId].coding_conversation.ques_ans[index].concepts.shift();
  }

  async pushCodingQuestion(roomId, questionObj, id) {
    this.rooms[roomId].coding_conversation.ques_ans.push({
      exampleGiven: false,
      ...questionObj,
      isCorrect: false,
      id,
      answer: "",
    });

    await this.redisClient.updateRoomField(
      roomId,
      "coding_conversation",
      this.rooms[roomId].coding_conversation
    );
  }

  async markCodingExampleGiven(roomId) {
    const index = this.rooms[roomId].coding_conversation.ques_ans.length - 1;
    this.rooms[roomId].coding_conversation.ques_ans[index].exampleGiven = true;

    await this.redisClient.updateRoomField(
      roomId,
      "coding_conversation",
      this.rooms[roomId].coding_conversation
    );
  }

  async pushCodingHint(roomId, query, hint, id) {
    this.rooms[roomId].coding_conversation.hints.push({
      query,
      hint,
      id,
    });

    await this.redisClient.updateRoomField(
      roomId,
      "coding_conversation",
      this.rooms[roomId].coding_conversation
    );
  }

  async setCodingAnswer(roomId, questionId, answer, isCorrect) {
    const index = this.rooms[roomId].coding_conversation.ques_ans.findIndex(
      (qa) => qa.id === questionId
    );
    this.rooms[roomId].coding_conversation.ques_ans[index].answer = answer;
    this.rooms[roomId].coding_conversation.ques_ans[index].isCorrect =
      isCorrect;

    await this.redisClient.updateRoomField(
      roomId,
      "coding_conversation",
      this.rooms[roomId].coding_conversation
    );
  }

  getRecommendedQuestions(roomId) {
    return this.rooms[roomId].recommended_questions;
  }

  getRecommendedQuestionsCount(roomId) {
    return this.rooms[roomId].recommended_questions_count;
  }

  getCurrentQuestionIndex(roomId) {
    return this.rooms[roomId].current_question_index;
  }

  async moveToNextQuestion(roomId) {
    const recommended_questions = this.rooms[roomId].recommended_questions;

    this.rooms[roomId].current_question_index++;

    await this.redisClient.updateRoomField(
      roomId,
      "current_question_index",
      this.rooms[roomId].current_question_index
    );

    return recommended_questions[this.rooms[roomId].current_question_index];
  }

  getResponse(roomId) {
    return this.rooms[roomId].response.getResponse();
  }

  async pushToHints(roomId, obj) {
    this.rooms[roomId].coding_conversation.hints.push(obj);
    await this.redisClient.updateRoomField(
      roomId,
      "coding_conversation",
      this.rooms[roomId].coding_conversation
    );
  }

  getHints(roomId) {
    return this.rooms[roomId].coding_conversation.hints;
  }

  getRoomState(roomId) {
    return this.rooms[roomId].room_state;
  }

  async changeRoomState(roomId, roomState) {
    this.rooms[roomId].room_state = roomState;
    await this.redisClient.updateRoomField(roomId, "room_state", roomState);
    return roomState;
  }

  getLanguage(roomId) {
    return this.rooms[roomId].language;
  }

  async closeBot(roomId) {
    const { botAudio, responseQueue } = this.rooms[roomId];
    this.rooms[roomId].ended = true;
    responseQueue.cleanup(true);
    botAudio?.close();
  }

  sendChatMessage(roomId, message) {
    const { botAudio } = this.rooms[roomId];
    botAudio.sendChatMessage(message);
  }

  sendQuestion(roomId, data) {
    const { botAudio } = this.rooms[roomId];
    botAudio.sendQuestion(data);
  }

  changeBotState(roomId, state) {
    this.rooms[roomId]?.botAudio?.changeBotState(state);
  }

  getMessenger(roomId) {
    return this.rooms[roomId].botAudio.messenger;
  }

  async pushResponseToResponseQueue(
    roomId,
    response,
    responseHandler,
    ...args
  ) {
    if (!this.rooms[roomId]) return;
    if (this.rooms[roomId].ended) return;
    this.rooms[roomId].responseQueue.addResponse(
      response,
      responseHandler,
      ...args
    );
  }

  async pushPromiseToResponseQueue(roomId, promise) {
    if (!this.rooms[roomId] || this.rooms[roomId].ended) return;
    return this.rooms[roomId].responseQueue.pushPromise(promise);
  }

  cleanupResponseQueue(roomId) {
    this.rooms[roomId].responseQueue.cleanup();
  }

  cancelPreviousResponsesInQueue(roomId) {
    this.rooms[roomId].responseQueue.cancel();
  }

  async setBotPaused(roomId, value) {
    console.log("setting bot paused", value, "for roomId", roomId);
    this.rooms[roomId].botPaused = value;

    await this.redisClient.updateRoomField(roomId, "botPaused", value);
  }

  async pushTabOutOfFocusTimestamp(roomId, timestamp) {
    this.rooms[roomId].tabOutOfFocusTimestamps.push(timestamp);

    await this.redisClient.updateRoomField(
      roomId,
      "tabOutOfFocusTimestamps",
      this.rooms[roomId].tabOutOfFocusTimestamps
    );
  }

  getTabOutOfFocusTimestamps(roomId) {
    return this.rooms[roomId].tabOutOfFocusTimestamps;
  }

  async setLastCase(roomId, lastCase) {
    this.rooms[roomId].lastCase = lastCase;
    await this.redisClient.updateRoomField(roomId, "lastCase", lastCase);
  }

  getLastCase(roomId) {
    return this.rooms[roomId].lastCase;
  }

  getCompanyId(roomId) {
    return this.rooms[roomId].companyId;
  }

  getLicence(roomId) {
    return this.rooms[roomId].licence;
  }

  setLicence(roomId, licence) {
    this.rooms[roomId].licence = licence;
  }

  getFormId(roomId) {
    return this.rooms[roomId].form_id;
  }

  async setFormId(roomId, formId) {
    await this.redisClient.updateRoomField(roomId, "form_id", formId);
    this.rooms[roomId].form_id = formId;
  }

  getCandidateId(roomId) {
    return this.rooms[roomId].candidate_id;
  }

  async setCandidateId(roomId, candidateId) {
    await this.redisClient.updateRoomField(roomId, "candidate_id", candidateId);
    this.rooms[roomId].candidate_id = candidateId;
  }

  getUserEmail(roomId) {
    return this.rooms[roomId].user_email;
  }

  async setUserEmail(roomId, email) {
    await this.redisClient.updateRoomField(roomId, "user_email", email);
    this.rooms[roomId].user_email = email;
  }

  getUserName(roomId) {
    return this.rooms[roomId].user_name;
  }

  async setUserName(roomId, name) {
    await this.redisClient.updateRoomField(roomId, "user_name", name);
    this.rooms[roomId].user_name = name;
  }

  async setTutorialStep(roomId, step) {
    this.rooms[roomId].tutorial_step = step;
  }

  getTutorialStep(roomId) {
    return this.rooms[roomId].tutorial_step;
  }

  setEvaluating(roomId, value) {
    this.rooms[roomId].evaluating = value;
  }

  getEvaluating(roomId) {
    return this.rooms[roomId].evaluating;
  }

  isDemoRoom(roomId) {
    return this.rooms[roomId].is_demo;
  }

  getPlantUMLDiagram(roomId) {
    return this.rooms[roomId].plantUMLDiagram;
  }
}

module.exports = Rooms;
