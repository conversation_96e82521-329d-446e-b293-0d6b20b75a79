const path = require("path");
const fs = require("fs");
const io = require("socket.io-client");
const mediasoupClient = require("mediasoup-client");
const { createMediasoupWorker, streamAIAudio } = require("../utils/index.js");
const { getBotIdToken } = require("../firebase");
const { botId, modes } = require("../constants/roomConstants.js");
const ffmpeg = require("fluent-ffmpeg");
const { ROOMS } = require("../cache/index.js");
const { caCert } = require("../configs/index.js");
const { Readable } = require("stream");
const MediasoupMessenger = require("./mediasoupMessenger.js");

const { startAskForHelpTimeout } = require("../utils/roomHandlerUtils");

const responseHandler = require("../eventHandlers/responseHandler.js");
const syntaxRequestHandler = require("../eventHandlers/syntaxRequestHandler.js");
const timeWarningHandler = require("../eventHandlers/timeWarningHandler.js");
const skipQuestionHandler = require("../eventHandlers/skipQuestionHandler.js");
const publishHandler = require("../eventHandlers/publishHandler.js");
const endInterviewHandler = require("../eventHandlers/endInterviewHandler.js");
const endTutorialHandler = require("../eventHandlers/endTutorialHandler.js");
const tutorialAction = require("../eventHandlers/tutorialAction.js");
const disconnectHandler = require("../eventHandlers/disconnectHandler.js");
const cheatAlert = require("../eventHandlers/cheatAlert.js");

class BotAudio {
  constructor(roomId, uid, mediaserverEndpoint) {
    this.roomId = roomId;
    this.uid = uid;
    this.mediaserverEndpoint = mediaserverEndpoint;
    this.socket = null;
    this.worker = null;
    this.audioFilePath = path.resolve(
      __dirname,
      `../../room_audios/${this.roomId}.webm`
    );
    this.transportSend = null;
    this.audioTrack = null;
    this.audioProducer = null;
    this.messenger = new MediasoupMessenger(roomId);
    this.dataConsumers = null;
  }

  getSocketEventResponse(socket, event, data, callback = () => {}) {
    return new Promise((resolve, reject) => {
      socket.emit(event, data, (response) => {
        callback(response);
        resolve(response);
      });
    });
  }

  init() {
    return new Promise(async (resolve, reject) => {
      try {
        const botIdToken = await getBotIdToken();

        this.socket = io(this.mediaserverEndpoint, {
          auth: (cb) => {
            cb({ token: "Bearer " + botIdToken, isBot: true });
          },
          // autoConnect: false,
          transports: ["websocket", "polling"],
          ca: caCert,
        });

        this.socket.on("connect", async () => {
          console.log(`Bot connected to mediasoup.`);
          this.worker = await createMediasoupWorker();

          const { peerId } = await this.getSocketEventResponse(
            this.socket,
            "joinRoom",
            {
              roomId: this.roomId,
              peerId: botId,
            }
          );
          this.peerId = peerId;
          const routerRtpCapabilities = await this.getSocketEventResponse(
            this.socket,
            "getRouterRtpCapabilities",
            {
              roomId: this.roomId,
            }
          );

          const device = new mediasoupClient.Device({
            handlerFactory: this.worker.createHandlerFactory(),
          });

          await device.load({ routerRtpCapabilities });

          const {
            id,
            iceParameters,
            iceCandidates,
            dtlsParameters,
            sctpParameters,
          } = await this.getSocketEventResponse(
            this.socket,
            "createTransport",
            {
              roomId: this.roomId,
              peerId,
              type: "send",
            }
          );

          this.transportSend = device.createSendTransport({
            id,
            iceParameters,
            iceCandidates,
            dtlsParameters,
            sctpParameters,
          });

          this.transportSend.on(
            "connect",
            async ({ dtlsParameters }, callback, errback) => {
              console.log("connecting transport send");
              await this.getSocketEventResponse(
                this.socket,
                "connectTransport",
                {
                  roomId: this.roomId,
                  peerId,
                  dtlsParameters,
                  type: "send",
                }
              );
              callback();
            }
          );

          this.transportSend.on(
            "produce",
            async ({ kind, rtpParameters, appData }, callback, errback) => {
              console.log("producing");
              await this.getSocketEventResponse(
                this.socket,
                "produce",
                { roomId: this.roomId, peerId, kind, rtpParameters, appData },
                ({ id }) => {
                  callback({ id });
                }
              );
            }
          );

          this.transportSend.on(
            "producedata",
            async (
              { sctpStreamParameters, label, protocol, appData },
              callback,
              errback
            ) => {
              const { id } = await this.getSocketEventResponse(
                this.socket,
                "produceData",
                {
                  roomId: this.roomId,
                  peerId,
                  sctpStreamParameters,
                  label,
                  protocol,
                  appData,
                }
              );
              callback({ id });
            }
          );

          const dataProducer = await this.transportSend.produceData();

          await this.messenger.setProducer(dataProducer);

          this.messenger.onNewMessage = async (message, callback) => {
            switch (message.event) {
              case "toggleCheating": {
                const { isCheatingActive, timestamp } = message.data;

                console.log(`Cheating Status Toggled:`, {
                  roomId: this.roomId,
                  isCheatingActive,
                  timestamp,
                });

                ROOMS.setCheatingState(
                  this.roomId,
                  isCheatingActive,
                  timestamp
                );

                callback({
                  success: true,
                  message: "Cheating status toggled successfully.",
                });
                break;
              }

              case "fetchBotStatus":
                console.log(ROOMS.getCheatingState(this.roomId));
                this.messenger.emit("currentButtonsState", {
                  isCheatingActive: ROOMS.getCheatingState(this.roomId)
                    .cheatingActive,
                  paused: ROOMS.isBotPaused(this.roomId),
                });

                break;

              case "response": {
                const { peerId, response, filesData } = message.data;

                if (peerId === ROOMS.getUID(this.roomId))
                  ROOMS.getEvaluating(this.roomId)
                    ? ROOMS.pushPromiseToResponseQueue(
                        this.roomId,
                        responseHandler(message.data, this.roomId)
                      )
                    : ROOMS.pushResponseToResponseQueue(
                        this.roomId,
                        { response, filesData },
                        responseHandler,
                        this.roomId
                      );

                callback({});
                break;
              }

              case "getChatHistory": {
                callback({ chatHistory: ROOMS.getChatHistory(this.roomId) });
                break;
              }

              case "getSyntax": {
                const { language, query } = message.data;
                const syntax = await syntaxRequestHandler(
                  this.roomId,
                  language,
                  query
                );
                if (!syntax) {
                  callback({ success: false });
                  break;
                }
                callback({ success: true, syntax });
                break;
              }
              case "timeWarning": {
                ROOMS.pushPromiseToResponseQueue(
                  this.roomId,
                  timeWarningHandler(this.roomId)
                );
                break;
              }

              case "publishAnswer": {
                ROOMS.pushPromiseToResponseQueue(
                  this.roomId,
                  publishHandler(this.roomId, message.data.changes, callback)
                );
                break;
              }

              case "skipQuestion": {
                ROOMS.pushPromiseToResponseQueue(
                  this.roomId,
                  skipQuestionHandler(
                    this.roomId,
                    message.data.changes,
                    callback
                  )
                );
                break;
              }

              case "tutorialAction": {
                const { currentStep } = message.data;
                ROOMS.pushPromiseToResponseQueue(
                  this.roomId,
                  tutorialAction(this.roomId, currentStep)
                );
                break;
              }

              case "interviewTimeUp": {
                ROOMS.pushPromiseToResponseQueue(
                  this.roomId,
                  endInterviewHandler(
                    this.roomId,
                    message.data?.changes,
                    false,
                    callback
                  )
                );
                break;
              }

              case "tutorialTimeUp": {
                ROOMS.pushPromiseToResponseQueue(
                  this.roomId,
                  endTutorialHandler(this.roomId, callback)
                );
                break;
              }

              case "cheatAlert": {
                ROOMS.pushPromiseToResponseQueue(
                  this.roomId,
                  cheatAlert(this.roomId)
                );
                break;
              }

              default:
                console.log("Unhandled data consumer message:", message);
            }
          };

          this.messenger.emit("currentButtonsState", {
            isCheatingActive: false,
            paused: false,
          });

          const {
            id: transportRecvId,
            iceParameters: transportRecvIceParameters,
            iceCandidates: transportRecvIceCandidates,
            dtlsParameters: transportRecvDtlsParameters,
            sctpParameters: transportRecvSctpParameters,
          } = await this.getSocketEventResponse(
            this.socket,
            "createTransport",
            {
              roomId: this.roomId,
              peerId,
              type: "recv",
            }
          );

          this.transportRecv = device.createRecvTransport({
            id: transportRecvId,
            iceParameters: transportRecvIceParameters,
            iceCandidates: transportRecvIceCandidates,
            dtlsParameters: transportRecvDtlsParameters,
            sctpParameters: transportRecvSctpParameters,
          });

          await new Promise((resolve, reject) => {
            this.transportRecv.on(
              "connect",
              async ({ dtlsParameters }, callback, errback) => {
                await this.getSocketEventResponse(
                  this.socket,
                  "connectTransport",
                  {
                    roomId: this.roomId,
                    peerId,
                    dtlsParameters,
                    type: "recv",
                  },
                  callback
                );
                console.log(
                  "connected transport recv",
                  this.transportRecv.connectionState
                );
                resolve();
              }
            );
          });

          resolve();
        });

        this.socket.on("connect_error", (error) => {
          console.log("Error while connecting bot to mediasoup:", error);
          // reject(error);
        });

        this.socket.on("ping", () => {
          this.socket.emit("pong");
        });

        this.socket.on("endInterview", () => {
          ROOMS.pushPromiseToResponseQueue(
            this.roomId,
            endInterviewHandler(this.roomId, null, true)
          );
        });

        this.socket.on("endTutorial", () => {
          ROOMS.pushPromiseToResponseQueue(
            this.roomId,
            endTutorialHandler(this.roomId)
          );
        });

        this.socket.on("togglePause", async ({ paused }) => {
          console.log("toggling pause:", this.roomId, paused);
          await ROOMS.setBotPaused(this.roomId, paused);
          ROOMS.cleanupResponseQueue(this.roomId);

          if (paused) {
            ROOMS.clearAskForHelpTimeout(this.roomId);
            if (this.audioProducer) (await this.audioProducer)?.close();
          } else {
            startAskForHelpTimeout(this.roomId);
          }
        });

        this.socket.on("toggleMode", async ({ mode }) => {
          console.log("switching to mode:", this.roomId, mode);
          console.log("valid modes:", this.roomId, Object.values(modes));
          if (Object.values(modes).includes(mode)) {
            console.log("setting mode", this.roomId, mode);
            await ROOMS.setMode(this.roomId, mode);
            if (this.audioProducer) (await this.audioProducer)?.close();
          }
        });

        this.socket.on("intervieweeSpeaking", async () => {
          if (this.audioProducer) (await this.audioProducer)?.close();
          ROOMS.cancelPreviousResponsesInQueue(this.roomId);
        });

        this.socket.on(
          "newDataConsumer",
          async ({
            id,
            dataProducerId,
            sctpStreamParameters,
            peerId,
            label,
            protocol,
            appData,
          }) => {
            if (!this.transportRecv) {
              console.log("Transport recv not available:", this.transportRecv);
              return;
            }

            if (!this.dataConsumers) this.dataConsumers = {};
            this.dataConsumers[peerId] = await this.transportRecv.consumeData({
              id,
              sctpStreamParameters,
              dataProducerId,
              label,
              protocol,
              appData,
            });

            this.dataConsumers[peerId].on(
              "message",
              this.messenger.handleIncomingMessage
            );

            this.dataConsumers[peerId].on("open", () => {
              console.log("Data consumer is open", peerId);
            });

            this.dataConsumers[peerId].on("close", () => {
              console.log("Data consumer is closed");
            });

            this.dataConsumers[peerId].on("error", (error) => {
              console.error("Data consumer error:", error);
            });
          }
        );

        this.socket.on("peerDisconnected", async ({ peerId }) => {
          try {
            console.log("Peer disconnected:", peerId);
            this.dataConsumers[peerId]?.close();
            delete this.dataConsumers[peerId];

            if (
              this.roomId.toLowerCase().includes("_tutorial") ||
              this.uid === peerId
            ) {
              await this.close();
              disconnectHandler(this.roomId);
            } else {
              console.log("Peer disconnected:", peerId);
            }
          } catch (e) {
            console.log("Error in peerDisconnected", e);
          }
        });

        this.socket.on("disconnect", () => {
          console.log("Bot disconnected from mediasoup.");
        });
      } catch (error) {
        console.error("Error in makeConnectionWithMediaSoup", error);
        // reject(error);
      }
    });
  }

  sendChatMessage(message) {
    this.messenger.emit("response", {
      peerId: botId,
      peerName: "Eval",
      response: message,
    });
  }

  sendQuestion(data) {
    this.messenger.emit("newQuestion", data);
  }

  changeBotState(state) {
    // LISTENING, THINKING, IDLE
    this.messenger.emit("botStateChange", { state });
  }

  async getMediaTrack() {
    const stream = await this.worker.getUserMedia({
      audio: {
        source: "file",
        file: this.audioFilePath,
        remote: true,
      },
      video: false,
    });

    return stream.getTracks()[0];
  }

  async onNewAudio(audioData) {
    try {
      return new Promise((resolve, reject) => {
        const audioBuffer = new Buffer.from(audioData);
        const delay = process.env.BOT_AUDIO_DELAY || 200;
        const audioStream = new Readable();
        audioStream.push(audioBuffer);
        audioStream.push(null);

        const audioDir = path.dirname(this.audioFilePath);

        if (!fs.existsSync(audioDir)) {
          fs.mkdirSync(audioDir, { recursive: true });
        }

        const baseFileName = path.basename(this.audioFilePath, ".webm");
        const timestamp = Date.now();
        const tempFilePath = path.join(
          audioDir,
          `${baseFileName}.${timestamp}.tmp.webm`
        );

        let command = ffmpeg()
          .input(audioStream)
          .inputFormat("webm")
          .audioFilters(`adelay=${delay}|${delay}`)
          .output(tempFilePath)
          .format("webm");

        command.on("error", (err) => {
          console.error("Error while processing audio:", err);
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
          }
          reject(err);
        });

        command.on("end", async () => {
          console.log("Processing finished!");
          try {
            if (fs.existsSync(this.audioFilePath)) {
              await fs.promises.unlink(this.audioFilePath);
            }
            await fs.promises.rename(tempFilePath, this.audioFilePath);
            console.log("Processing finished and file replaced!");
            resolve();
          } catch (err) {
            console.error("Error while replacing file:", err);
            reject(err);
          }
        });

        command.run();
      });
    } catch (error) {
      console.error("Error during ffmpeg processing:", error);
      throw error;
    }
  }

  async play() {
    //get new audio track and replace it
    this.audioTrack = await this.getMediaTrack();

    if (this.audioProducer) (await this.audioProducer)?.close();

    // this.audioProducer.replaceTrack({ track: audioTrack });
    this.audioProducer = await this.transportSend.produce({
      track: this.audioTrack,
      appData: { source: "mic" },
    });
  }

  async close() {
    try {
      console.log("closing bot audio", this.roomId);
      // this.transportSend?.close(); might not be needed

      this.socket?.disconnect();
      this.worker?.close();
      if (fs.existsSync(this.audioFilePath)) {
        fs.unlinkSync(this.audioFilePath);
        fs.rm(this.audioFilePath, { force: true }, (err) => {
          if (err) console.log("Error deleting dir", this.audioFilePath, err);
          else console.log("Deleted file", this.audioFilePath);
        });
      }
    } catch (e) {
      console.log("error in closing bot audio", e);
    }
  }
}

module.exports = { BotAudio };
