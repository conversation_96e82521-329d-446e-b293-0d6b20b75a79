function TCP(roomId, start_end) {
  this.type = "tcp";
  this.roomId = roomId;
  this.start_end = start_end;
}

function UDP(roomId, audioData, audioId, audioFormat, deviceConfig) {
  this.type = "udp";
  this.roomId = roomId;
  this.audioData = audioData;
  this.audioId = audioId;
  this.audioFormat = audioFormat;
  this.deviceConfig = deviceConfig;
}

class Packet {
  static create(type, roomId, data, audioId, start_end, audioFormat, deviceConfig) {
    switch (type) {
      case "tcp":
        return new TCP(roomId, start_end);
      case "udp":
        return new UDP(roomId, data, audioId, audioFormat, deviceConfig);
      default:
        return null;
    }
  }
}

module.exports = Packet;
