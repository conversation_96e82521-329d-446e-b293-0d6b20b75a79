class Mutex {
  constructor(redisClient) {
    this.redisClient = redisClient;
  }

  async lock(identifier) {
    let lockAcquired = false;
    try {
      if (await this.redisClient.getLock(identifier)) {
        const error = new Error("Lock already acquired");
        error.code = "err_lock_already_acquired";
        throw error;
      } else {
        await this.redisClient.setLock(identifier);
        lockAcquired = true;
        const unlock = async () => {
          try {
            await this.redisClient.deleteLock(identifier);
          } catch (error) {
            console.error(`Error releasing lock for ${identifier}:`, error);
          }
        };
        return unlock;
      }
    } catch (error) {
      if (lockAcquired) {
        try {
          await this.redisClient.deleteLock(identifier);
        } catch (releaseError) {
          console.error(
            `Error releasing lock for ${identifier} after error:`,
            releaseError
          );
        }
      }
      throw error;
    }
  }
}

module.exports = Mutex;
