const { BaseRoomHandler } = require("../models/roomHandler");
const { ROOMS } = require("../cache/index.js");
const { startAskForHelpTimeout } = require("../utils/roomHandlerUtils.js");

const cheatAlert = async (roomId) => {
  try {
    if (!(await ROOMS.hasRoom(roomId))) {
      console.log("Room not found while sending cheat alert", roomId);
      return;
    }

    ROOMS.clearAskForHelpTimeout(roomId);
    const baseRoomHandler = new BaseRoomHandler(roomId);
    await baseRoomHandler.sendResponse(
      "We have detected some potentially malicious activity, which could result in the cancellation of your candidacy. We strongly advise against engaging in such actions.",
      true,
      true
    );
    startAskForHelpTimeout(roomId);
  } catch (error) {
    console.error("cheat alert handler", error);
  }
};

module.exports = cheatAlert;
