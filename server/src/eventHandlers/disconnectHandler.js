const { ROOMS } = require("../cache");
const { roomStates } = require("../constants/roomConstants");
const closeTutorialRoom = require("../utils/closeTutorialRoom");

const disconnectHandler = async (roomId) => {
  try {
    console.log("Interviewee disconnected:", roomId);
    if (await ROOMS.hasRoom(roomId)) {
      if (roomId.includes("_TUTORIAL")) {
        await closeTutorialRoom(roomId);
        return;
      }

      ROOMS.clearAskForHelpTimeout(roomId);
      ROOMS.cleanupResponseQueue(roomId);

      if (ROOMS.getRoomState(roomId) === roomStates.end) return;

      await ROOMS.clearRoom(roomId);
    }
  } catch (error) {
    console.error("disconnect handler", error);
  }
};

module.exports = disconnectHandler;
