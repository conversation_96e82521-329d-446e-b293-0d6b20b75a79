const { ROOMS } = require("../cache");
const { CodeRoomHandler } = require("../models/roomHandler");

const timeWarningHandler = async (roomId) => {
  try {
    if (!(await ROOMS.hasRoom(roomId))) {
      console.log("Room not found while sending time warning", roomId);
      return;
    }

    if (ROOMS.isBotPaused(roomId)) return;
    const roomHandler = new CodeRoomHandler(roomId);
    const warningText = "You have 5 minutes remaining. ";
    await roomHandler.sendResponse(warningText);
  } catch (error) {
    console.error("time warning handler", error);
  }
};

module.exports = timeWarningHandler;
