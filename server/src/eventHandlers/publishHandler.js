const { ROOMS } = require("../cache");
const { CodeRoomHandler } = require("../models/roomHandler");
const { updateFiles, getGitDiff } = require("../utils");
const {
  SCORE_THRESHOLD_FOR_NEXT_QUESTION,
} = require("../constants/scoreThreshold");
const { gitCommit } = require("../utils/gitCommit");
const { roomStates, botStates } = require("../constants/roomConstants");
const { updateCodingConversationAndAddRating } = require("../database");
const closeRoom = require("../utils/closeRoom");
const callDataLayer = require("../utils/callDataLayer");
const { startAskForHelpTimeout } = require("../utils/roomHandlerUtils");

const publishHandler = async (roomId, changes, callback) => {
  try {
    if (!(await ROOMS.hasRoom(roomId))) {
      console.log("Room not found while publishing changes", roomId);
      callback({ success: false });
      return;
    }

    ROOMS.clearAskForHelpTimeout(roomId);
    const room_state = ROOMS.getRoomState(roomId);
    if (room_state !== roomStates.coding_conversation && room_state !== roomStates.dsa_coding_conversation) {
      console.log("Room not in coding conversation state", roomId);
      callback({ success: false });
      return;
    }

    console.log("publishHandler", roomId, changes);

    ROOMS.changeBotState(roomId, botStates.THINKING);
    ROOMS.setEvaluating(roomId, true);

    const uid = ROOMS.getUID(roomId);
    const {
      question,
      id,
      concepts,
      filePath: relevantFilePaths,
    } = ROOMS.getLastCodingQuestionAnswer(roomId);

    const questionNumber = ROOMS.getCurrentQuestionIndex(roomId) + 1;

    await updateFiles(roomId, uid, changes);

    const gitDiff = await getGitDiff(
      uid,
      changes,
      relevantFilePaths,
      questionNumber
    );

    console.log("diff", gitDiff);

    const submissionHandler = new CodeRoomHandler(roomId);

    const { ratings: scores, hint } = await submissionHandler.getAnswerScore(
      question,
      concepts,
      gitDiff
    );

    callback({ success: true });

    if (ROOMS.getCompanyId(roomId)) {
      callDataLayer(
        "updateConversationAndAddRating",
        ROOMS.getCompanyId(roomId),
        "POST",
        {
          ratings: scores?.map((score) => ({
            roomId: roomId,
            coding_conversation_id: id,
            answer: gitDiff,
            rating_id: score.id,
            rating_value: score.rating_value,
            rating_reason: score.rating_reason,
            is_custom_skill: score.is_custom_skill ? 1 : 0,
          })),
        }
      ).catch((err) => console.log("publishHandler", err));
    } else {
      updateCodingConversationAndAddRating(
        roomId,
        id,
        gitDiff,
        1,
        scores,
        ""
      ).catch((err) => console.log("publishHandler", err));
    }

    let isCorrectCode = false;

    const averageRatingValue = scores.length
      ? scores.reduce((sum, s) => sum + s.rating_value, 0) / scores.length
      : 0;

    switch (ROOMS.getRoomState(roomId)) {
      case roomStates.coding_conversation: {
        const primaryScore = scores.find((score) => score.id === 1);
        const scoreValue = primaryScore
          ? primaryScore.rating_value
          : averageRatingValue;
        isCorrectCode = scoreValue >= SCORE_THRESHOLD_FOR_NEXT_QUESTION;
        break;
      }
      case roomStates.dsa_coding_conversation: {
        const primaryScore = scores.find(
          (score) => score.skill === "Data Structure Implementation"
        );
        const scoreValue = primaryScore
          ? primaryScore.rating_value
          : averageRatingValue;
        isCorrectCode = scoreValue >= SCORE_THRESHOLD_FOR_NEXT_QUESTION;
        break;
      }
    };
    const isLastResponse = submissionHandler.isLastResponse();
    const hasQuestions =
      ROOMS.getRecommendedQuestionsCount(roomId) - 1 !==
      ROOMS.getCurrentQuestionIndex(roomId);

    await ROOMS.setCodingAnswer(roomId, id, gitDiff, isCorrectCode);

    if (isCorrectCode) {
      await gitCommit(uid, changes);
      if (!isLastResponse && hasQuestions) {
        await submissionHandler.moveToNextQuestion(true);
      } else {
        await ROOMS.changeRoomState(roomId, roomStates.end);
        await submissionHandler.handleLastResponse();
      }
    } else {
      if (!isLastResponse) {
        await submissionHandler.giveHintOnSubmission(hint);
      } else {
        await ROOMS.changeRoomState(roomId, roomStates.end);
        await submissionHandler.handleLastResponse();
      }
    }

    ROOMS.changeBotState(roomId, botStates.IDLE);
    ROOMS.setEvaluating(roomId, false);
    await submissionHandler.sendResponse();
    ROOMS.changeBotState(roomId, botStates.LISTENING);

    ROOMS.clearResponse(roomId);

    isLastResponse || (isCorrectCode && !hasQuestions)
      ? await closeRoom(roomId)
      : startAskForHelpTimeout(roomId);
  } catch (err) {
    callback({ success: false });
    ROOMS.changeBotState(roomId, botStates.LISTENING);
    ROOMS.setEvaluating(roomId, false);
    console.error(err);
  }
};

module.exports = publishHandler;
