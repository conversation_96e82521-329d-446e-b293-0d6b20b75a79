const { ROOMS, REDIS } = require("../cache/index.js");
const { getRoomData } = require("../database/index.js");
const {
  <PERSON><PERSON><PERSON><PERSON>and<PERSON>,
  EnglishRoomHandler,
} = require("../models/roomHandler.js");
const { BotAudio } = require("../models/botAudio.js");
const {
  roomStates,
  botStates,
  modes,
} = require("../constants/roomConstants.js");
const { getRoomHandler } = require("../utils/getRoomHandler.js");
const {
  getDifferenceInMS,
  moment,
  convertToUTC,
} = require("../utils/moment.js");

const callDataLayer = require("../utils/callDataLayer.js");
const { startAskForHelpTimeout } = require("../utils/roomHandlerUtils.js");
const {
  getMediaserverEndpoint,
} = require("../utils/getMediaserverEndpoint.js");
const { getAssignmentStructure } = require("../apis/getAssignments.js");

async function joinRoomHandler(req, res) {
  const { roomId, companyId, conversationMode, reconnect } = req.body;
  let responseSent = false;

  try {
    if (!companyId) {
      return res
        .status(400)
        .send({ role: "unauthorized", reason: "general_access_denied" });
    }

    const roomDataCache = await REDIS.getRoomData(roomId);

    const isRejoin = roomDataCache !== null;
    const mediaserverEndpoint = isRejoin
      ? roomDataCache.mediaserverEndpoint
      : await getMediaserverEndpoint(companyId, roomId);

    if (reconnect) console.log(`Bot Reconnecting to room ${roomId}`);
    else
      console.log(
        `Interviewee ${isRejoin ? "rejoining" : "joining"} room ${roomId}`
      );

    const [roomData] = [
      (await callDataLayer(`/room/${roomId}`, companyId)).data,
    ];

    let { language, start_time, end_time, is_demo, demo_length, timezone } =
      roomData;

    const currentTime = moment().valueOf();
    if (is_demo) {
      roomData.start_time = currentTime;
      roomData.end_time = currentTime + demo_length * 60000;
    } else {
      roomData.start_time = convertToUTC(start_time, timezone).utcMs;
      roomData.end_time = convertToUTC(end_time, timezone).utcMs;
    }

    if (currentTime > roomData.end_time) {
      console.log("room already ended", roomId);
      return res.status(400).send("interview-ended");
    }

    if (roomDataCache && roomDataCache.room_state === roomStates.end) {
      console.log("room already in end state", roomId);
      return res.status(400).send("interview-ended");
    }

    await getAssignmentStructure(roomData.submission_id, companyId);

    let room_state;
    if (language === "coderoom") {
      room_state = roomStates.coding_conversation;
    } else if (language === "dsaroom") {
      room_state = roomStates.dsa_coding_conversation;
    }

    const roomHandler = isRejoin
      ? getRoomHandler(roomId, roomDataCache.room_state)
      : language === "en"
      ? new EnglishRoomHandler(roomId)
      : new CodeRoomHandler(roomId);

    if (!roomHandler) {
      console.log("Invalid room state", roomId);
      return res.status(500).send("internal-server-error");
    }

    const botAudio = new BotAudio(roomId, roomData.uid, mediaserverEndpoint);
    await botAudio.init();

    const timeoutDelay =
      (isRejoin ? roomDataCache.end_time : roomData.end_time) -
      currentTime +
      180000;

    await roomHandler.generateRoom({
      ...roomData,
      botAudio,
      room_state,
      companyId,
      mediaserverEndpoint,
      redisExpirySeconds: parseInt(timeoutDelay / 1000),
      ...(roomDataCache && { ...roomDataCache }),
      mode: conversationMode,
    });

    if (!reconnect) {
      isRejoin
        ? await roomHandler.handleRejoin()
        : await roomHandler.initialize();

      console.log(
        `Interviewee ${isRejoin ? "rejoined" : "joined"} room ${roomId}`
      );
    } else {
      console.log(`Bot Reconnected to room ${roomId}`);
    }

    res.status(200).json({
      conversationMode: conversationMode,
      chatHistory: ROOMS.getChatHistory(roomId),
      questionData: roomHandler?.displayCodingQuestion(),
      remainingTime: getDifferenceInMS(
        currentTime,
        ROOMS.getRoom(roomId).end_time
      ),
      introductionFinished:
        ROOMS.getRoomState(roomId) !== roomStates.introduction,
      botPaused: ROOMS.isBotPaused(roomId),
    });

    responseSent = true;

    if (
      !reconnect &&
      conversationMode === modes.voice &&
      !ROOMS.isBotPaused(roomId)
    ) {
      await roomHandler.sendResponse(null, false, false);
    }
    ROOMS.clearResponse(roomId);
    ROOMS.changeBotState(roomId, botStates.LISTENING);
    startAskForHelpTimeout(roomId);
  } catch (e) {
    console.log("Error in joinRoomHandler", e);
    if (!responseSent) res.status(500).json({ error: e.message });
  }
}

module.exports = joinRoomHandler;
