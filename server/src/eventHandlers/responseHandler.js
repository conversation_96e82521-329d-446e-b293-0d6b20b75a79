const { ROOMS } = require("../cache");
const { roomStates, botStates } = require("../constants/roomConstants");
const {
  insertConversation,
  generateConversationRatings,
  insertConversationRatings,
} = require("../database/index");
const { updateFiles, getGitDiff } = require("../utils");
const callDataLayer = require("../utils/callDataLayer");
const { startAskForHelpTimeout } = require("../utils/roomHandlerUtils");
const { getRoomHandler } = require("../utils/getRoomHandler");
const staticTexts = require("../utils/staticTexts");

const MAX_PROMPTS_PER_MINUTE = 10;

const responseHandler = async (data, roomId) => {
  console.log("response handler", data, roomId);

  if (!(await ROOMS.hasRoom(roomId))) {
    console.log("Room not found while handling response", roomId);
    return;
  }

  const uid = ROOMS.getUID(roomId);
  const { response, filesData: changes } = data;

  if (response && response?.trim() === "") {
    console.log("response handler: empty answer");
    return;
  }

  ROOMS.clearAskForHelpTimeout(roomId);

  if (
    ROOMS.getRoomState(roomId) === roomStates.end ||
    ROOMS.isBotPaused(roomId)
  ) {
    return;
  }
  const room_state = ROOMS.getRoomState(roomId);
  const roomHandler = getRoomHandler(roomId, room_state);

  const promptCount = ROOMS.getPromptCount(roomId);
  if (ROOMS.isRateLimitActive(roomId)) {
    console.log("Rate limit is active, ignoring prompt.");
    return;
  }

  if (promptCount >= MAX_PROMPTS_PER_MINUTE) {
    ROOMS.setRateLimitActive(roomId, true);
    ROOMS.changeBotState(roomId, botStates.IDLE);

    ROOMS.setResponse(roomId, {
      question: staticTexts.PROMPT_LIMIT_EXCEEDED_MSG1,
    });
    await roomHandler.sendResponse();

    await new Promise((resolve) => setTimeout(resolve, 60000));

    ROOMS.setResponse(roomId, {
      question: staticTexts.PROMPT_LIMIT_EXCEEDED_MSG2,
    });
    await roomHandler.sendResponse();

    ROOMS.changeBotState(roomId, botStates.LISTENING);
    ROOMS.setRateLimitActive(roomId, false);

    ROOMS.clearResponse(roomId);
    startAskForHelpTimeout(roomId);
    return;
  }

  ROOMS.addPromptTimestamp(roomId);

  ROOMS.setResponse(roomId, { answer: response });
  ROOMS.pushToConversation(roomId, {
    role: "user",
    content: response,
  });

  //for english room handler, intro
  if (room_state != roomStates.coding_conversation && room_state != roomStates.dsa_coding_conversation) {
    const companyId = ROOMS.getCompanyId(roomId);
    if (companyId) {
      callDataLayer("insertConversation", companyId, "POST", {
        roomId: roomId,
        speaker: 1,
        text: response,
      })
        .then((res) =>
          generateConversationRatings(roomId).then(
            (ratings) =>
              ratings.length > 0 &&
              callDataLayer("insertConversationRatings", companyId, "POST", {
                roomId: roomId,
                conversationId: res.id,
                ratings: ratings,
              }).catch((err) => console.log("responseHandler", err))
          )
        )
        .catch((err) => console.log("responseHandler", err));
    } else {
      insertConversation(roomId, 1, response).then((res) =>
        generateConversationRatings(roomId).then(
          (ratings) =>
            ratings.length > 0 &&
            insertConversationRatings(roomId, res.insertId, ratings)
        )
      );
    }
  }

  const isEvaluating = ROOMS.getEvaluating(roomId);

  if (isEvaluating) {
    await roomHandler.sendResponse(staticTexts.publishInterruptPrompt);
    return;
  }

  ROOMS.changeBotState(roomId, botStates.THINKING);

  await updateFiles(roomId, uid, changes);
  const codeChanges = await getGitDiff(uid, changes);

  const prompt = roomHandler.getPrompt(codeChanges);
  await roomHandler.generateNextResponse(prompt);

  ROOMS.changeBotState(roomId, botStates.IDLE);
  await roomHandler.sendResponse();
  ROOMS.changeBotState(roomId, botStates.LISTENING);

  ROOMS.clearResponse(roomId);
  startAskForHelpTimeout(roomId);
};

module.exports = responseHandler;
