const closeRoom = require("../utils/closeRoom");
const { CodeRoomHandler } = require("../models/roomHandler");
const { ROOMS } = require("../cache/index");
const callDataLayer = require("../utils/callDataLayer");
const { updateCodingConversationAndAddRating } = require("../database");
const { updateFiles, getGitDiff } = require("../utils");
const { roomStates } = require("../constants/roomConstants");

const endInterviewHandler = async (
  roomId,
  changes,
  force = false,
  callback = () => {}
) => {
  try {
    console.log("End interview called for room", roomId, {
      changes,
      force,
    });

    if (!(await ROOMS.hasRoom(roomId))) {
      console.log("Room not found while ending interview", roomId);
      callback({});
      return;
    }

    if (ROOMS.getRoomState(roomId) === roomStates.end) {
      console.log("Room already ended", roomId);
      callback({});
      return;
    }

    ROOMS.clearAskForHelpTimeout(roomId);
    const roomHandler = new CodeRoomHandler(roomId);

    if (changes && changes.length) {
      const uid = ROOMS.getUID(roomId);
      await updateFiles(roomId, uid, changes);

      const {
        question,
        id,
        concepts,
        filePath: relevantFilePaths,
      } = ROOMS.getLastCodingQuestionAnswer(roomId);
      const questionNumber = ROOMS.getCurrentQuestionIndex(roomId) + 1;

      const gitDiff = await getGitDiff(
        uid,
        changes,
        relevantFilePaths,
        questionNumber
      );

      const { ratings: scores, hint } = await roomHandler.getAnswerScore(
        question,
        concepts,
        gitDiff
      );

      if (ROOMS.getCompanyId(roomId)) {
        callDataLayer(
          "updateConversationAndAddRating",
          ROOMS.getCompanyId(roomId),
          "POST",
          {
            ratings: scores.map((score) => ({
              roomId: roomId,
              coding_conversation_id: id,
              answer: gitDiff,
              rating_id: score.id,
              rating_value: score.rating_value,
              rating_reason: score.rating_reason,
              is_custom_skill: score.is_custom_skill ? 1 : 0,
            })),
          }
        ).catch((err) => console.log("endInterviewHandler", err));
      } else {
        updateCodingConversationAndAddRating(
          roomId,
          id,
          gitDiff,
          1,
          scores,
          ""
        ).catch((err) => console.log("endInterviewHandler", err));
      }
    }

    await ROOMS.changeRoomState(roomId, roomStates.end);

    if (!force) {
      await roomHandler.handleLastResponse();
      await roomHandler.sendResponse();
    }

    callback({});

    await closeRoom(roomId);
  } catch (error) {
    console.error("end interview handler", error);
    callback({});
  }
};

module.exports = endInterviewHandler;
