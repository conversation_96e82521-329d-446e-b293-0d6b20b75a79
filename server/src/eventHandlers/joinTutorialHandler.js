const { ROOMS } = require("../cache/index.js");
const { BotAudio } = require("../models/botAudio.js");
const { getDifferenceInMS, moment } = require("../utils/moment.js");
const { TutorialHandler } = require("../models/roomHandler.js");
const { tutorialDuration } = require("../constants/roomConstants");
const {
  getMediaserverEndpoint,
} = require("../utils/getMediaserverEndpoint.js");
const callDataLayer = require("../utils/callDataLayer.js");

async function joinTutorialHandler(req, res) {
  const { roomId, companyId, reconnect } = req.body;

  try {
    if (!companyId) {
      return res
        .status(400)
        .send({ role: "unauthorized", reason: "general_access_denied" });
    }

    const mediaserverEndpoint = await getMediaserverEndpoint(companyId, roomId);

    if (reconnect) console.log(`Bot Reconnecting to room ${roomId}`);
    else console.log(`Interviewee joining room ${roomId}`);

    const [roomData] = [
      (await callDataLayer(`/room/${roomId.split("_TUTORIAL")[0]}`, companyId))
        .data,
    ];

    const currentTime = moment().valueOf();

    const start_time = currentTime;
    const end_time = currentTime + tutorialDuration * 60000;

    const roomHandler = new TutorialHandler(roomId);

    const botAudio = new BotAudio(roomId, "", mediaserverEndpoint);
    await botAudio.init();

    await roomHandler.generateRoom({
      ...roomData,
      start_time,
      end_time,
      botAudio,
      companyId,
      mediaserverEndpoint,
      is_tutorial: true,
    });

    const questionData = reconnect
      ? roomHandler.getQuestionData()
      : roomHandler.initialize();

    if (!reconnect) {
      console.log(`Interviewee joined room ${roomId}`);
    } else {
      console.log(`Bot Reconnected to room ${roomId}`);
    }

    res.status(200).json({
      remainingTime: getDifferenceInMS(
        currentTime,
        ROOMS.getRoom(roomId).end_time
      ),
      questionData,
    });
  } catch (e) {
    console.log("Error in joinTutorialHandler", e);
    res.status(500).json({ error: e.message });
  }
}

module.exports = joinTutorialHandler;
