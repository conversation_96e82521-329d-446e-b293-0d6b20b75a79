const { ROOMS } = require("../cache");
const { updateFiles, getGitDiff } = require("../utils");
const { gitCommit } = require("../utils/gitCommit");
const closeRoom = require("../utils/closeRoom");
const { CodeRoomHandler } = require("../models/roomHandler");
const { updateCodingConversationAndAddRating } = require("../database");
const callDataLayer = require("../utils/callDataLayer");
const { startAskForHelpTimeout } = require("../utils/roomHandlerUtils");
const { botStates, roomStates } = require("../constants/roomConstants");

const skipQuestionHandler = async (roomId, changes, callback) => {
  try {
    if (!(await ROOMS.hasRoom(roomId))) {
      console.log("Room not found while skipping question", roomId);
      callback({ success: false });
      return;
    }

    if (ROOMS.getRoomState(roomId) === roomStates.end) {
      console.log("Room already ended", roomId);
      callback({ success: false });
      return;
    }

    ROOMS.clearAskForHelpTimeout(roomId);
    console.log("skipQuestionHandler", roomId);
    ROOMS.changeBotState(roomId, botStates.THINKING);

    const roomHandler = new CodeRoomHandler(roomId);

    if (changes.length) {
      const {
        question,
        id,
        concepts,
        filePath: relevantFilePaths,
      } = ROOMS.getLastCodingQuestionAnswer(roomId);
      const questionNumber = ROOMS.getCurrentQuestionIndex(roomId) + 1;
      const uid = ROOMS.getUID(roomId);
      await updateFiles(roomId, uid, changes);
      const gitDiff = await getGitDiff(
        uid,
        changes,
        relevantFilePaths,
        questionNumber
      );
      await gitCommit(uid, changes);
      const { ratings: scores, hint } = await roomHandler.getAnswerScore(
        question,
        concepts,
        gitDiff
      );

      if (ROOMS.getCompanyId(roomId)) {
        callDataLayer(
          "updateConversationAndAddRating",
          ROOMS.getCompanyId(roomId),
          "POST",
          {
            ratings: scores?.map((score) => ({
              roomId: roomId,
              coding_conversation_id: id,
              answer: gitDiff,
              rating_id: score.id,
              rating_value: score.rating_value,
              rating_reason: score.rating_reason,
              is_custom_skill: score.is_custom_skill ? 1 : 0,
            })),
          }
        ).catch((err) => console.log("skipQuestionHandler", err));
      } else {
        updateCodingConversationAndAddRating(
          roomId,
          id,
          gitDiff,
          1,
          0,
          ""
        ).catch((err) => console.log("skipQuestionHandler", err));
      }
    }

    const isLastResponse = roomHandler.isLastResponse();

    const hasQuestions =
      ROOMS.getRecommendedQuestionsCount(roomId) - 1 !==
      ROOMS.getCurrentQuestionIndex(roomId);

    if (!isLastResponse && hasQuestions) {
      await roomHandler.moveToNextQuestion(false);
    } else {
      await ROOMS.changeRoomState(roomId, roomStates.end);
      await roomHandler.handleLastResponse();
    }

    callback({ success: true });

    ROOMS.changeBotState(roomId, botStates.IDLE);
    await roomHandler.sendResponse();
    ROOMS.changeBotState(roomId, botStates.LISTENING);

    ROOMS.clearResponse(roomId);

    isLastResponse || !hasQuestions
      ? await closeRoom(roomId)
      : startAskForHelpTimeout(roomId);
  } catch (error) {
    console.error("skipQuestionHandler", error);
    ROOMS.changeBotState(roomId, botStates.LISTENING);
    callback({ success: false });
  }
};

module.exports = skipQuestionHandler;
