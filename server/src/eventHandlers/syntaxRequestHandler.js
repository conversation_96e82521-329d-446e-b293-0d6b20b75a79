const { ROOMS } = require("../cache");
const { roomStates } = require("../constants/roomConstants");
const { CodeRoomHandler } = require("../models/roomHandler");

const syntaxRequestHandler = async (roomId, language, query) => {
  try {
    if (!(await ROOMS.hasRoom(roomId))) {
      console.log("Room not found while getting syntax", roomId);
      return null;
    }

    if (ROOMS.getRoomState(roomId) === roomStates.end) {
      console.log("Room not in coding conversation state", roomId);
      return;
    }

    ROOMS.clearAskForHelpTimeout(roomId);
    console.log("syntaxRequestHandler", roomId);

    const roomHandler = new CodeRoomHandler(roomId);
    const syntax = await roomHandler.getSyntax(language, query);
    return syntax;
  } catch (error) {
    console.error("syntaxRequestHandler", error);
    return null;
  }
};

module.exports = syntaxRequestHandler;
