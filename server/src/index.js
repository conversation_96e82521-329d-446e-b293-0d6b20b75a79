const path = require("path");
const dotenv = require("dotenv");
dotenv.config({
  path: path.resolve(
    process.cwd(),
    process.env.NODE_ENV === "production" ? ".env" : ".env.local"
  ),
});

const originalLog = console.log;
const originalError = console.error;

console.log = function (...args) {
  const timestamp = new Date().toISOString();
  originalLog.apply(console, [timestamp, ...args]);
};

console.error = function (...args) {
  const timestamp = new Date().toISOString();
  originalError.apply(console, [timestamp, ...args]);
};

const moment = require("moment-timezone");
const http = require("http");
const express = require("express");
const cors = require("cors");
const axios = require("axios");

const {
  pool,
  fetchRatings,
  fetchInitialQuestions,
  getRoomData,
  fetchAllSkills,
} = require("./database/index.js");
const { corsOptions } = require("./configs/index.js");
const {
  RATINGS,
  ROOMS,
  QUESTIONS,
  REDIS,
  SKILLS,
  DSA_SKILLS,
} = require("./cache/index.js");
const {
  getCustomToken,
  getFromRealtimeDatabase,
} = require("./firebase/index.js");
const { authMiddleware } = require("./middleware/authMiddleware.js");
const {
  internalApiMiddleware,
} = require("./middleware/internalApiMiddleware.js");
const {
  getAssignments,
  getAssignmentStructure,
} = require("./apis/getAssignments.js");
const { getFeedbackQuestions } = require("./apis/getFeedbackQuestions.js");
const { getFileContent } = require("./apis/getFileContent.js");
const { handleCloneRepoApiCall } = require("./apis/cloneRepo.js");
const {
  submitAssignmentByGithubLink,
} = require("./apis/submitAssignmentByGithubLink.js");
const {
  submitAssignmentByDirectoryUpload,
} = require("./apis/submitAssignmentByDirectoryUpload.js");
const { getSpeechToken } = require("./apis/getSpeechToken.js");
const { validateRoomAccess } = require("./apis/validateRoomAccess.js");
const superAdminMiddleware = require("./middleware/superAdminMiddleware.js");
const getAllAdmins = require("./apis/getAllAdmins.js");
const sendInvite = require("./apis/sendInvite.js");
const adminSignup = require("./apis/adminSignup.js");
const makeSuperAdmin = require("./apis/makeSuperAdmin.js");
const getGeneratedQuestions = require("./apis/getGeneratedQuestions.js");
const scheduleInterview = require("./apis/scheduleInterview.js");
const sendMetaInfo = require("./apis/sendMetaInfo.js");
const sendCheatingTimestamps = require("./apis/sendCheatingTimestamps.js");
const {
  getNumSlotsPerDay,
  dateConversionMiddleware,
} = require("./apis/getNumSlotsPerDay.js");
const getAvailableSlots = require("./apis/getAvailableSlots.js");
const {
  bookSlot,
  bookSlotTimezoneConversionMiddleware,
} = require("./apis/bookSlot.js");
const {
  sendJobFormDetailsV1,
  sendJobFormDetailsV2,
  sendJobFormDetailsV3,
  sendJobFormDetailsV4,
  sendJobFormDetailsV5,
} = require("./apis/sendJobFormDetails.js");
const getCandidateSkills = require("./apis/getUserSkills.js");
const addCoins = require("./apis/addCoins.js");
const { getFormCandidates } = require("./apis/getFormCandidates.js");
const getCandidateDetails = require("./apis/getCandidateDetails.js");
const { checkRoomIsDemo } = require("./apis/checkRoomIsDemo.js");
const { storeVoiceEmbeddings } = require("./apis/storeVoiceEmbeddings.js");
const { getEmbeddings } = require("./apis/getEmbeddings.js");
const {
  getCandidateAssignmentV1,
  getCandidateAssignmentV2,
  getCandidateAssignmentV4,
  getCandidateAssignmentV5,
} = require("./apis/getCandidateAssignment.js");
const adminMiddleware = require("./middleware/adminMiddleware.js");
const authorisationMiddleware = require("./middleware/authorisationMiddleware.js");
const techyrrAdminMiddleware = require("./middleware/techyrrAdminMiddleware.js");
const { checkCompany } = require("./apis/techyrr/checkCompany.js");
const { addCompany } = require("./apis/techyrr/addCompany.js");
const sendRecommendedQuestions = require("./apis/sendRecommendedQuestions.js");
const {
  authorizeAssignmentSubmission,
} = require("./middleware/authorizeAssignmentSubmission.js");
const { getAssignmentDetails } = require("./apis/getAssignmentDetails.js");
const fillFeedback = require("./apis/feedback.js");
const { getFormTechStack } = require("./apis/getFormTechStack.js");
const getProgrammingSkills = require("./apis/getProgrammingSkills.js");
const checkCompanyMiddleware = require("./middleware/checkCompanyMiddleware.js");
const {
  makeJobFormDraftV1,
  makeJobFormDraftV2,
  makeJobFormDraftV4,
  makeJobFormDraftV5,
} = require("./apis/makeJobFormDraft.js");
const {
  getJobFormDraftsV1,
  getJobFormDraftsV2,
  getJobFormDraftsV4,
  getJobFormDraftsV5,
} = require("./apis/getJobFormDrafts.js");
const deleteDraft = require("./apis/deleteDraft.js");
const {
  addCandidatesToFormV1,
  addCandidatesToFormV2,
  addCandidatesToFormV3,
  addCandidatesToFormV4,
  addCandidatesToFormV5,
} = require("./apis/addCandidatesToForm.js");
const callDataLayer = require("./utils/callDataLayer.js");
const joinRoomHandler = require("./eventHandlers/joinRoomHandler.js");
const joinTutorialHandler = require("./eventHandlers/joinTutorialHandler.js");
const {
  getLastUsedMailTemplatesV1,
  getLastUsedMailTemplatesV2,
  getLastUsedMailTemplatesV4,
  getLastUsedMailTemplatesV3,
  getLastUsedMailTemplatesV5,
} = require("./apis/getLastUsedMailTemplates.js");
const getUnapprovedCustomSkills = require("./apis/getUnapprovedCustomSkills.js");
const approveCustomSkills = require("./apis/approveCustomSkills.js");
const approveForm = require("./apis/approveForm.js");
const downloadZip = require("./apis/downloadZip.js");
const connectRabbitMQ = require("./models/rabbitMQConsumer.js");
const sendProjectRepo = require("./apis/sendProjectRepo.js");
const assignmentRepoMiddleware = require("./middleware/assignmentRepoMiddleware.js");
const updateFileCreateTime = require("./apis/updateFileCreateTime.js");
const getFileCreateTime = require("./apis/getFileCreateTime.js");
const downloadRecording = require("./apis/downloadRecording.js");
const { getDataFromJD } = require("./apis/getDataFromJD.js");
const {
  getAllFormsV1,
  getAllFormsV2,
  getAllFormsV4,
  getAllFormsV5,
} = require("./apis/getAllForms.js");
const {
  allocateRepoForInterview,
} = require("./apis/allocateRepoForInterview.js");
const {
  getFormDetailsAdmin,
  getDetailsForRepoAllocation,
  getDetailsForQuestionsApproval,
} = require("./apis/getFormDetails.js");
const checkInterviewStatus = require("./apis/checkInterviewStatus.js");
const getAdminCoinWallet = require("./apis/getAdminCoinWallet.js");
const closeJobForm = require("./apis/closeJobForm.js");
const getDailyTransactionByMonth = require("./apis/getDailyTransactionByMonth.js");
const {
  getFirstTransactionYear,
} = require("./apis/getFirstTransactionYear.js");
const {
  getMonthlyTransactionsByYear,
} = require("./apis/getMonthlyTransactionsByYear.js");
const healthCheck = require("./apis/healthCheck.js");
const {
  tutorialDuration,
  roomStates,
} = require("./constants/roomConstants.js");
const { cancelSlot } = require("./apis/cancelSlot.js");
const reportError = require("./apis/reportError.js");
const verifyOrder = require("./apis/verifyOrder.js");
const createOrder = require("./apis/createOrder.js");
const paymentFailed = require("./apis/paymentFailed.js");
const approveAllocatedQuestions = require("./apis/approveAllocatedQuestions.js");
const { getAdminData } = require("./apis/getAdminData.js");
const { getPageNames } = require("./apis/getPageNames.js");
const { createAdminRole } = require("./apis/createAdminRole.js");
const { assignRoleToAdmin } = require("./apis/assignAdminRole.js");
const { sendAdminInvite } = require("./apis/sendAdminInvite.js");
const { getRoles } = require("./apis/getRoles.js");
const { sendHiringEmail } = require("./apis/sendHiringEmail.js");
const checkMergingStatus = require("./apis/checkMergingStatus.js");
const {
  updateJobFormDetailsV1,
  updateJobFormDetailsV2,
  updateJobFormDetailsV4,
  updateJobFormDetailsV5,
} = require("./apis/updateJobFormDetails.js");
const endInterview = require("./apis/endInterview.js");
const { downloadCandidateData } = require("./apis/downloadCandidateData.js");
const sendImageFiles = require("./apis/sendImageFiles.js");
const { getB2CPricing } = require("./apis/getB2CPricing.js");
const {
  createCandidatesView,
  getFormViews,
  getFormViewDetails,
  updateCandidateNote,
  deleteCandidatesViews,
  getViewCandidates,
} = require("./apis/candidatesView.js");
const { convertToUTC } = require("./utils/moment.js");
const { sendErrorEmail } = require("./utils/insertIntoRabbitMQ.js");

process.on("uncaughtException", (err) => {
  console.error("Uncaught Exception:", err);
  sendErrorEmail({
    isSendMail: true,
    errorFunction: "uncaughtException",
    error: JSON.stringify({
      message: err.message,
      stack: err.stack,
    }),
  });
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection:", reason);
  sendErrorEmail({
    isSendMail: true,
    errorFunction: "unhandledRejection",
    error: JSON.stringify({
      message: reason.message,
      stack: reason.stack,
    }),
  });
});

const port = 5005;
const app = express();

app.use(cors(corsOptions));
app.use(express.json());
app.use(express.static("public"));
app.use("/", authMiddleware);

const server = http.createServer(app);

app.get("/", (req, res) => {
  res.sendFile("/index.html");
});

app.get("/get-speech-token", getSpeechToken);

console.log(moment.tz.guess());

app.post("/get-custom-token", getCustomToken);

app.get(
  "/get-assignment-details/:assignmentId",
  authorizeAssignmentSubmission,
  getAssignmentDetails
);
app.post(
  "/save-user-assignment-by-github-link/:assignmentId",
  authorizeAssignmentSubmission,
  submitAssignmentByGithubLink
);

app.post(
  "/save-user-assignment-by-directory-upload/:assignmentId",
  authorizeAssignmentSubmission,
  submitAssignmentByDirectoryUpload
);

app.get(
  "/get-num-slots/:submissionId",
  getNumSlotsPerDay,
  dateConversionMiddleware
);

app.post("/get-available-slots", getAvailableSlots);

app.post("/book-slot", bookSlotTimezoneConversionMiddleware, bookSlot);

app.post("/cancel-slot/:submissionId", cancelSlot);

app.get("/assignment-repo/:uid/*", assignmentRepoMiddleware, sendProjectRepo);

app.post("/download-zip", downloadZip);

app.post("/metaInfo", sendMetaInfo);

app.post("/send-image/:roomId", sendImageFiles);

app.post("/check-room/:roomId", async (req, res) => {
  try {
    const { roomId } = req.params;
    const { companyId, email: loggedInUserEmail } = res.locals;

    if (!roomId) {
      //use more complex validation
      console.log(
        "RoomId undefined or null when",
        loggedInUserEmail,
        "tried to access room details"
      );
      return res.status(400).json({ errorCode: "invalid-room" });
    }

    const accessResult = await validateRoomAccess(
      roomId,
      loggedInUserEmail,
      res.locals.role,
      companyId
    );

    if (accessResult.role === "unauthorized") {
      if (accessResult.reason === "room_not_found") {
        return res.status(400).json({ errorCode: "invalid-room" });
      }
      if (accessResult.reason === "email_mismatch") {
        return res.status(400).json({
          errorCode: "email-mismatch",
          intendedEmail: accessResult.intendedEmail,
          currentEmail: loggedInUserEmail,
        });
      }
      if (accessResult.reason === "general_access_denied") {
        return res.status(400).json({ errorCode: "unauthorized" });
      }
    }

    //user wants to rejoin
    const doesRoomExist = await REDIS.getRoomData(roomId);
    const tutorialDurationInMs = tutorialDuration * 60 * 1000;
    const currentTime = moment().valueOf();
    console.log("doesRoomExist", doesRoomExist);

    let assignmentStructure;
    let botPaused = false;
    if (doesRoomExist) {
      let {
        end_time,
        start_time,
        language,
        is_demo,
        programming_languages,
        submission_id,
      } = doesRoomExist;

      botPaused = doesRoomExist.botPaused;

      if (doesRoomExist.room_state === roomStates.end) {
        return res.status(400).json({ errorCode: "interview-ended" });
      }

      const timeLeftInMs = end_time - currentTime;

      console.log("timeLeftInMs re join", roomId, timeLeftInMs);

      if (timeLeftInMs < 120000) {
        return res.status(400).json({ errorCode: "interview-ended" });
      }

      const timePassedSinceStartInMs = currentTime - start_time;
      const interviewDurationInMinutes = (
        (end_time - start_time) /
        60000
      ).toFixed(2);

      if (language === "coderoom" || language === "dsaroom")
        assignmentStructure = await getAssignmentStructure(
          submission_id,
          companyId
        );

      return res.status(200).json({
        data: {
          language,
          programmingLanguages: programming_languages,
          interviewDurationInMinutes,
          timeLeftForTheInterviewToStart:
            timePassedSinceStartInMs < 0 ? -timePassedSinceStartInMs : 0,
          timeLeftForTutorialToStart:
            timePassedSinceStartInMs <= -tutorialDurationInMs
              ? -(timePassedSinceStartInMs + tutorialDurationInMs)
              : 0,
          structure: assignmentStructure,
          botPaused,
          isDemo: is_demo,
        },
        role: accessResult.role,
      });
    }

    //joining for the first time
    const roomData = companyId
      ? [(await callDataLayer(`/room/${roomId}`, companyId)).data]
      : await getRoomData(roomId);

    const isRoomValid = roomData && (companyId || roomData.length);
    console.log("roomData", roomData, isRoomValid, roomId);
    if (!isRoomValid) {
      console.log(
        "Room not found with ID",
        roomId,
        "when",
        loggedInUserEmail,
        "tried to access room details"
      );
      return res.status(400).json({ errorCode: "invalid-room" });
    }

    let {
      start_time,
      end_time,
      is_demo,
      demo_length,
      language,
      submission_id,
      programming_languages,
      is_canceled,
      timezone,
    } = roomData[0];

    if (is_canceled) {
      return res
        .status(400)
        .json({ errorCode: "room-canceled-or-rescheduled" });
    }

    if (is_demo) {
      start_time = currentTime;
      end_time = currentTime + demo_length * 60000;
    } else {
      start_time = convertToUTC(start_time, timezone).utcMs;
      end_time = convertToUTC(end_time, timezone).utcMs;
    }

    const timeLeftInMs = end_time - currentTime;

    console.log("timeLeftInMs", roomId, timeLeftInMs);

    if (timeLeftInMs < 120000) {
      return res.status(400).json({ errorCode: "interview-ended" });
    }

    const timePassedSinceStartInMs = currentTime - start_time;
    const interviewDurationInMinutes = (
      (end_time - start_time) /
      60000
    ).toFixed(2);

    if (language === "coderoom" || language === "dsaroom")
      assignmentStructure = await getAssignmentStructure(
        submission_id,
        companyId
      );

    return res.status(200).json({
      data: {
        language,
        programmingLanguages: programming_languages,
        interviewDurationInMinutes,
        timeLeftForTheInterviewToStart:
          timePassedSinceStartInMs < 0 ? -timePassedSinceStartInMs : 0,
        //tutorial can be joined 10 minutes before the start time
        timeLeftForTutorialToStart:
          timePassedSinceStartInMs <= -tutorialDurationInMs
            ? -(timePassedSinceStartInMs + tutorialDurationInMs)
            : 0,
        structure: assignmentStructure,
        botPaused,
        isDemo: is_demo,
      },
      role: accessResult.role,
    });
  } catch (error) {
    console.error("Error while checking room:", error);
    if (error?.data?.code === "job-form-closed") {
      return res.status(500).json({ errorCode: "job-form-closed" });
    }
    return res.status(500).json({ errorCode: "internal-server-error" });
  }
});

app.post("/report-error", reportError);
app.post("/join-room", joinRoomHandler);

app.post("/join-tutorial-room", joinTutorialHandler);

app.post("/send-cheating-timestamp", sendCheatingTimestamps);

app.post("/fill-feedback", fillFeedback);
app.get("/getFeedbackQuestions", getFeedbackQuestions);

// app.post("/submitAssignmentChanges", submitAssignmentChanges);

// API endpoint to get the structure of a directory
app.get("/get-assignment", getAssignments);

// API endpoint to get the content of a file
app.post("/get-file-content", getFileContent);

app.post("/clone-repo", handleCloneRepoApiCall);

// app.post("/saveAssignmentChange", saveAssignmentChange);

app.get(
  "/super-admin/get-generated-questions/:submissionId",
  getGeneratedQuestions
);

app.use("/super-admin", superAdminMiddleware);
app.post("/super-admin/invite", sendInvite);
app.get("/super-admin/getAllAdmins", getAllAdmins);
app.get(
  "/super-admin/get-unapproved-custom-skills/:companyId/:formId",
  getUnapprovedCustomSkills
);
app.get(
  "/super-admin/get-generated-questions/:submissionId",
  getGeneratedQuestions
);
app.post("/super-admin/schedule-interview", scheduleInterview);
app.post("/super-admin/makeSuperAdmin", makeSuperAdmin);
app.post("/super-admin/approve-custom-skills", approveCustomSkills);
app.post("/super-admin/approve-form", approveForm);
app.get(
  "/super-admin/get-repo-allocation-details/:companyId/:formId/:assignmentPoolId",
  getDetailsForRepoAllocation
);
app.post(
  "/super-admin/allocate-repo-interview/:companyId/:formId/:assignmentPoolId",
  allocateRepoForInterview
);

app.post(
  "/super-admin/get-allocated-questions",
  getDetailsForQuestionsApproval
);

app.post("/super-admin/approve-allocated-questions", approveAllocatedQuestions);
app.post("/super-admin/addCoins", addCoins);

// ADMIN ROUTES
app.use("/admin", adminMiddleware, authorisationMiddleware);
// app.use("/admin", adminMiddleware);
app.post("/admin/signup", adminSignup);
app.post("/admin/v1/sendJobFormDetails", sendJobFormDetailsV1); // to submit a form
app.post("/admin/v2/sendJobFormDetails", sendJobFormDetailsV2); // to submit a form
app.post("/admin/v3/sendJobFormDetails", sendJobFormDetailsV3); // to submit a form
app.post("/admin/v4/sendJobFormDetails", sendJobFormDetailsV4); // to submit a form
app.post("/admin/v5/sendJobFormDetails", sendJobFormDetailsV5); // to submit a form
app.post("/admin/v1/updateJobFormDetails", updateJobFormDetailsV1); // to update a form
app.post("/admin/v2/updateJobFormDetails", updateJobFormDetailsV2); // to update a form
app.post("/admin/v4/updateJobFormDetails", updateJobFormDetailsV4); // to update a form
app.post("/admin/v5/updateJobFormDetails", updateJobFormDetailsV5); // to update a form
app.post("/admin/getCandidateSkills", getCandidateSkills); // this is a demo function which is getting all the skills of a candidate by its id
app.get("/admin/v1/getAllForms/:companyName", getAllFormsV1); // to get all the forms submitted using company name
app.get("/admin/v2/getAllForms/:companyName", getAllFormsV2); // to get all the forms submitted using company name
app.get("/admin/v4/getAllForms/:companyName", getAllFormsV4); // to get all the forms submitted using company name
app.get("/admin/v5/getAllForms/:companyName", getAllFormsV5); // to get all the forms submitted using company name
// app.put("/admin/addCoins", addCoins); // add coins for a company
app.get("/admin/getFormDetails/:formId", getFormDetailsAdmin);
app.post("/admin/invite", sendAdminInvite);
app.get("/admin/getAllAdminData", getAdminData);
app.post("/admin/getPagesForAdmin", getPageNames);
app.post("/admin/createAdminRole", createAdminRole);
app.post("/admin/assignRole", assignRoleToAdmin);
app.get("/admin/getRoles", getRoles);
app.post("/admin/sendHiringEmail", sendHiringEmail);
app.post("/admin/download-candidate-data", downloadCandidateData);
app.post("/admin/createCandidatesView", createCandidatesView);
app.post("/admin/getFormViews", getFormViews);
app.get("/admin/getFormViewDetails/:viewId", getFormViewDetails);
app.post("/admin/updateCandidateNote/:viewId/:id", updateCandidateNote);
app.post("/admin/deleteCandidatesViews", deleteCandidatesViews);

app.post(
  "/admin/:version/getFormCandidates",
  checkCompanyMiddleware,
  getFormCandidates
);

app.post(
  "/admin/:version/getViewCandidates",
  checkCompanyMiddleware,
  getViewCandidates
);

app.post(
  "/admin/getCandidateDetails",
  checkCompanyMiddleware,
  getCandidateDetails
);
app.post("/admin/v1/getCandidateAssignment", getCandidateAssignmentV1); // get assignment
app.post("/admin/v2/getCandidateAssignment", getCandidateAssignmentV2); // get assignment
app.post("/admin/v4/getCandidateAssignment", getCandidateAssignmentV4); // get assignment
app.post("/admin/v5/getCandidateAssignment", getCandidateAssignmentV5); // get assignment
app.post("/admin/sendRecommendedQuestions", sendRecommendedQuestions); // adds the recommended questions in the realtime db
app.get("/admin/getFormTechStack/:licence", getFormTechStack);
app.post("/admin/getProgrammingSkills/:licence", getProgrammingSkills);
app.post("/admin/v1/makeJobFormDraft", makeJobFormDraftV1);
app.post("/admin/v2/makeJobFormDraft", makeJobFormDraftV2);
app.post("/admin/v4/makeJobFormDraft", makeJobFormDraftV4);
app.post("/admin/v5/makeJobFormDraft", makeJobFormDraftV5);
app.post("/admin/v1/getJobFormDrafts", getJobFormDraftsV1); // getting all form drafts
app.post("/admin/v2/getJobFormDrafts", getJobFormDraftsV2); // getting all form drafts
app.post("/admin/v4/getJobFormDrafts", getJobFormDraftsV4); // getting all form drafts
app.post("/admin/v5/getJobFormDrafts", getJobFormDraftsV5); // getting all form drafts
app.post("/admin/deleteDraft", deleteDraft);
app.post("/admin/v1/addCandidatesToForm", addCandidatesToFormV1); // add candidates to form
app.post("/admin/v2/addCandidatesToForm", addCandidatesToFormV2); // add candidates to form
app.post("/admin/v4/addCandidatesToForm", addCandidatesToFormV4); // add candidates to form
app.post("/admin/v5/addCandidatesToForm", addCandidatesToFormV5); // add candidates to form
// app.post("/admin/v2/addCandidatesToForm", addCandidatesToFormV3); // add candidates to form
app.get("/admin/v1/getMailTemplates", getLastUsedMailTemplatesV1);
app.get("/admin/v2/getMailTemplates", getLastUsedMailTemplatesV2);
app.get("/admin/v3/getMailTemplates", getLastUsedMailTemplatesV3);
app.get("/admin/v4/getMailTemplates", getLastUsedMailTemplatesV4);
app.get("/admin/v5/getMailTemplates", getLastUsedMailTemplatesV5);
app.get("/admin/downloadRecording/:file", downloadRecording);
app.post("/admin/getDataFromJD", getDataFromJD);
app.get("/admin/getAdminCoinWallet", getAdminCoinWallet);
app.post("/admin/closeJobForm", closeJobForm);
app.get("/admin/getDailyTransactionByMonth", getDailyTransactionByMonth);
app.get("/admin/getFirstTransactionYear", getFirstTransactionYear);
app.get(
  "/admin/getMonthlyTransactionsByYear/:year",
  getMonthlyTransactionsByYear
);
app.post("/admin/createOrder", createOrder);
app.post("/admin/verifyOrder", verifyOrder);
app.post("/admin/paymentFailed", paymentFailed);

//! api for internal server api calls
app.use("/internal", internalApiMiddleware);
app.get("/internal/check-room-demo/:roomId", checkRoomIsDemo);
app.post("/internal/store-embeddings", storeVoiceEmbeddings);
app.get("/internal/embeddings/:uid", getEmbeddings);
app.get("/internal/get-create-time", getFileCreateTime);
app.post("/internal/update-create-time", updateFileCreateTime);
app.post("/internal/check-interview-status", checkInterviewStatus);
app.post("/internal/check-merging-status", checkMergingStatus);
app.get("/internal/health-check", healthCheck);
app.post("/internal/end-interview", endInterview);

//! Techyrr Admin Routes
app.use("/techyrr-admin", techyrrAdminMiddleware);
app.post("/techyrr-admin/add-company", addCompany);

//* check company name is taken or not - no middlware
app.post("/techyrr/check-company", checkCompany);

//! Public Routes
app.get("/public/B2CPricing", getB2CPricing);

pool.getConnection((err, connection) => {
  if (err) {
    console.log("Error connecting to the database: ", err.stack);
    process.exit(1);
  } else {
    console.log("Connected to the database as id ", connection.threadId);
    connection.release();

    const fetchInitialDataWithRetry = async (retries = 30) => {
      let attempt = 0;

      while (attempt <= retries) {
        try {
          const response = await axios.get(
            `${process.env.DATA_LAYER_URL}internal/get-initial-data`,
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${process.env.STATIC_TOKEN}`,
              },
            }
          );

          if (response.status !== 200) {
            throw new Error(`Error fetching initial data: ${response.status}`);
          }

          return response;
        } catch (err) {
          attempt++;
          if (attempt > retries) {
            console.log(
              `Failed to fetch initial data after ${retries} attempts.`,
              err
            );
            throw err;
          } else {
            console.log(
              `Fetch attempt ${attempt} failed: ${err.message}. Retrying in 10 seconds...`
            );
            await new Promise((resolve) => setTimeout(resolve, 10 * 1000));
          }
        }
      }
    };

    fetchInitialDataWithRetry()
      .then(async (res) => {
        const { ratings, initialQuestions, skills, dsaSkills } = res.data.data;
        RATINGS.setRatings(ratings);
        QUESTIONS.setQuestions(initialQuestions);
        SKILLS.setSkills(skills);
        DSA_SKILLS.setDSASkills(dsaSkills);
        await connectRabbitMQ();
        await REDIS.client.connect();
        server.listen(port, () => {
          console.log(`EvalAI server running at http://localhost:${port}/`);
        });

        process.on("SIGINT", () => {
          console.log("SIGINT received. Closing server.");
          server.close(() => {
            console.log("Server closed.");
            process.exit(0);
          });
        });

        process.on("SIGTERM", () => {
          console.log("SIGTERM received. Closing server.");
          server.close(() => {
            console.log("Server closed.");
            process.exit(0);
          });
        });
      })
      .catch((err) => {
        console.error("Failed to initialize application:", err);
        process.exit(1);
      });
  }
});
