import cv2
import numpy as np
import mediapipe as mp
from .save_data import save_metrics_to_csv
from .detector_pool import cpu_detector_pool

brightness_low = 30
brightness_high = 220
eye_brightness_low = 25
avg_face_detection_rate = 0.7

def analyze_frame(frame, detector_info):
    detector = detector_info["detector"]

    timestamp = detector_info["last_timestamp"] + 10000
    detector_info["last_timestamp"] = timestamp

    image_payload = mp.Image(image_format=mp.ImageFormat.SRGB, data=frame)
    results = detector.detect_for_video(image_payload, timestamp)

    analysis = {
        "face_detected": 0,
        "face_brightness": 0,
        "left_eye_brightness": 0,
        "right_eye_brightness": 0,
    }

    if results.face_landmarks:
        analysis["face_detected"] = 1
        face_landmarks = results.face_landmarks[0]
        h, w, _ = frame.shape
        x_min, y_min = w, h
        x_max, y_max = 0, 0

        for lm in face_landmarks:
            x, y = int(lm.x * w), int(lm.y * h)
            x_min = min(x_min, x)
            y_min = min(y_min, y)
            x_max = max(x_max, x)
            y_max = max(y_max, y)

        x_min = max(0, x_min)
        y_min = max(0, y_min)
        x_max = min(w, x_max)
        y_max = min(h, y_max)

        face_roi = frame[y_min:y_max, x_min:x_max]

        if face_roi.size > 0:
            gray_face = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)
            analysis["face_brightness"] = int(np.mean(gray_face))

        left_eye_top = np.array([face_landmarks[159].x * w, face_landmarks[159].y * h])
        left_eye_bottom = np.array(
            [face_landmarks[145].x * w, face_landmarks[145].y * h]
        )
        left_eye_inner = np.array(
            [face_landmarks[133].x * w, face_landmarks[133].y * h]
        )
        left_eye_outer = np.array(
            [face_landmarks[130].x * w, face_landmarks[130].y * h]
        )

        right_eye_top = np.array([face_landmarks[386].x * w, face_landmarks[386].y * h])
        right_eye_bottom = np.array(
            [face_landmarks[374].x * w, face_landmarks[374].y * h]
        )
        right_eye_inner = np.array(
            [face_landmarks[362].x * w, face_landmarks[362].y * h]
        )
        right_eye_outer = np.array(
            [face_landmarks[359].x * w, face_landmarks[359].y * h]
        )

        left_eye_width = np.linalg.norm(left_eye_outer - left_eye_inner)
        left_eye_height = np.linalg.norm(left_eye_top - left_eye_bottom)
        right_eye_width = np.linalg.norm(right_eye_outer - right_eye_inner)
        right_eye_height = np.linalg.norm(right_eye_top - right_eye_bottom)

        margin = 5

        left_x1 = max(0, int(min(left_eye_inner[0], left_eye_outer[0]) - margin))
        left_y1 = max(0, int(min(left_eye_top[1], left_eye_bottom[1]) - margin))
        left_x2 = min(w, int(max(left_eye_inner[0], left_eye_outer[0]) + margin))
        left_y2 = min(h, int(max(left_eye_top[1], left_eye_bottom[1]) + margin))

        right_x1 = max(0, int(min(right_eye_inner[0], right_eye_outer[0]) - margin))
        right_y1 = max(0, int(min(right_eye_top[1], right_eye_bottom[1]) - margin))
        right_x2 = min(w, int(max(right_eye_inner[0], right_eye_outer[0]) + margin))
        right_y2 = min(h, int(max(right_eye_top[1], right_eye_bottom[1]) + margin))

        left_eye_roi = frame[left_y1:left_y2, left_x1:left_x2]
        right_eye_roi = frame[right_y1:right_y2, right_x1:right_x2]

        if left_eye_roi.size > 0:
            left_eye_gray = cv2.cvtColor(left_eye_roi, cv2.COLOR_BGR2GRAY)
            analysis["left_eye_brightness"] = int(np.mean(left_eye_gray))

        if right_eye_roi.size > 0:
            right_eye_gray = cv2.cvtColor(right_eye_roi, cv2.COLOR_BGR2GRAY)
            analysis["right_eye_brightness"] = int(np.mean(right_eye_gray))

    return analysis


def visibility_test(video_frames, room_id=None, interviewee_id=None, create_csv=False):
    detector_info = cpu_detector_pool.get_detector()

    try:
        face_detected_results = []
        face_brightness_results = []
        left_eye_brightness_results = []
        right_eye_brightness_results = []

        if len(video_frames) < 4:
            avg_face_detection_rate = 0.5
        else:
            avg_face_detection_rate = 0.7

        for frame in video_frames:
            try:
                analysis = analyze_frame(frame, detector_info)
            except Exception as e:
                print(f"Error analyzing frame: {e}")
                continue

            face_detected_results.append(analysis["face_detected"])

            if analysis["face_detected"]:
                face_brightness_results.append(analysis["face_brightness"])
                left_eye_brightness_results.append(analysis["left_eye_brightness"])
                right_eye_brightness_results.append(analysis["right_eye_brightness"])

        avg_face_detected = (
            sum(face_detected_results) / len(face_detected_results)
            if face_detected_results
            else 0
        )

        avg_face_brightness = (
            sum(face_brightness_results) / len(face_brightness_results)
            if face_brightness_results
            else 0
        )

        avg_left_eye_brightness = (
            sum(left_eye_brightness_results) / len(left_eye_brightness_results)
            if left_eye_brightness_results
            else 0
        )

        avg_right_eye_brightness = (
            sum(right_eye_brightness_results) / len(right_eye_brightness_results)
            if right_eye_brightness_results
            else 0
        )

        result = {
            "status": True,
            "issues": [],
            "metrics": {
                "face_detection_rate": avg_face_detected,
                "face_brightness": round(avg_face_brightness, 2),
                "left_eye_brightness": round(avg_left_eye_brightness, 2),
                "right_eye_brightness": round(avg_right_eye_brightness, 2),
            },
            "thresholds": {
                "avg_face_detection_rate": avg_face_detection_rate,
                "brightness_low": brightness_low,
                "brightness_high": brightness_high,
                "eye_brightness_low": eye_brightness_low,
            },
        }

        if avg_face_detected < avg_face_detection_rate:
            result["status"] = False
            result["issues"].append("Face is not visible properly")
        else:
            if avg_face_brightness < brightness_low:
                result["status"] = False
                result["issues"].append("Face brightness is too low")

            if avg_face_brightness > brightness_high:
                result["status"] = False
                result["issues"].append("Face brightness is too high")

            if avg_left_eye_brightness < eye_brightness_low:
                result["status"] = False
                result["issues"].append("Left eye brightness is too low")

            if avg_right_eye_brightness < eye_brightness_low:
                result["status"] = False
                result["issues"].append("Right eye brightness is too low")

        if room_id and interviewee_id and create_csv:
            save_metrics_to_csv(room_id, interviewee_id, result["metrics"])

        return result

    finally:
        cpu_detector_pool.release_detector(detector_info)
