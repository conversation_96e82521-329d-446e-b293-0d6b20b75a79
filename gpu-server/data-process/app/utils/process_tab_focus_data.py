import os
import time
import csv
import os


def process_tab_focus(file_path):
    print(f"Processing tab focus data from: {file_path}")

    result = []
    if not os.path.isfile(file_path):
        print(f"File not found: {file_path}")
        return result

    filename = os.path.basename(file_path)

    try:
        if filename.endswith(".csv"):
            with open(file_path, "r") as csvfile:
                dialect = csv.Sniffer().sniff(csvfile.read(2048))
                csvfile.seek(0)
                reader = csv.DictReader(csvfile, dialect=dialect)

                reader.fieldnames = [
                    field.strip().replace(" ", "_") for field in reader.fieldnames
                ]

                join_time = None
                current_focus_out = None
                for row in reader:
                    event_type = row["type"].strip()
                    timestamp = int(row["timestamp"])
                    if event_type == "join":
                        if join_time is None:
                            join_time = timestamp

                    elif join_time is not None:
                        relative_time = timestamp - join_time

                        if event_type == "focusOut":
                            current_focus_out = relative_time

                        elif event_type == "focusIn" and current_focus_out is not None:
                            if current_focus_out < relative_time:
                                result.append(
                                    {
                                        "start": max(0, current_focus_out),
                                        "end": relative_time,
                                    }
                                )
                            current_focus_out = None
                print(f"Processed file successfully: {file_path}")
        else:
            print("Invalid file type. Expected CSV file.")
            return result

        return result

    except Exception as e:
        print(f"Error processing tab focus data files: {e}")
        return result
