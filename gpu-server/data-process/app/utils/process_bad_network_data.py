import os
import csv


def process_bad_network_data(file_path, threshold_ms=2000):
    """
    Processes a CSV file containing bad network events with event types (type/timestamp)
    and calculates relative timestamps based on join event.

    Args:
        file_path (str): Path to the CSV file.
        threshold_ms (int): Minimum time gap (in milliseconds) between intervals.

    Returns:
        List[Dict[str, int]]: List of bad network intervals with relative start/end times.
    """
    print(f"Processing bad network data from: {file_path}")

    result = []
    if not os.path.isfile(file_path):
        print(f"File not found: {file_path}")
        return result

    try:
        if not file_path.endswith(".csv"):
            print("Invalid file type. Expected CSV file.")
            return result

        with open(file_path, "r") as csvfile:
            dialect = csv.Sniffer().sniff(csvfile.read(2048))
            csvfile.seek(0)
            reader = csv.DictReader(csvfile, dialect=dialect)

            reader.fieldnames = [
                field.strip().replace(" ", "_") for field in reader.fieldnames
            ]

            if "type" not in reader.fieldnames or "timestamp" not in reader.fieldnames:
                print(
                    f"Invalid CSV format in {file_path}. Required columns: type, timestamp")
                return result

            join_time = None
            current_issue_start = None

            for row in reader:
                try:
                    event_type = row["type"].strip()
                    timestamp = int(row["timestamp"])

                    if event_type == "join":
                        if join_time is None:
                            join_time = timestamp

                    elif join_time is not None:
                        relative_time = timestamp - join_time

                        if event_type == "networkIssueStart":
                            current_issue_start = relative_time

                        elif event_type == "networkIssueEnd" and current_issue_start is not None:
                            if current_issue_start < relative_time:
                                result.append({
                                    "start": max(0, current_issue_start),
                                    "end": relative_time
                                })
                            current_issue_start = None
                except (ValueError, KeyError) as e:
                    print(f"Error processing row: {e}")
                    continue

            # Apply threshold filter
            if result:
                result.sort(key=lambda x: x["start"])
                filtered_intervals = []
                last_end = None

                for interval in result:
                    if last_end is None or (interval["start"] - last_end) >= threshold_ms:
                        filtered_intervals.append(interval)
                        last_end = interval["end"]

                print(
                    f"Processed {len(filtered_intervals)} network intervals from {file_path}")
                return filtered_intervals

            return result

    except Exception as e:
        print(f"Error processing bad network data: {e}")
        return result
