import os
import time
import csv
import os


def process_timeline(file_path, min_inactivity_seconds=15):
    print(f"Processing input data from: {file_path}")

    if not os.path.isfile(file_path):
        print(f"File not found: {file_path}")
        return {"question": [], "inactivity": [], "start_time": None, "end_time": None}

    filename = os.path.basename(file_path)
    min_duration = min_inactivity_seconds * 1000
    result = {}

    try:
        if filename.endswith(".csv"):
            with open(file_path, "r") as csvfile:
                dialect = csv.Sniffer().sniff(csvfile.read(2048))
                csvfile.seek(0)
                reader = csv.DictReader(csvfile, dialect=dialect)

                reader.fieldnames = [
                    field.strip().replace(" ", "_") for field in reader.fieldnames
                ]

                rows = []
                first_start_time = None
                last_timestamp = None
                for row in reader:
                    start_time = int(row["Start_Time"])
                    if first_start_time is None:
                        first_start_time = start_time

                    last_timestamp = start_time

                    rows.append(
                        {
                            "Start_Time": start_time - first_start_time,
                            "Key_Count": int(row.get("Key_Count") or 0),
                            "Mouse_Movement_Count": int(
                                row.get("Mouse_Movement_Count") or 0
                            ),
                            "Bot_Speaking": int(row.get("Bot_Speaking") or 0),
                            "Question": (row.get("Question", "").strip() or None),
                        }
                    )

                if not rows:
                    raise ValueError("No records found in input file")

                for i in range(len(rows)):
                    if i < len(rows) - 1:
                        rows[i]["End_Time"] = rows[i + 1]["Start_Time"]
                    else:
                        rows[i]["End_Time"] = rows[i]["Start_Time"]

                relative_end_time = rows[-1]["End_Time"]
                absolute_end_time = first_start_time + relative_end_time

                # process question tags
                question_tags = []
                current_question = None
                question_start = None
                question_end = None

                for row in rows:
                    if row["Question"]:
                        if current_question != row["Question"]:
                            if current_question is not None:
                                question_tags.append(
                                    {
                                        "question": current_question,
                                        "start": question_start,
                                        "end": question_end,
                                    }
                                )
                            current_question = row["Question"]
                            question_start = row["Start_Time"]
                        question_end = row["End_Time"]

                # last question end
                if current_question is not None:
                    question_tags.append(
                        {
                            "question": current_question,
                            "start": question_start,
                            "end": question_end,
                        }
                    )

                # process inactivity tags
                inactivity_tags = []
                current_inactive_start = None
                current_inactive_end = None

                for row in rows:
                    if (
                        row["Key_Count"] == 0
                        and row["Mouse_Movement_Count"] == 0
                        and row["Bot_Speaking"] == 0
                    ):
                        if current_inactive_start is None:
                            current_inactive_start = row["Start_Time"]
                            current_inactive_end = row["End_Time"]
                        else:
                            current_inactive_end = row["End_Time"]
                    else:
                        if current_inactive_start is not None:
                            duration = current_inactive_end - current_inactive_start
                            if duration >= min_duration:
                                inactivity_tags.append(
                                    {
                                        "start": current_inactive_start,
                                        "end": current_inactive_end,
                                    }
                                )
                            current_inactive_start = None

                # last inactive end
                if current_inactive_start is not None:
                    duration = current_inactive_end - current_inactive_start
                    if duration >= min_duration:
                        inactivity_tags.append(
                            {
                                "start": current_inactive_start,
                                "end": current_inactive_end,
                            }
                        )

                    question_start_time = question_tags[0]["start"]
                    question_end_time = question_tags[-1]["end"]

                    inactivity_tags = [
                        tag
                        for tag in inactivity_tags
                        if tag["start"] >= question_start_time
                        and tag["end"] <= question_end_time
                    ]

                result = {
                    "question": question_tags,
                    "inactivity": inactivity_tags,
                    "start_time": first_start_time,
                    "end_time": absolute_end_time,
                }

                print(f"Processed file successfully: {file_path}")
        else:
            print("Invalid file type. Expected CSV file.")
            return {
                "question": [],
                "inactivity": [],
                "start_time": None,
                "end_time": None,
            }

        return result

    except Exception as e:
        print(f"Error processing input data files: {e}")
        return {"question": [], "inactivity": [], "start_time": None, "end_time": None}
