import os
import sys
import json
import time
import torch
import requests
import subprocess
import torchaudio
import numpy as np
from pydub import AudioSegment
from pathlib import Path
from threading import Thread
from datetime import datetime
from denoiser import pretrained
from .audio_utils import preprocess_wav
from denoiser.dsp import convert_audio
from pynvml import (
    nvmlInit,
    nvmlDeviceGetHandleByIndex,
    nvmlDeviceGetMemoryInfo,
    nvmlShutdown,
)
from .config_env import SERVER_URL, STATIC_TOKEN, RECORDING_PATH, CSV_FILE_PATH

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

model = pretrained.dns64().to(device)

GPU_MEMORY_LIMIT = 10  # in GB

env = os.environ.copy()
env["CUDA_VISIBLE_DEVICES"] = "0"


def monitor_gpu_memory(limit_gb, process, check_interval=1):
    nvmlInit()
    try:
        handle = nvmlDeviceGetHandleByIndex(0)
        while process.poll() is None:
            mem_info = nvmlDeviceGetMemoryInfo(handle)
            used_gb = mem_info.used / 1024**3

            if used_gb > limit_gb:
                print(
                    f"[ERROR]:    GPU memory usage exceeded {limit_gb}GB! Current usage: {used_gb:.2f}GB."
                )

                process.terminate()
                print(
                    f"[WARNING]:    Process terminated due to excessive GPU memory usage."
                )
                break

            time.sleep(check_interval)

    finally:
        nvmlShutdown()


def denoise_audio(file, chunk_duration=150.0):
    wav, sr = torchaudio.load(file)
    wav = wav.to(device)
    num_samples = int(sr * chunk_duration)
    num_chunks = wav.size(1) // num_samples + (
        1 if wav.size(1) % num_samples > 0 else 0
    )

    denoised_chunks = []

    for i in range(num_chunks):
        start_sample = i * num_samples
        end_sample = start_sample + num_samples

        chunk = wav[:, start_sample:end_sample]

        chunk = convert_audio(chunk, sr, model.sample_rate, model.chin)

        with torch.no_grad():
            denoised_chunk = model(chunk.unsqueeze(0))[0]

        denoised_chunks.append(denoised_chunk)

    denoised_audio = torch.cat(denoised_chunks, dim=1)

    torchaudio.save(file, denoised_audio.cpu(), model.sample_rate)
    print("denoised file - ", file)
    clear_cuda()


def process_voice_data(uid, file_dir):
    try:
        folder_path = os.path.join(RECORDING_PATH, file_dir)
        embeddings = get_embeddings(folder_path, uid)
        room_id = str(file_dir).split("_")[0]
        url = f"{SERVER_URL}/internal/store-embeddings"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {STATIC_TOKEN}",
        }
        payload = {
            "uid": uid,
            "interview_id": room_id,
            "embeddings": {"embeddings": embeddings},
        }
        response = requests.post(url, json=payload, headers=headers)
        response_data = response.json()
        if response_data.get("success") == True:
            print("Embeddings stored successfully")
        else:
            print("Something went wrong while storing embeddings in database")
            print(f"Response: {response_data}")
    except Exception as e:
        print("Error while storing embeddings in database:", e)
    finally:
        clear_cuda()
        return file_dir


def clear_cuda():
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        print("torch cache clear")


def get_embeddings(folder_path, uid):
    audio_file = concate_audio_files(folder_path, uid)
    denoise_audio(audio_file)
    preprocess_wav(Path(audio_file))

    command = [
        "venv/bin/python3",
        "app/utils/extract_embeddings.py",
        audio_file,
    ]

    process = subprocess.Popen(
        command,
        env=env,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )

    monitor_thread = Thread(target=monitor_gpu_memory, args=(GPU_MEMORY_LIMIT, process))
    monitor_thread.start()

    process.wait()
    monitor_thread.join()

    try:
        os.remove(audio_file)
        print(f"{audio_file} removed!")
        os.remove(f"./{uid}.wav")
        print(f"./{uid}.wav removed symbolic link!")
    except Exception as e:
        print(f"Error deleting files: {e}")

    if process.returncode != 0:
        stderr = process.stderr.read().decode()
        raise RuntimeError(f"encode_batch failed with error: {stderr}")

    embeddings = extract_embeddings_from_process(process)

    return embeddings


def extract_embeddings_from_process(process):
    stdout = process.stdout.read().decode()
    try:
        embeddings = json.loads(stdout)
    except json.JSONDecodeError as e:
        raise RuntimeError(f"Error decoding embeddings from the process output: {e}")

    return embeddings


def concate_audio_files(folder_path, uid):
    folder = Path(folder_path)
    concatenated_audio = AudioSegment.empty()
    output_folder = Path(CSV_FILE_PATH)

    mp3_files = list(folder.glob(f"{uid}_mic_*.mp3"))

    if not mp3_files:
        raise FileNotFoundError(
            f"No files found starting with {uid}_mic_ in {folder_path}"
        )

    for file_path in mp3_files:
        audio_segment = AudioSegment.from_file(file_path, format="mp3")
        concatenated_audio += audio_segment

    wav_output_file = f"{output_folder}/{uid}.wav"
    concatenated_audio.export(wav_output_file, format="wav")
    print("audio file concate done - ", folder_path, " for uid ", uid)
    return wav_output_file
