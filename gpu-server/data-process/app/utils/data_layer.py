import os
import aiohttp
from urllib.parse import urljoin
from .config_env import DATA_LAYER_URL

def merge_configs(target, source):
    if not isinstance(target, dict):
        return source
    merged = target.copy()
    for key, value in source.items():
        if key in merged and isinstance(value, dict):
            merged[key] = merge_configs(merged[key], value)
        else:
            merged[key] = value
    return merged


async def call_data_layer(route, company_id, method="GET", data=None, additional_config=None):
    if additional_config is None:
        additional_config = {}

    url = urljoin(DATA_LAYER_URL, route)
    print("Url:", url)

    base_config = {
        "headers": {
            "Content-Type": "application/json",
            "companyid": company_id,
        }
    }

    merged_config = merge_configs(base_config, additional_config)

    print("Data being sent:", data)
    print("Merged config:", merged_config)

    try:
        async with aiohttp.ClientSession() as session:
            if method == "GET":
                async with session.get(url, **merged_config) as response:
                    response.raise_for_status()
                    return await response.json()
            elif method == "POST":
                async with session.post(url, json=data, **merged_config) as response:
                    response.raise_for_status()
                    return await response.json()
            elif method == "PUT":
                async with session.put(url, json=data, **merged_config) as response:
                    response.raise_for_status()
                    return await response.json()
            elif method == "DELETE":
                async with session.delete(url, **merged_config) as response:
                    response.raise_for_status()
                    return await response.json()
            else:
                async with session.get(url, **merged_config) as response:
                    response.raise_for_status()
                    return await response.json()
    except aiohttp.ClientError as e:
        print(f"Request failed: {e}")
        return None
