import os
import h5py
import numpy as np
import mediapipe as mp
from datetime import datetime
from .detector_pool import tutorial_detector_pool
from .config_env import CSV_FILE_PATH

def process_tutorial_click(
    frames,
    room_id,
    interviewee_id,
    click_position=None,
    screen_dimensions=None,
    step_id=None,
    timestamp=None,
):
    detector_info = tutorial_detector_pool.get_detector()

    try:
        tutorial_dir = f"{CSV_FILE_PATH}/{room_id}_TUTORIAL_{interviewee_id}_tutorial"
        os.makedirs(tutorial_dir, exist_ok=True)

        h5_path = f"{tutorial_dir}/tutorial_clicks.h5"

        click_id = f"click_{step_id or 'unknown'}_{timestamp}"

        with h5py.File(h5_path, "a") as h5file:
            if "face_data" not in h5file:
                h5file.create_group("face_data")

            if "click_metadata" not in h5file:
                h5file.create_group("click_metadata")

            if click_id in h5file["click_metadata"]:
                click_id = f"{click_id}_{datetime.now().strftime('%H%M%S')}"

            click_group = h5file["click_metadata"].create_group(click_id)

            click_group.attrs["step_id"] = step_id or "unknown"
            click_group.attrs["timestamp"] = timestamp

            if click_position:
                click_group.attrs["click_x"] = click_position.get("x", 0)
                click_group.attrs["click_y"] = click_position.get("y", 0)

            if screen_dimensions:
                click_group.attrs["screen_width"] = screen_dimensions.get("width", 0)
                click_group.attrs["screen_height"] = screen_dimensions.get("height", 0)

            frames_group = click_group.create_group("frames")

            face_detection_count = 0
            blendshapes_count = 0
            transformations_count = 0

            for i, frame in enumerate(frames):
                frame_timestamp = timestamp + (i * 33)  # ~30fps
                frame_id = f"frame_{i}"

                frame_ref = frames_group.create_group(frame_id)
                frame_ref.attrs["frame_index"] = i
                frame_ref.attrs["frame_timestamp"] = frame_timestamp

                try:
                    image_payload = mp.Image(
                        image_format=mp.ImageFormat.SRGB, data=frame
                    )
                    detector = detector_info["detector"]
                    results = detector.detect_for_video(image_payload, frame_timestamp)

                    if results.face_landmarks:
                        face_detection_count += 1
                        frame_ref.attrs["face_detected"] = True

                        face_group_id = f"face_data_{frame_timestamp}"
                        face_group = h5file["face_data"].create_group(face_group_id)

                        landmarks = np.array(
                            [(lm.x, lm.y, lm.z) for lm in results.face_landmarks[0]],
                            dtype=np.float32,
                        )

                        distance = calculate_distance(
                            landmarks, click_group.attrs["screen_width"]
                        )
                        face_group.create_dataset("landmarks", data=landmarks)

                        if results.face_blendshapes:
                            blendshapes_count += 1
                            blendshapes = np.array(
                                [
                                    (bs.category_name, bs.score)
                                    for bs in results.face_blendshapes[0]
                                ],
                                dtype=np.dtype(
                                    [("category", "S50"), ("score", np.float32)]
                                ),
                            )
                            face_group.create_dataset("blendshapes", data=blendshapes)

                        if results.facial_transformation_matrixes:
                            transformations_count += 1
                            transform_matrix = np.array(
                                results.facial_transformation_matrixes[0],
                                dtype=np.float32,
                            )
                            face_group.create_dataset(
                                "transformation_matrix", data=transform_matrix
                            )

                        frame_ref.attrs["distance"] = distance
                        frame_ref.attrs["face_data_ref"] = face_group_id
                    else:
                        frame_ref.attrs["face_detected"] = False
                except Exception as frame_error:
                    print(f"Error processing frame {i}: {str(frame_error)}")
                    frame_ref.attrs["face_detected"] = False
                    frame_ref.attrs["error"] = str(frame_error)

            click_group.attrs["total_frames"] = len(frames)
            click_group.attrs["face_detection_rate"] = (
                face_detection_count / len(frames) if len(frames) > 0 else 0
            )
            click_group.attrs["blendshapes_rate"] = (
                blendshapes_count / len(frames) if len(frames) > 0 else 0
            )
            click_group.attrs["transformations_rate"] = (
                transformations_count / len(frames) if len(frames) > 0 else 0
            )

        return {
            "status": True,
            "message": "Tutorial click data processed successfully",
        }

    except Exception as e:
        print(f"Error processing tutorial click: {str(e)}")
        return {"status": False, "error": f"Error processing tutorial click"}

    finally:
        if detector_info:
            tutorial_detector_pool.release_detector(detector_info)

def calculate_distance(landmarks, screen_w):
    LEFT_IRIS_INDICES = [468, 469, 470, 471, 472]
    RIGHT_IRIS_INDICES = [473, 474, 475, 476, 477]

    left_iris = np.mean(
        [
            [landmarks[i][0], landmarks[i][1], landmarks[i][2]]
            for i in LEFT_IRIS_INDICES
        ],
        axis=0,
    )
    right_iris = np.mean(
        [
            [landmarks[i][0], landmarks[i][1], landmarks[i][2]]
            for i in RIGHT_IRIS_INDICES
        ],
        axis=0,
    )

    ipd_normalized = np.sqrt(sum((left_iris - right_iris) ** 2))
    average_ipd_mm = 63.0
    focal_length_estimate = 500.0
    distance_mm = (average_ipd_mm * focal_length_estimate) / (
        ipd_normalized * screen_w
    )
    distance_cm = distance_mm / 10.0

    return distance_cm
