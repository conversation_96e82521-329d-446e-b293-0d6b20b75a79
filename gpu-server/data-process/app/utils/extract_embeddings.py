import torch
import json
import sys
from speechbrain.inference.classifiers import EncoderClassifier

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
classifier = EncoderClassifier.from_hparams(
    source="speechbrain/spkrec-ecapa-voxceleb",
    savedir="temp",
    run_opts={"device": device},
)
classifier.device = device


def clear_cuda():
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()


if __name__ == "__main__":
    if len(sys.argv) != 2:
        sys.exit(1)

    audio_file = sys.argv[1]
    try:
        waveform = classifier.load_audio(audio_file)
        batch = waveform.unsqueeze(0)
        embeddings = classifier.encode_batch(batch)
        print(json.dumps(embeddings.tolist()))
    except Exception as e:
        sys.exit(1)
    finally:
        clear_cuda()
