import os
from dotenv import load_dotenv
from pathlib import Path


def load_environment():
    current_env = os.environ.get("NODE_ENV", "development")
    env_files = {
        "development": ".env.local",
        "staging": ".env.staging",
        "production": ".env",
    }

    env_file = env_files.get(current_env, ".env.local")
    env_path = Path(os.getcwd()) / env_file
    load_dotenv(env_path)


load_environment()

RECORDING_PATH = os.getenv("RECORD_FILE_LOCATION_PATH")
CSV_FILE_PATH = os.getenv("CSV_FILE_PATH")
AMQP_URL = os.getenv("AMQP_URL")
BG_PROCESS_QUEUE = os.getenv("BG_PROCESS_QUEUE")
PYTHON_SERVER_PROCESS_QUEUE = os.getenv("PYTHON_SERVER_PROCESS_QUEUE")
SERVER_URL = os.getenv("SERVER_URL")
STATIC_TOKEN = os.getenv("STATIC_TOKEN")
DATA_LAYER_URL = os.getenv("DATA_LAYER_URL")
AZURE_STORAGE_CONNECTION_STRING = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
AZURE_STORAGE_CONTAINER_NAME = os.getenv("AZURE_STORAGE_CONTAINER_NAME")
