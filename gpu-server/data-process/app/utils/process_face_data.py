import h5py
import numpy as np
from matplotlib.path import Path
from scipy.spatial import ConvexHull
from sklearn.cluster import DBSCAN
from scipy.stats import zscore
import os

def convert_numpy_types(obj):
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj


def load_blendshape_data(file_path):
    try:
        with h5py.File(file_path, "r") as f:
            if "face_data" not in f:
                print(f"No face_data group in {file_path}")
                return None, None

            face_data = f["face_data"]
            timestamps = []
            eye_coordinates = []

            timestamp_keys = sorted(
                [int(key) for key in face_data.keys() if key.isdigit()]
            )

            for timestamp in timestamp_keys:
                try:
                    timestamp_key = str(timestamp)
                    sample = face_data[timestamp_key]
                    if "blendshapes" not in sample:
                        continue

                    blendshapes = sample["blendshapes"][()]

                    left_eye_in = 0
                    right_eye_in = 0
                    top_eye = 0
                    bottom_eye = 0

                    for idx_val, score_val in blendshapes:
                        try:
                            idx = (
                                int(float(idx_val))
                                if not np.isnan(float(idx_val))
                                else -1
                            )
                            score = (
                                float(score_val)
                                if not np.isnan(float(score_val))
                                else 0.0
                            )

                            if idx == 13:  # EYE_LOOK_IN_LEFT
                                left_eye_in = score
                            elif idx == 14:  # EYE_LOOK_IN_RIGHT
                                right_eye_in = score
                            elif idx in [17, 18]:  # EYE_LOOK_UP_LEFT, EYE_LOOK_UP_RIGHT
                                top_eye = max(top_eye, score)
                            elif idx in [
                                11,
                                12,
                            ]:  # EYE_LOOK_DOWN_LEFT, EYE_LOOK_DOWN_RIGHT
                                bottom_eye = max(bottom_eye, score)
                        except (ValueError, TypeError) as e:
                            print(
                                f"Error processing blendshape: {idx_val}, {score_val}, {e}"
                            )
                            continue

                    x_gaze = right_eye_in - left_eye_in
                    y_gaze = top_eye - bottom_eye

                    timestamps.append(timestamp)
                    eye_coordinates.append((x_gaze, y_gaze))

                except Exception as e:
                    print(f"Error processing timestamp {timestamp_key}: {e}")
                    continue

            if not timestamps:
                print(f"No valid data found in {file_path}")
                return None, None

            return np.array(timestamps), np.array(eye_coordinates)

    except Exception as e:
        print(f"Error loading HDF5 file {file_path}: {e}")
        return None, None


def clean_and_create_polygon(points, z_threshold=4, eps=0.05, min_samples=15):
    try:
        if points is None or len(points) < min_samples:
            print(
                f"Not enough points to create polygon: {len(points) if points is not None else 0}"
            )
            return None

        x = points[:, 0]
        y = points[:, 1]

        if np.std(x) == 0 or np.std(y) == 0:
            print("Standard deviation is zero, cannot compute z-scores")
            return None

        x_scores = np.abs(zscore(x))
        y_scores = np.abs(zscore(y))

        mask = (x_scores < z_threshold) & (y_scores < z_threshold)
        clean_points = points[mask]

        if len(clean_points) < min_samples:
            print(f"Not enough points after z-score filtering: {len(clean_points)}")
            return None

        db = DBSCAN(eps=eps, min_samples=min_samples).fit(clean_points)
        labels = db.labels_

        mask = labels != -1
        clean_points = clean_points[mask]

        if len(clean_points) < 3:
            print(f"Not enough points after DBSCAN: {len(clean_points)}")
            return None

        hull = ConvexHull(clean_points)
        polygon = clean_points[hull.vertices]

        return Path(polygon)
    except Exception as e:
        print(f"Error creating polygon: {e}")
        return None


def process_gaze_attention(
    interview_file,
    tutorial_file,
    start_time,
    end_time,
    window_seconds,
    attention_threshold,
    undetectable_threshold_ms,
):
    if (start_time is None) or (end_time is None):
        print("Invalid start or end time")
        return {"inattention": [], "undetectable_face": []}

    if end_time <= start_time:
        print(
            f"Invalid time range: start_time ({start_time}) must be before end_time ({end_time})"
        )
        return {"inattention": [], "undetectable_face": []}

    print(f"Loading tutorial data from {tutorial_file}")
    tutorial_timestamps, tutorial_points = load_blendshape_data(tutorial_file)
    if tutorial_timestamps is None or len(tutorial_timestamps) == 0:
        print(f"Failed to load tutorial data from {tutorial_file}")
        return {
            "inattention": [],
            "undetectable_face": [{"start": 0, "end": int(end_time - start_time)}],
        }

    print(f"Loaded {len(tutorial_timestamps)} points from tutorial data")

    polygon_path = clean_and_create_polygon(tutorial_points)
    if polygon_path is None:
        print("Failed to create reference polygon from tutorial data")
        return {
            "inattention": [],
            "undetectable_face": [{"start": 0, "end": int(end_time - start_time)}],
        }

    print(f"Loading interview data from {interview_file}")
    interview_timestamps, interview_points = load_blendshape_data(interview_file)
    if interview_timestamps is None or len(interview_timestamps) == 0:
        print(f"Failed to load interview data from {interview_file}")
        return {
            "inattention": [],
            "undetectable_face": [{"start": 0, "end": int(end_time - start_time)}],
        }

    valid_indices = (interview_timestamps >= start_time) & (
        interview_timestamps <= end_time
    )
    interview_timestamps = interview_timestamps[valid_indices]
    interview_points = interview_points[valid_indices]

    print(
        f"Loaded {len(interview_timestamps)} points from interview data within time range"
    )

    undetectable_periods = []

    if interview_timestamps.size == 0:
        undetectable_periods.append({"start": 0, "end": int(end_time - start_time)})
        return {"inattention": [], "undetectable_face": undetectable_periods}

    if interview_timestamps[0] > start_time:
        undetectable_periods.append(
            {
                "start": 0,
                "end": max(0, int(interview_timestamps[0] - start_time)),
            }
        )

    if len(interview_timestamps) > 1:
        timestamp_diffs = np.diff(interview_timestamps)
        gap_indices = np.where(timestamp_diffs > undetectable_threshold_ms)[0]

        for idx in gap_indices:
            gap_start = interview_timestamps[idx]
            gap_end = interview_timestamps[idx + 1]
            undetectable_periods.append(
                {
                    "start": max(0, int(gap_start - start_time)),
                    "end": max(0, int(gap_end - start_time)),
                }
            )

    if interview_timestamps[-1] < end_time:
        undetectable_periods.append(
            {
                "start": max(0, int(interview_timestamps[-1] - start_time)),
                "end": max(0, int(end_time - start_time)),
            }
        )

    print(f"Found {len(undetectable_periods)} raw undetectable face periods")

    if undetectable_periods:
        undetectable_periods.sort(key=lambda x: x["start"])

        merged_periods = []
        current_period = undetectable_periods[0]

        for period in undetectable_periods[1:]:
            if period["start"] <= current_period["end"] + 2000:
                current_period["end"] = max(current_period["end"], period["end"])
            else:
                merged_periods.append(current_period)
                current_period = period

        merged_periods.append(current_period)

        undetectable_periods = merged_periods

    print(f"Found {len(undetectable_periods)} merged undetectable face periods")

    window_size_ms = window_seconds * 1000
    inattention_periods = []

    current_window_start = max(start_time, interview_timestamps[0])
    current_window_end = min(end_time, current_window_start + window_size_ms)
    inattention_start = None
    inattention_min_percent = None

    while current_window_start < end_time:
        window_mask = (interview_timestamps >= current_window_start) & (
            interview_timestamps < current_window_end
        )
        window_points = interview_points[window_mask]

        if len(window_points) == 0:
            current_window_start = current_window_end
            current_window_end = min(end_time, current_window_start + window_size_ms)
            continue

        inside_mask = polygon_path.contains_points(window_points)
        percent_inside = np.sum(inside_mask) / len(window_points)

        if percent_inside < attention_threshold:
            if inattention_start is None:
                if inattention_periods and inattention_periods[-1]["end"] == (
                    current_window_start - start_time
                ):
                    prev_period = inattention_periods.pop()
                    inattention_start = prev_period["start"] + start_time
                    inattention_min_percent = (
                        prev_period["percent_inside"] + percent_inside
                    ) / 2.0
                else:
                    inattention_start = current_window_start
                    inattention_min_percent = percent_inside
            else:
                inattention_min_percent = (
                    inattention_min_percent + percent_inside
                ) / 2.0
        else:
            if inattention_start is not None:
                inattention_periods.append(
                    {
                        "start": max(0, int(inattention_start - start_time)),
                        "end": max(0, int(current_window_end - start_time)),
                        "percent_inside": round(inattention_min_percent, 2),
                    }
                )
                inattention_start = None
                inattention_min_percent = None

        current_window_start = current_window_end
        current_window_end = min(end_time, current_window_start + window_size_ms)

    if inattention_start is not None:
        inattention_periods.append(
            {
                "start": max(0, int(inattention_start - start_time)),
                "end": max(0, int(min(current_window_end, end_time) - start_time)),
                "percent_inside": round(inattention_min_percent, 2),
            }
        )

    print(f"Found {len(inattention_periods)} inattention periods")

    inattention_periods = convert_numpy_types(inattention_periods)
    undetectable_periods = convert_numpy_types(undetectable_periods)

    return {
        "inattention": inattention_periods,
        "undetectable_face": undetectable_periods,
    }


def process_face_attention(
    file_path,
    tutorial_path,
    start_time,
    end_time,
    window_seconds,
    attention_threshold,
    undetectable_threshold_ms,
):
    print(f"Processing gaze attention for {file_path} & {tutorial_path}")

    interview_file = f"{file_path}/face_data.h5"
    tutorial_file = f"{tutorial_path}/face_data.h5"

    if not os.path.exists(interview_file):
        print(f"Interview file does not exist: {interview_file}")
        return {"inattention": [], "undetectable_face": []}

    if not os.path.exists(tutorial_file):
        print(f"Tutorial file does not exist: {tutorial_file}")
        return {"inattention": [], "undetectable_face": []}

    with h5py.File(interview_file, "r") as f:
        if "face_data" not in f or len(f["face_data"].keys()) == 0:
            print(f"No valid face data in {interview_file}")
            return {
                "inattention": [],
                "undetectable_face": [{"start": 0, "end": int(end_time - start_time)}],
            }

    result = process_gaze_attention(
        interview_file,
        tutorial_file,
        start_time,
        end_time,
        window_seconds,
        attention_threshold,
        undetectable_threshold_ms,
    )

    return convert_numpy_types(result)
