import os
import time
import asyncio
import aiofiles
from azure.storage.blob.aio import BlobServiceClient
from .config_env import AZURE_STORAGE_CONNECTION_STRING, AZURE_STORAGE_CONTAINER_NAME

async def get_container_client():
    blob_service_client = BlobServiceClient.from_connection_string(
        AZURE_STORAGE_CONNECTION_STRING
    )
    return blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)


async def upload_to_blob_storage(folder, directory_path):
    print(f"Uploading files from directory: {directory_path}")
    timestamp = int(time.time())
    try:
        container_client = await get_container_client()
        for filename in os.listdir(directory_path):
            file_path = os.path.join(directory_path, filename)
            if os.path.isfile(file_path):  
                blob_name = os.path.join(f"{folder}_{timestamp}", filename)
                blob_client = container_client.get_blob_client(blob_name)

                async with aiofiles.open(file_path, "rb") as data:
                    await blob_client.upload_blob(data, overwrite=True)
                print(f"Uploaded file successfully: {blob_client.url}")

                os.remove(file_path) 
                print(f"File removed successfully: {file_path}")

    except Exception as e:
        print(f"Error uploading files to Azure Blob Storage: {e}")
        raise e

    try:
        os.rmdir(directory_path)
        print(f"Directory removed successfully: {directory_path}")
    except Exception as e:
        print(f"Error removing directory: {e}")
        raise e
