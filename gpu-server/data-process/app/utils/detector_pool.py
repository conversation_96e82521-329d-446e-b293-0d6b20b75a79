import threading
from queue import Queue
import mediapipe as mp
import os

is_local = os.environ.get("NODE_ENV", "development") == "development"

NUM_VIDEO_DETECTORS = 6
DETECTOR_QUEUE_MAX_SIZE = NUM_VIDEO_DETECTORS * 2
NUM_CPU_DETECTORS_CAM_TEST = is_local and 1 or 10
NUM_CPU_DETECTORS_TUTORIAL = is_local and 1 or 10

def process_frame_batch(frames, detector):
    results = []
    for idx, (frame, timestamp) in enumerate(frames):
        try:
            image_payload = mp.Image(image_format=mp.ImageFormat.SRGB, data=frame)
            detection_result = detector.detect_for_video(image_payload, timestamp)
            if (
                detection_result
                and detection_result.face_blendshapes
                and detection_result.face_landmarks
                and detection_result.facial_transformation_matrixes
            ):
                results.append((detection_result, timestamp))
        except Exception as e:
            continue
    return results


class CPUDetectorPool:
    def __init__(
        self, pool_size=2, output_blendshapes=False, output_transformations=False
    ):
        self.pool_size = pool_size
        self.detector_queue = Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        self.output_blendshapes = output_blendshapes
        self.output_transformations = output_transformations
        self.initialize_pool()

    def initialize_pool(self):
        for i in range(self.pool_size):
            detector = self._create_detector()
            self.detector_queue.put(
                {
                    "detector": detector,
                    "last_timestamp": i * 1000000,
                    "in_use": False,
                    "id": i,
                }
            )
        print(f"Initialized detector pool with {self.pool_size} detectors")

    def _create_detector(self):
        BaseOptions = mp.tasks.BaseOptions
        FaceLandmarker = mp.tasks.vision.FaceLandmarker
        FaceLandmarkerOptions = mp.tasks.vision.FaceLandmarkerOptions

        base_options = BaseOptions(
            model_asset_path="./app/model/face_landmarker.task",
            delegate=mp.tasks.BaseOptions.Delegate.CPU,
        )

        options = FaceLandmarkerOptions(
            base_options=base_options,
            min_face_detection_confidence=0.85,
            min_tracking_confidence=0.9,
            min_face_presence_confidence=0.9,
            num_faces=1,
            output_face_blendshapes=self.output_blendshapes,
            output_facial_transformation_matrixes=self.output_transformations,
            running_mode=mp.tasks.vision.RunningMode.VIDEO,
        )

        return FaceLandmarker.create_from_options(options)

    def get_detector(self):
        with self.lock:
            detector_info = self.detector_queue.get()
            detector_info["in_use"] = True
            return detector_info

    def release_detector(self, detector_info):
        with self.lock:
            detector_info["in_use"] = False
            self.detector_queue.put(detector_info)


class GlobalDetectorThread(threading.Thread):
    def __init__(self, detector_id, job_queue):
        threading.Thread.__init__(self)
        self.detector_id = detector_id
        self.job_queue = job_queue
        self.detector = self.create_detector()
        self.daemon = True

    def create_detector(self):
        BaseOptions = mp.tasks.BaseOptions
        FaceLandmarker = mp.tasks.vision.FaceLandmarker
        FaceLandmarkerOptions = mp.tasks.vision.FaceLandmarkerOptions
        base_options = BaseOptions(
            model_asset_path="./app/model/face_landmarker.task",
            delegate=mp.tasks.BaseOptions.Delegate.CPU,
        )
        options = FaceLandmarkerOptions(
            base_options=base_options,
            output_face_blendshapes=True,
            output_facial_transformation_matrixes=True,
            # min_face_detection_confidence=0.85,
            min_tracking_confidence=0.9,
            # min_face_presence_confidence=0.9,
            num_faces=1,
            running_mode=mp.tasks.vision.RunningMode.VIDEO,
        )
        return FaceLandmarker.create_from_options(options)

    def run(self):
        while True:
            job = self.job_queue.get()
            if job is None:
                break
            frames_batch, out_queue, done_event = job
            try:
                results = process_frame_batch(frames_batch, self.detector)
                for result in results:
                    out_queue.put(result)
            except Exception as e:
                print(f"Error in detector thread {self.detector_id}: {str(e)}")
            done_event.set()
            self.job_queue.task_done()
        if self.detector:
            self.detector.close()


class DetectorPool:
    def __init__(self, num_detectors):
        self.queue = Queue(maxsize=DETECTOR_QUEUE_MAX_SIZE)
        self.threads = []
        for i in range(num_detectors):
            thread = GlobalDetectorThread(i, self.queue)
            thread.start()
            self.threads.append(thread)

    def enqueue(self, job):
        self.queue.put(job, block=True)

    def shutdown(self):
        for _ in self.threads:
            self.queue.put(None)
        for thread in self.threads:
            thread.join()


cpu_detector_pool = CPUDetectorPool(
    pool_size=NUM_CPU_DETECTORS_CAM_TEST,
    output_blendshapes=False,
    output_transformations=False,
)
tutorial_detector_pool = CPUDetectorPool(
    pool_size=NUM_CPU_DETECTORS_TUTORIAL,
    output_blendshapes=True,
    output_transformations=True,
)
