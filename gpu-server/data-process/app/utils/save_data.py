import os
import csv
import time
import numpy as np
import h5py
from datetime import datetime
from .config_env import CSV_FILE_PATH

def save_face_data_to_hdf5(hdf5_file, detection_result, timestamp, is_cheating=0):
    try:
        # Separate dataset options for different data shapes
        landmark_opts = {
            "chunks": (478, 3),  # For face mesh landmarks
            "shuffle": False,
            "fletcher32": False,
            "compression": None,
            "track_times": False,
        }

        transform_opts = {
            "chunks": (4, 4),  # For transformation matrix
            "shuffle": False,
            "fletcher32": False,
            "compression": None,
            "track_times": False,
        }

        blendshape_opts = {
            "chunks": (146, 2),  # For blendshapes
            "shuffle": False,
            "fletcher32": False,
            "compression": None,
            "track_times": False,
        }

        landmarks = np.empty((478, 3), dtype=np.float32)
        transform_matrix = np.empty((4, 4), dtype=np.float32)
        blendshapes = np.empty((146, 2), dtype=np.float32)

        for i, landmark in enumerate(detection_result.face_landmarks[0]):
            landmarks[i] = [landmark.x, landmark.y, landmark.z]

        np.copyto(
            transform_matrix,
            np.asarray(
                detection_result.facial_transformation_matrixes[0], dtype=np.float32
            ),
        )

        for i, blendshape in enumerate(detection_result.face_blendshapes[0]):
            blendshapes[i] = [i, blendshape.score]

        face_group = hdf5_file["face_data"]
        sample_group = face_group.create_group(str(timestamp))

        sample_group.create_dataset("landmarks", data=landmarks, **landmark_opts)
        sample_group.create_dataset(
            "transform_matrix", data=transform_matrix, **transform_opts
        )
        sample_group.create_dataset("blendshapes", data=blendshapes, **blendshape_opts)

        attrs = sample_group.attrs
        attrs.create("timestamp", timestamp, dtype=np.int64)
        attrs.create("is_cheating", is_cheating, dtype=np.bool_)

        # Reduce flush frequency
        if sample_group.id.id % 500 == 0:
            hdf5_file.flush()

        return True

    except Exception as e:
        print(f"[Warning] Failed to save HDF5 data: {str(e)}")
        return False


def save_metrics_to_csv(room_id, interviewee_id, metrics):
    directory = f"{CSV_FILE_PATH}/{room_id}_{interviewee_id}"
    os.makedirs(directory, exist_ok=True)

    file_path = f"{directory}/brightness.csv"
    file_exists = os.path.isfile(file_path)

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    with open(file_path, "a", newline="") as csvfile:
        fieldnames = [
            "timestamp",
            "face_detection_rate",
            "face_brightness",
            "left_eye_brightness",
            "right_eye_brightness",
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        if not file_exists:
            writer.writeheader()
        writer.writerow(
            {
                "timestamp": timestamp,
                "face_detection_rate": metrics["face_detection_rate"],
                "face_brightness": metrics["face_brightness"],
                "left_eye_brightness": metrics["left_eye_brightness"],
                "right_eye_brightness": metrics["right_eye_brightness"],
            }
        )
