import os
import time
import cv2
import asyncio
from . import save_data
from pathlib import Path
import h5py
from .detector_pool import DetectorPool, NUM_VIDEO_DETECTORS
import threading
from queue import Queue, Empty
import mediapipe as mp
import gc
from .config_env import RECORDING_PATH, CSV_FILE_PATH

BATCH_SIZE = 200
TARGET_FPS = 30
FLUSH_BUFFER_SIZE = 1000


def flush_output_worker(hdf5_file, output_queue):
    buffer = []
    while True:
        try:
            result = output_queue.get(timeout=1)
            if result is None:
                break
            buffer.append(result)
            if len(buffer) >= FLUSH_BUFFER_SIZE:
                for detection_result, ts in buffer:
                    save_data.save_face_data_to_hdf5(hdf5_file, detection_result, ts)
                buffer.clear()
        except Empty:
            continue
    for detection_result, ts in buffer:
        save_data.save_face_data_to_hdf5(hdf5_file, detection_result, ts)


def process_video_internal(video_file, create_time, hdf5_file, detector_pool):
    output_queue = Queue()
    flush_thread = threading.Thread(
        target=flush_output_worker, args=(hdf5_file, output_queue)
    )
    flush_thread.start()

    cap = cv2.VideoCapture(video_file)
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1000)
    if not cap.isOpened():
        print(f"Error: Could not open video file: {video_file}")
        output_queue.put(None)
        flush_thread.join()
        return True
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    original_fps = cap.get(cv2.CAP_PROP_FPS)
    frame_interval = max(1, round(original_fps / TARGET_FPS))
    total_frames_to_process = total_frames // frame_interval

    frames_batch = []
    frame_count = 0
    frames_processed = 0
    last_progress_update = 0
    print(
        f"\n\n~~~~~~~~~~~~~~~~~~~~~~\nProcessing video: {os.path.basename(video_file)}"
    )
    print(f"Total frames: {total_frames}, Original FPS: {original_fps:.2f}")
    print(
        f"Frame interval: {frame_interval}, Expected processed frames: {total_frames_to_process}"
    )
    start_time = time.time()

    job_events = []

    next_progress = 20
    try:
        while frame_count < total_frames:
            ret, frame = cap.read()
            if not ret:
                break
            frame_count += 1

            if (frame_count - 1) % frame_interval == 0:
                frames_processed += 1
                elapsed_ms = int(cap.get(cv2.CAP_PROP_POS_MSEC))
                frame_timestamp = create_time + elapsed_ms

                frames_batch.append((frame, frame_timestamp))

                if len(frames_batch) >= BATCH_SIZE:
                    done_event = threading.Event()
                    job_events.append(done_event)
                    detector_pool.enqueue((frames_batch, output_queue, done_event))
                    frames_batch = []

                    if len(job_events) >= NUM_VIDEO_DETECTORS:
                        job_events.pop(0).wait()

                current_progress = int(
                    (frames_processed / total_frames_to_process) * 100
                )
                elapsed_time = time.time() - start_time
                while elapsed_time >= 1 and current_progress >= next_progress:
                    fps = frames_processed / elapsed_time
                    remaining = total_frames_to_process - frames_processed
                    eta = remaining / fps if fps > 0 else 0
                    print(
                        f"File: {os.path.basename(video_file)} | Progress: {next_progress}% | "
                        f"Processed: {frames_processed}/{total_frames_to_process} | "
                        f"Speed: {fps:.1f} fps | ETA: {eta/60:.2f} min"
                    )
                    next_progress += 20
            if frame_count % 10000 == 0:
                gc.collect()
    finally:
        gc.collect()

    if frames_batch:
        print(f"Submitting final batch of {len(frames_batch)} frames for processing")
        done_event = threading.Event()
        job_events.append(done_event)
        detector_pool.enqueue((frames_batch, output_queue, done_event))

    for event in job_events:
        event.wait()

    output_queue.put(None)
    flush_thread.join()

    elapsed_ms = int(cap.get(cv2.CAP_PROP_POS_MSEC))
    total_time = time.time() - start_time
    print(f"\nCompleted processing {os.path.basename(video_file)}")
    print(f"Total time: {total_time/60:.1f} minutes")
    print(f"Average speed: {frame_count/total_time:.1f} fps\n")

    cap.release()
    return True


def process_all_cameras(file_dir, roomId, uid):
    folders_to_process = [
        (
            Path(f"{RECORDING_PATH}/{roomId}_TUTORIAL"),
            f"{CSV_FILE_PATH}/{roomId}_TUTORIAL_{uid}_tutorial",
        ),
        (Path(f"{RECORDING_PATH}/{file_dir}"), f"{CSV_FILE_PATH}/{roomId}_{uid}"),
    ]
    try:
        for folder, save_folder in folders_to_process:
            hdf5_file = None
            try:
                camera_files = list(folder.glob(f"{uid}_camera_*.mp4"))
                if not camera_files:
                    print(
                        f"No camera files found in {folder} for pattern {uid}_camera_*.mp4"
                    )
                    continue

                sorted_files = sorted(
                    camera_files, key=lambda x: int(str(x).split("_")[-1].split(".")[0])
                )

                os.makedirs(save_folder, exist_ok=True)
                hdf5_path = os.path.join(save_folder, "face_data.h5")
                hdf5_file = h5py.File(
                    hdf5_path,
                    "w",
                    libver="latest",
                    rdcc_nbytes=32 * 1024 * 1024,  # chunk cache
                    rdcc_w0=0.75,  # chunk cache writeback ratio
                    track_order=False,  # Disable track_order for better performance
                )
                if "face_data" not in hdf5_file:
                    hdf5_file.create_group("face_data")

                for file_path in sorted_files:
                    create_time = int((str(file_path).split("_")[-1].split(".")[0]))
                    print(
                        f"Processing camera file: {file_path} and create_time: {create_time}"
                    )
                    detector_pool = DetectorPool(NUM_VIDEO_DETECTORS)
                    delta_time = process_video_internal(
                        file_path, create_time, hdf5_file, detector_pool
                    )
                    detector_pool.shutdown()

                if hdf5_file is not None:
                    try:
                        hdf5_file.flush()  # Force write any remaining data
                        hdf5_file.close()  # Close the file
                    except Exception as e:
                        print(f"Error closing HDF5 file: {str(e)}")
            except Exception as e:
                print(f"Error processing camera files: {str(e)}")
    except Exception as e:
        print(f"Error processing all cameras: {str(e)}")
