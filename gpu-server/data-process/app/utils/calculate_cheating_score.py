def cheating_score(timeline, start_time, end_time):
    weights = {
        "tabFocus": 6,
        "inactivity": 3,
        # "gaze": 1,
        #  "undetectableFace": 1
    }

    total_timeline_sum = 0
    for key, weight in weights.items():
        durations = [data["end"] - data["start"] for data in timeline.get(key, [])]
        total_timeline_sum += sum(durations) * weight

    if start_time is not None and end_time is not None:
        total_time = end_time - start_time
        if total_time > 0:
            score = int((total_timeline_sum * 100) / (total_time * 9))
            return score

    return 0
