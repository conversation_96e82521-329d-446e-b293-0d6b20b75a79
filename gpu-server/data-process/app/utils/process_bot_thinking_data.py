import os
import time
import csv
import os


def process_bot_thinking(file_path, room_start_time):
    print(f"Processing bot thinking data from: {file_path}")

    result = []
    if not os.path.isfile(file_path):
        print(f"File not found: {file_path}")
        return result

    filename = os.path.basename(file_path)

    try:
        if filename.endswith(".csv"):
            with open(file_path, "r") as csvfile:
                dialect = csv.Sniffer().sniff(csvfile.read(2048))
                csvfile.seek(0)
                reader = csv.DictReader(csvfile, dialect=dialect)

                reader.fieldnames = [
                    field.strip().replace(" ", "_") for field in reader.fieldnames
                ]

                start_time = None
                for row in reader:
                    event_type = row["type"].strip()
                    timestamp = int(row["timestamp"])
                    if event_type == "start_time":
                        start_time = timestamp - room_start_time

                    elif start_time is not None:
                        end_time = timestamp - room_start_time
                        if event_type == "end_time":
                            if start_time < end_time:
                                result.append(
                                    {
                                        "start": max(0, start_time),
                                        "end": end_time,
                                    }
                                )
                            start_time = None
                print(f"Processed file successfully: {file_path}")
        else:
            print("Invalid file type. Expected CSV file.")
            return result

        return result

    except Exception as e:
        print(f"Error processing bot thinking data files: {e}")
        return result


def fix_timeline(timeline):
    bot_thinking = timeline["botThinking"]

    fixed_inactivity = remove_overlaps(timeline["inactivity"], bot_thinking)
    fixed_gaze = remove_overlaps(timeline["gaze"], bot_thinking)
    fixed_undetectable_face = remove_overlaps(
        timeline["undetectableFace"], bot_thinking
    )
    fixed_bad_network = remove_overlaps(timeline.get("badNetwork", []), bot_thinking)

    fixed_timeline = {
        "question": timeline["question"],
        "inactivity": fixed_inactivity,
        "gaze": fixed_gaze,
        "undetectableFace": fixed_undetectable_face,
        "tabFocus": timeline["tabFocus"],
        "botThinking": bot_thinking,
        "badNetwork": fixed_bad_network,
    }

    return fixed_timeline

def remove_overlaps(timeline_data, bot_thinking):
    if not timeline_data or not bot_thinking:
        return timeline_data
    
    result = []
    
    for period in timeline_data:
        period_start = period["start"]
        period_end = period["end"]
        
        overlapping_segments = []
        for bot_period in bot_thinking:
            bot_start = bot_period["start"]
            bot_end = bot_period["end"]
            
            if period_end <= bot_start or period_start >= bot_end:
                continue
            
            overlap_start = max(period_start, bot_start)
            overlap_end = min(period_end, bot_end)
            overlapping_segments.append((overlap_start, overlap_end))
        
        if not overlapping_segments:
            result.append(period)
            continue
        
        overlapping_segments.sort()
        
        merged_segments = []
        current_segment = overlapping_segments[0]
        
        for segment in overlapping_segments[1:]:
            if segment[0] <= current_segment[1]:
                current_segment = (current_segment[0], max(current_segment[1], segment[1]))
            else:
                merged_segments.append(current_segment)
                current_segment = segment
        
        merged_segments.append(current_segment)
        
        current_start = period_start
        
        for overlap_start, overlap_end in merged_segments:
            if current_start < overlap_start:
                result.append({"start": current_start, "end": overlap_start})
            
            current_start = overlap_end
        
        if current_start < period_end:
            result.append({"start": current_start, "end": period_end})
    
    return result
