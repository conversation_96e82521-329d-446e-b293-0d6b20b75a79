def calculate_network_score(file_path):
    import pandas as pd

    try:
        with open(file_path, 'r') as f:
            header = f.readline().strip()
            column_names = header.split(',')
        columns_to_use = column_names[:22] if len(column_names) >= 22 else column_names
        df = pd.read_csv(file_path, usecols=columns_to_use)

        if 'FractionLost' not in df.columns:
            if len(df.columns) == 22:
                standard_columns = ['ProducerIndex', 'Timestamp', 'SSRC', 'RTX_SSRC', 'Kind', 
                                    'MimeType', 'PacketsLost', 'FractionLost', 'PacketsDiscarded', 
                                    'PacketsRetransmitted', 'PacketsRepaired', 'NackCount', 
                                    'NackPacketCount', 'PliCount', 'FirCount', 'Score', 
                                    'RoundTripTime', 'Type', 'Jitter', 'ByteCount', 
                                    'PacketCount', 'Bitrate']
                rename_dict = {df.columns[i]: standard_columns[i] for i in range(len(df.columns))}
                df = df.rename(columns=rename_dict)

        if 'FractionLost' not in df.columns:
            print("Missing 'FractionLost' column.")
            return None

        df['FractionLost'] = pd.to_numeric(df['FractionLost'], errors='coerce')

        avg_fraction_lost = df['FractionLost'].mean()
        std_fraction_lost = df['FractionLost'].std()

        score = 10.0

        if avg_fraction_lost > 0:
            loss_penalty = min(7.0, (avg_fraction_lost / 70) ** 1.5 * 7.0)
            score -= loss_penalty

        if std_fraction_lost > 0:
            var_penalty = min(3.0, (std_fraction_lost / 20) ** 2.0 * 3.0)
            score -= var_penalty

        score = max(1.0, min(10.0, score))
        score = round(score, 1)

        return score

    except Exception as e:
        print(f"Error calculating network score: {e}")
        return None
