import struct
import librosa
import webrtcvad
import numpy as np
from pydub import AudioSegment
from typing import Optional, Union
from pathlib import Path
from scipy.ndimage.morphology import binary_dilation

sampling_rate = 16000
audio_norm_target_dBFS = -30

## Voice Activation Detection
# Window size of the VAD. Must be either 10, 20 or 30 milliseconds.
# This sets the granularity of the VAD. Should not need to be changed.
vad_window_length = 30  # In milliseconds
# Number of frames to average together when performing the moving average smoothing.
# The larger this value, the larger the VAD variations must be to not get smoothed out.
vad_moving_average_width = 8
# Maximum number of consecutive silent frames a segment can have.
vad_max_silence_length = 6

int16_max = (2**15) - 1


def preprocess_wav(
    fpath_or_wav: Union[str, Path, np.ndarray], source_sr: Optional[int] = None
):
    if isinstance(fpath_or_wav, str) or isinstance(fpath_or_wav, Path):
        wav, source_sr = librosa.load(str(fpath_or_wav), sr=None)
        initial_duration = librosa.get_duration(y=wav, sr=source_sr)
    else:
        wav = fpath_or_wav
        initial_duration = len(wav) / sampling_rate

    if source_sr is not None:
        wav = librosa.resample(wav, orig_sr=source_sr, target_sr=sampling_rate)

    wav = normalize_volume(wav, audio_norm_target_dBFS, increase_only=True)
    wav = trim_long_silences(wav)
    final_duration = len(wav) / sampling_rate
    save_preprocessed_wav(wav, fpath_or_wav)
    print(f"Initial audio length: {initial_duration:.2f} seconds")
    print(f"Processed audio length: {final_duration:.2f} seconds")


def save_preprocessed_wav(wav: np.ndarray, filename: str, sample_rate: int = 16000):
    wav = (wav * int16_max).astype(np.int16)

    audio_segment = AudioSegment(
        wav.tobytes(),
        frame_rate=sample_rate,
        sample_width=wav.dtype.itemsize,
        channels=1,
    )

    audio_segment.export(filename, format="wav")


def trim_long_silences(wav):
    samples_per_window = (vad_window_length * sampling_rate) // 1000

    wav = wav[: len(wav) - (len(wav) % samples_per_window)]

    pcm_wave = struct.pack(
        "%dh" % len(wav), *(np.round(wav * int16_max)).astype(np.int16)
    )

    voice_flags = []
    vad = webrtcvad.Vad(mode=3)
    for window_start in range(0, len(wav), samples_per_window):
        window_end = window_start + samples_per_window
        voice_flags.append(
            vad.is_speech(
                pcm_wave[window_start * 2 : window_end * 2], sample_rate=sampling_rate
            )
        )
    voice_flags = np.array(voice_flags)

    def moving_average(array, width):
        array_padded = np.concatenate(
            (np.zeros((width - 1) // 2), array, np.zeros(width // 2))
        )
        ret = np.cumsum(array_padded, dtype=float)
        ret[width:] = ret[width:] - ret[:-width]
        return ret[width - 1 :] / width

    audio_mask = moving_average(voice_flags, vad_moving_average_width)
    audio_mask = np.round(audio_mask).astype(bool)

    audio_mask = binary_dilation(audio_mask, np.ones(vad_max_silence_length + 1))
    audio_mask = np.repeat(audio_mask, samples_per_window)

    return wav[audio_mask == True]


def normalize_volume(wav, target_dBFS, increase_only=False, decrease_only=False):
    if increase_only and decrease_only:
        raise ValueError("Both increase only and decrease only are set")
    rms = np.sqrt(np.mean((wav * int16_max) ** 2))
    wave_dBFS = 20 * np.log10(rms / int16_max)
    dBFS_change = target_dBFS - wave_dBFS
    if dBFS_change < 0 and increase_only or dBFS_change > 0 and decrease_only:
        return wav
    return wav * (10 ** (dBFS_change / 20))
