import os
import json
import httpx
import asyncio
import aio_pika
from typing import Any, Dict
from .utils import (
    process_voice,
    upload_to_blob_storage,
    process_input_data,
    data_layer,
    find_folder,
    process_video,
    process_face_data,
    process_tab_focus_data,
    process_bot_thinking_data,
    calculate_cheating_score,
    process_bad_network_data,
    calculate_network_score,
)
from .utils.config_env import (
    CSV_FILE_PATH,
    BG_PROCESS_QUEUE,
    AMQP_URL,
    PYTHON_SERVER_PROCESS_QUEUE,
    SERVER_URL,
    STATIC_TOKEN,
)
import gc
import multiprocessing

connection = None
channel = None

def run_video_processing(file_dir, roomId, uid):
    try:
        process_video.process_all_cameras(file_dir, roomId, uid)
    except Exception as e:
        print(f"Error processing video: {str(e)}")


async def connect(url: str = None) -> None:
    global connection, channel
    try:
        connection = await aio_pika.connect_robust(url or AMQP_URL, heartbeat=60)
        channel = await connection.channel()
        print("Connected to RabbitMQ")
    except Exception as e:
        print(f"Error connecting to RabbitMQ: {e}")


async def push_to_queue(queue_name: str, message: Dict[str, Any]) -> None:
    global connection, channel
    if not connection or connection.is_closed:
        await connect()

    try:
        queue = await channel.declare_queue(queue_name, durable=True)

        await channel.default_exchange.publish(
            aio_pika.Message(
                body=json.dumps(message).encode("utf-8"),
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
            ),
            routing_key=queue_name,
        )
        print(f"Message sent to {queue_name}:", message)
    except Exception as e:
        print(f"Error sending message to queue {queue_name}:", message, e)


async def push_to_delay_queue(
    queue_name: str, message: Dict[str, Any], delay: int = 0
) -> None:
    global connection, channel
    if not connection or connection.is_closed:
        await connect()

    try:
        exchange = await channel.declare_exchange(
            "delayed_exchange",
            type="x-delayed-message",
            durable=True,
            arguments={"x-delayed-type": "direct"},
        )

        queue = await channel.declare_queue(queue_name, durable=True)
        await queue.bind(exchange, queue_name)

        await exchange.publish(
            aio_pika.Message(
                body=json.dumps(message).encode("utf-8"),
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                headers={"x-delay": delay},
            ),
            routing_key=queue_name,
        )
        print(f"Message sent to {queue_name} with delay of {delay} ms:", message)
    except Exception as e:
        print(f"Error sending message to queue {queue_name}:", message, e)


async def consume_queue(queue_name: str, message_processor) -> None:
    global connection, channel
    if not connection or connection.is_closed:
        await connect()

    try:
        await channel.set_qos(prefetch_count=1)
        queue = await channel.declare_queue(
            queue_name,
            durable=True,
        )

        print(f"Started consuming messages from {queue_name}")
        await queue.consume(message_processor)
    except Exception as e:
        print(f"Error setting up consumer for {queue_name}:", e)


async def consume_python_server_process_queue() -> None:
    await consume_queue(PYTHON_SERVER_PROCESS_QUEUE, python_server_process)


async def python_server_process(message: aio_pika.IncomingMessage):
    async with message.process():
        try:
            body = message.body.decode()
            data = json.loads(body)
            print("Received message python_server_process():", data)

            file_dir = data["recordingDir"]
            uid = data["intervieweeId"]
            companyId = data["companyId"]
            roomId = file_dir.split("_")[0]

            await push_to_queue(
                BG_PROCESS_QUEUE,
                {"recordingDir": str(file_dir), "companyId": companyId},
            )

            try:
                p = multiprocessing.Process(
                    target=run_video_processing,
                    args=(file_dir, roomId, uid),
                )
                p.daemon = True
                p.start()
                await asyncio.to_thread(p.join)
                await asyncio.to_thread(gc.collect)
            except Exception as e:
                print(f"Error processing video: {str(e)}")

            print("Video processing completed")

            try:
                await upload_files_to_azure(roomId, companyId)
            except Exception as e:
                print(f"Error uploading files to azure: {str(e)}")

            # await process_voice.process_voice_data(uid, file_dir)

        except json.JSONDecodeError:
            print("Error decoding JSON message:", body)
        except KeyError as e:
            print(f"Missing key in message: {e}")
        except Exception as e:
            print(f"Error processing message: {e}")

async def upload_files_to_azure(roomId, companyId):
    try:
        print("upload_files_to_azure():", roomId, companyId)
        folder = find_folder.find_folder_with_roomId(roomId, False)
        tutorial_folder = find_folder.find_folder_with_roomId(roomId, True)

        file_path = f"{CSV_FILE_PATH}/{folder}"
        tutorial_path = f"{CSV_FILE_PATH}/{tutorial_folder}"

        intervieweeId = None
        inactive_timeline = {
            "question": [],
            "inactivity": [],
            "start_time": None,
            "end_time": None,
        }
        gaze_timeline = {"inattention": []}
        tab_focus_data = []
        bot_thinking_data = []
        bad_network_alerts = []

        if folder:
            intervieweeId = folder.split("_")[1]
        else:
            intervieweeId = tutorial_folder.split("_")[2]

        if folder:
            try:
                input_count_file_path = f"{file_path}/input_count.csv"
                inactive_timeline = process_input_data.process_timeline(
                    file_path=input_count_file_path
                )
            except Exception as e:
                inactive_timeline = {
                    "question": [],
                    "inactivity": [],
                    "start_time": None,
                    "end_time": None,
                }
                print(f"Error processing input count data: {str(e)}")

            try:
                tab_focus_file_path = f"{file_path}/tab_focus.csv"
                tab_focus_data = process_tab_focus_data.process_tab_focus(
                    tab_focus_file_path
                )
            except Exception as e:
                tab_focus_data = []
                print(f"Error processing tab focus data: {str(e)}")

            try:
                bot_thinking_file_path = f"{file_path}/bot_thinking.csv"
                bot_thinking_data = process_bot_thinking_data.process_bot_thinking(
                    bot_thinking_file_path,
                    room_start_time=inactive_timeline["start_time"],
                )
            except Exception as e:
                bot_thinking_data = []
                print(f"Error processing bot thinking data: {str(e)}")

            try:
                bad_network_file_path = f"{file_path}/bad_network_intervals.csv"
                bad_network_alerts = process_bad_network_data.process_bad_network_data(bad_network_file_path)
            except Exception as e:
                bad_network_alerts = []
                print(f"Error processing bad network data: {str(e)}")

        try:
            gaze_timeline = process_face_data.process_face_attention(
                file_path,
                tutorial_path,
                start_time=inactive_timeline["start_time"],
                end_time=inactive_timeline["end_time"],
                window_seconds=30,
                attention_threshold=0.75,
                undetectable_threshold_ms=1000,
            )
        except Exception as e:
            gaze_timeline = {
                "inattention": [],
                "undetectable_face": [],
            }
            print(f"Error processing gaze data: {str(e)}")

        timeline = {
            "question": inactive_timeline["question"],
            "inactivity": inactive_timeline["inactivity"],
            "gaze": gaze_timeline["inattention"],
            "undetectableFace": gaze_timeline["undetectable_face"],
            "tabFocus": tab_focus_data,
            "botThinking": bot_thinking_data,
            "badNetwork": bad_network_alerts,  
        }
        print("Before fixing timeline:", timeline)

        try:
            timeline = process_bot_thinking_data.fix_timeline(timeline)
        except Exception as e:
            print(f"Error fixing timeline: {str(e)}")

        print("After fixing timeline:", timeline)

        try:
            producer_stats_file_path = f"{file_path}/producer_stats.csv"
            network_score = calculate_network_score.calculate_network_score(producer_stats_file_path)

            if network_score is not None:
                await data_layer.call_data_layer(
                    "/addNetworkScore",
                    company_id=companyId,
                    method="POST",
                    data={"roomId": roomId, "networkScore": network_score},
                )
        except Exception as e:
            print(f"Error processing network score: {str(e)}")

        cheatingScore = 0

        try:
            cheatingScore = calculate_cheating_score.cheating_score(
                timeline,
                inactive_timeline["start_time"],
                inactive_timeline["end_time"],
            )
        except Exception as e:
            print(f"Error calculating cheating score: {str(e)}")

        timeline["cheating_score"] = cheatingScore

        try:
            await data_layer.call_data_layer(
                "/updateInterviewTimeline",
                company_id=companyId,
                method="POST",
                data={"roomId": roomId, "timeline": timeline},
            )
        except Exception as e:
            print(f"Error updateInterviewTimeline(): {str(e)}")

        try:
            await upload_to_blob_storage(f"{roomId}_{intervieweeId}", file_path)
        except Exception as e:
            print(f"Error uploading interview file: {str(e)}")

        if tutorial_folder:
            try:
                await upload_to_blob_storage(
                    f"{roomId}_{intervieweeId}_tutorial", tutorial_path
                )
            except Exception as e:
                print(f"Error uploading tutorial file: {str(e)}")

    except json.JSONDecodeError:
        print("Error decoding JSON message:", body)
    except KeyError as e:
        print(f"Missing key in message: {e}")
    except Exception as e:
        print(f"Error processing message: {str(e)}")


async def get_creation_time_data(room_id, company_id) -> None:
    url = f"{SERVER_URL}/internal/get-create-time"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {STATIC_TOKEN}",
    }

    params = {"roomId": room_id, "companyId": company_id}

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers, params=params)
            data = response.json()
            print(f"get-create-time:{room_id} {company_id}: {data}")

            if data.get("time"):
                return data
            return False

        except httpx.HTTPError as e:
            print(f"HTTP error occurred: {e}")
            return False
        except Exception as e:
            print(f"Error fetching creation time: {e}")
            return False


async def close() -> None:
    global connection
    if connection and not connection.is_closed:
        await connection.close()
        print("Disconnected from RabbitMQ")
