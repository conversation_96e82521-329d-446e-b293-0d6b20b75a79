from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
from fastapi import Request
import os
from ..utils.config_env import STATIC_TOKEN

class InternalRouteMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.url.path.startswith("/internal/"):
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header[7:]
            else:
                token = None

            if token != STATIC_TOKEN:
                return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

        response = await call_next(request)
        return response
