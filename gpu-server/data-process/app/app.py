import asyncio
import warnings
import os
from pathlib import Path
from . import rbmq
from fastapi import FastAPI, File, UploadFile, Form
from fastapi.responses import JSONResponse
from typing import List, Optional
import datetime
from .utils.camera_test import visibility_test
from .middleware.internalMiddleware import InternalRouteMiddleware
import base64
import cv2
import json
import numpy as np
import threading
import functools
from concurrent.futures import ThreadPoolExecutor
from .utils.tutorial_click import process_tutorial_click

thread_pool = ThreadPoolExecutor(max_workers=4)

warnings.filterwarnings(
    "ignore", category=UserWarning, module="google.protobuf.symbol_database"
)

app = FastAPI()

app.add_middleware(InternalRouteMiddleware)


def run_consumer_in_thread():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(rbmq.connect())
        loop.create_task(rbmq.consume_python_server_process_queue())
        loop.run_forever()
    except Exception as e:
        print(f"Error in consumer thread: {e}")
    finally:
        if not loop.is_closed():
            pending = asyncio.all_tasks(loop)
            if pending:
                loop.run_until_complete(
                    asyncio.gather(*pending, return_exceptions=True)
                )
            loop.close()
        print("Consumer thread loop closed")


@app.on_event("startup")
async def startup_event():
    try:
        thread = threading.Thread(target=run_consumer_in_thread, daemon=True)
        thread.start()
    except Exception as e:
        print("Error starting up:", e)

@app.on_event("shutdown")
async def shutdown_event():
    try:
        await rbmq.close()
        thread_pool.shutdown(wait=False)
    except Exception as e:
        print(f"Error closing RBMQ connection: {e}")

    print("Server shutdown cleanup completed")


@app.get("/")
async def read_root():
    return JSONResponse(
        content={"message": "Data Process Server Running!"}, status_code=200
    )


@app.post("/internal/test-camera/{roomId}/{intervieweeId}")
async def test_camera(
    roomId: str,
    intervieweeId: str,
    files: List[UploadFile] = File(...),
    createCSV: Optional[bool] = Form(None),
):
    try:
        video_frames = []
        for file in files:
            img_bytes = await file.read()
            img_np = np.frombuffer(img_bytes, dtype=np.uint8)
            frame = cv2.imdecode(img_np, cv2.IMREAD_COLOR)
            video_frames.append(frame)

        loop = asyncio.get_running_loop()
        result = await loop.run_in_executor(
            thread_pool,
            functools.partial(
                visibility_test, video_frames, roomId, intervieweeId, createCSV
            ),
        )
        return JSONResponse(content=result, status_code=200)
    except Exception as e:
        print("Error testing camera:", e)
        return JSONResponse(
            content={"status": False, "error": "Error testing camera"}, status_code=500
        )


@app.post("/internal/tutorial-click/{roomId}/{intervieweeId}")
async def tutorial_click(
    roomId: str,
    intervieweeId: str,
    files: List[UploadFile] = File(...),
    clickPosition: Optional[str] = Form(None),
    screenDimensions: Optional[str] = Form(None),
    stepId: Optional[str] = Form(None),
    timestamp: Optional[str] = Form(None),
):
    try:
        video_frames = []
        for file in files:
            img_bytes = await file.read()
            img_np = np.frombuffer(img_bytes, dtype=np.uint8)
            frame = cv2.imdecode(img_np, cv2.IMREAD_COLOR)
            video_frames.append(frame)

        click_position = json.loads(clickPosition) if clickPosition else {}
        screen_dimensions = json.loads(screenDimensions) if screenDimensions else {}
        timestamp_value = (
            int(timestamp)
            if timestamp
            else int(datetime.datetime.now().timestamp() * 1000)
        )

        loop = asyncio.get_running_loop()
        await loop.run_in_executor(
            thread_pool,
            functools.partial(
                process_tutorial_click,
                video_frames,
                roomId,
                intervieweeId,
                click_position,
                screen_dimensions,
                stepId,
                timestamp_value,
            ),
        )

        return JSONResponse(content={"status": True}, status_code=200)
    except Exception as e:
        print("Error processing clicked frame camera:", e)
        return JSONResponse(
            content={
                "status": False,
                "error": f"Error processing clicked frame camera: {str(e)}",
            },
            status_code=500,
        )
