import os
import asyncio
import warnings
from .utils import process_voice
from fastapi import <PERSON><PERSON><PERSON>, Request, WebSocket
from .gaze_ai import GazeAI, gaze_ai_instances
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.templating import Jin<PERSON>2Templates
from .utils.config_env import STATIC_TOKEN

warnings.filterwarnings(
    "ignore", category=UserWarning, module="google.protobuf.symbol_database"
)

app = FastAPI()

templates = Jinja2Templates(directory="templates")

@app.on_event("shutdown")
async def shutdown_event():
    instance_ids = list(gaze_ai_instances.keys())

    for room_id in instance_ids:
        try:
            instance = gaze_ai_instances.get(room_id)
            if instance:
                print(f"Cleaning up instance for room {room_id}")
                await instance.cleanup()
                del gaze_ai_instances[room_id]
        except Exception as e:
            print(f"Error cleaning up instance {room_id}: {e}")

    gaze_ai_instances.clear()

    print("Server shutdown cleanup completed")


@app.get("/")
async def get():
    return JSONResponse(content={"success": "Server Running"}, status_code=200)


@app.post("/internal/join-room/{force_join}")
async def join_room(request: Request, force_join: bool):
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header[7:]
    else:
        token = None

    if token != STATIC_TOKEN:
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

    data = await request.json()
    print("rejoining room", data)
    roomId = data.get("roomId")
    companyId = data.get("companyId")
    intervieweeId = data.get("intervieweeId")
    if not roomId:
        return JSONResponse(
            content={"error": "Missing roomId", "success": False},
            status_code=400
        )

    try:
        if roomId in gaze_ai_instances:
            if force_join:
                old_instance = gaze_ai_instances[roomId]
                await old_instance.cleanup()
                del gaze_ai_instances[roomId]

                gaze_ai_instance = GazeAI(roomId, companyId)
                gaze_ai_instances[roomId] = gaze_ai_instance
                await gaze_ai_instance.init()
                if intervieweeId:
                    print("interveiwee id received", intervieweeId)
                    gaze_ai_instance.set_interviewee(intervieweeId)
                return JSONResponse(
                    content={
                        "message": "Already connected, Reconnected",
                        "roomId": roomId,
                        "success": True,
                    }
                )
            else:
                return JSONResponse(
                    content={
                        "message": "Already connected",
                        "roomId": roomId,
                        "success": True,
                    }
                )
        gaze_ai_instance = GazeAI(roomId, companyId)
        gaze_ai_instances[roomId] = gaze_ai_instance
        await gaze_ai_instance.init()
        if intervieweeId:
            print("interveiwee id received", intervieweeId)
            gaze_ai_instance.set_interviewee(intervieweeId)
        return JSONResponse(
            content={"message": "Connected", "roomId": roomId, "success": True}
        )
    except Exception as e:
        if roomId in gaze_ai_instances:
            await gaze_ai_instances[roomId].cleanup()
            del gaze_ai_instances[roomId]
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.post("/internal/join-tutorial")
async def join_room(request: Request):
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header[7:]
    else:
        token = None

    if token != STATIC_TOKEN:
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

    data = await request.json()
    print("rejoining tutorial room", data)
    roomId = data.get("roomId")
    companyId = data.get("companyId")
    intervieweeId = data.get("intervieweeId")
    if not roomId:
        return JSONResponse(
            content={"error": "Missing roomId", "success": False},
            status_code=400
        )

    try:
        if roomId in gaze_ai_instances:
            return JSONResponse(
                content={
                    "message": "Already connected",
                    "roomId": roomId,
                    "success": True,
                }
            )
        gaze_ai_instance = GazeAI(roomId, companyId, True)
        gaze_ai_instances[roomId] = gaze_ai_instance
        await gaze_ai_instance.init()
        if intervieweeId:
            print("interveiwee id received", intervieweeId)
            gaze_ai_instance.set_interviewee(intervieweeId)
        return JSONResponse(
            content={"message": "joined tutorial room",
                     "roomId": roomId, "success": True}
        )
    except Exception as e:
        if roomId in gaze_ai_instances:
            await gaze_ai_instances[roomId].cleanup()
            del gaze_ai_instances[roomId]
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.get("/voice_data/{uid}")
async def serve_voice_data(request: Request, uid: str):
    result = process_voice.fetchall_embeddings(uid)
    return templates.TemplateResponse(
        "voice_biometrics.html", {"request": request, "result": result, "uid": uid}
    )
