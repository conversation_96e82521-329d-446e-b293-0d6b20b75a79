import os
import json
import pytz
import torch
import requests
import numpy as np
from datetime import datetime
from .config_env import SERVER_URL, STATIC_TOKEN


def calculate_similarity(emb1, emb2):
    similarity = torch.nn.CosineSimilarity(dim=-1, eps=1e-6)
    return similarity(torch.tensor(emb1), torch.tensor(emb2))


def fetchall_embeddings(uid):
    embeddings = []
    fetchData = ""
    try:
        url = f"{SERVER_URL}/internal/embeddings/{uid}"
        headers = {"Authorization": f"Bearer {STATIC_TOKEN}"}
        response = requests.get(url, headers=headers)

        data = response.json()
        if data["success"]:
            fetchData = data["data"]
        else:
            print(json.dumps({"error": "No embeddings found for the given UID."}))
    except Exception as e:
        print("error:", e)

    for item in fetchData:
        interview_id = item["interview_id"]
        if "embeddings" in item["embeddings"]:
            embedding = item["embeddings"]["embeddings"]
        else:
            embedding = item["embeddings"]
        time = item["created_at"]

        embedding_array = np.array(embedding, dtype=np.float64)
        embeddings.append((interview_id, embedding_array, time))

    similarity_results = {}
    for i in range(len(embeddings)):
        for j in range(i + 1, len(embeddings)):
            interview_id_1, embedding_1, time_1 = embeddings[i]
            interview_id_2, embedding_2, time_2 = embeddings[j]

            similarity = calculate_similarity(embedding_1, embedding_2)

            key = f"{interview_id_1} - {utc_to_local(time_1)} & {interview_id_2} - {utc_to_local(time_2)}"
            print(key, " = ", similarity.tolist())
            if similarity >= 0.70:
                similarity_results[key] = "Match"
            else:
                similarity_results[key] = "Not matched"
    return similarity_results


def utc_to_local(time):
    utc_time_str = time
    utc_format = "%Y-%m-%d %H:%M:%S"

    utc_time = datetime.strptime(utc_time_str, utc_format)

    # Define UTC and IST timezones
    utc_zone = pytz.timezone("UTC")
    ist_zone = pytz.timezone("Asia/Kolkata")

    # Localize the UTC time and convert to IST
    utc_time = utc_zone.localize(utc_time)
    ist_time = utc_time.astimezone(ist_zone)

    return ist_time.strftime(utc_format)
