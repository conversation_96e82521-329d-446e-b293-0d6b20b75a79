import os
import csv
import pandas as pd


def save_key_data_to_csv(data, file_path):
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        columns = ["Time", "Key", "Line_Number", "Column"]
        file_exists = os.path.exists(file_path)
        df = pd.DataFrame(data, columns=columns)
        df.to_csv(file_path, mode="a", index=False, header=not file_exists)
    except Exception as e:
        print("save_key_data_to_csv -", e)


def save_input_count_to_csv(data, file_path):
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        columns = [
            "Start_Time",
            "Key_Count",
            "Mouse_Movement_Count",
            "Bot_Speaking",
            "Question",
        ]
        file_exists = os.path.exists(file_path)
        df = pd.DataFrame([data], columns=columns)
        df.to_csv(file_path, mode="a", index=False, header=not file_exists)
    except Exception as e:
        print("save_input_count_to_csv -", e)


def save_volume_data_to_csv(data, file_path, isMurmuring):
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        file_exists = os.path.exists(file_path)
        df = pd.DataFrame(data)
        df["isMurmuring"] = isMurmuring
        df.to_csv(file_path, mode="a", index=False, header=not file_exists)
    except Exception as e:
        print("save_volume_data_to_csv -", e)


def save_tab_focus_change_to_csv(data, file_path):
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        columns = ["type", "timestamp"]
        file_exists = os.path.exists(file_path)
        df = pd.DataFrame([data], columns=columns)
        df.to_csv(file_path, mode="a", index=False, header=not file_exists)
    except Exception as e:
        print("save_tab_focus_change_to_csv -", e)


def save_bad_network_interval_to_csv(data, file_path):
    """
    Save bad network interval data to CSV file.
    Uses the same format as tab focus change data.

    Args:
        data (dict): Dictionary with 'type' and 'timestamp' keys
        file_path (str): Path to the CSV file
    """
    try:
        save_tab_focus_change_to_csv(data, file_path)
    except Exception as e:
        print("save_bad_network_interval_to_csv -", e)


def save_bot_thinking_to_csv(data, file_path):
    try:
        if data != None:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            columns = ["type", "timestamp"]
            file_exists = os.path.exists(file_path)
            df = pd.DataFrame(data, columns=columns)
            df.to_csv(file_path, mode="a", index=False, header=not file_exists)
    except Exception as e:
        print("save_bot_thinking_to_csv -", e)


def save_producer_stats_to_csv(stats, file_path, producer_index):
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        file_exists = os.path.exists(file_path)
        data_dict = {
            "ProducerIndex": producer_index,
            "Timestamp": stats.get("timestamp"),
            "SSRC": stats.get("ssrc"),
            "RTX_SSRC": stats.get("rtxSsrc"),
            "Kind": stats.get("kind"),
            "MimeType": stats.get("mimeType"),
            "PacketsLost": stats.get("packetsLost"),
            "FractionLost": stats.get("fractionLost"),
            "PacketsDiscarded": stats.get("packetsDiscarded"),
            "PacketsRetransmitted": stats.get("packetsRetransmitted"),
            "PacketsRepaired": stats.get("packetsRepaired"),
            "NackCount": stats.get("nackCount"),
            "NackPacketCount": stats.get("nackPacketCount"),
            "PliCount": stats.get("pliCount"),
            "FirCount": stats.get("firCount"),
            "Score": stats.get("score"),
            "RoundTripTime": stats.get("roundTripTime"),
            "Type": stats.get("type"),
            "Jitter": stats.get("jitter"),
            "ByteCount": stats.get("byteCount"),
            "PacketCount": stats.get("packetCount"),
            "Bitrate": stats.get("bitrate"),
        }
        df = pd.DataFrame([data_dict])
        df.to_csv(file_path, mode="a", index=False, header=not file_exists)
    except Exception as e:
        print("save_producer_stats_to_csv -", e)
