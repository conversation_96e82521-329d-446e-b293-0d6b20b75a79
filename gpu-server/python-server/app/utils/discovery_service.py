import os
import aiohttp
from urllib.parse import urljoin
from .config_env import DISCOVERY_URL


async def get_mediaserver_endpoint(company_id, room_id):
    env = os.environ.get("NODE_ENV", "development")
    if env == "development":
        return "http://localhost:3001"

    endpoint_path = f"/VMAllocation/getAllocatedVM/{company_id}/{room_id}"
    url = urljoin(DISCOVERY_URL, endpoint_path)

    headers = {
        "Content-Type": "application/json",
        "x-discovery-secret": "jf203jd0jfasjfojaflaksdjflkjsdafl",
    }

    async with aiohttp.ClientSession() as session:
        async with session.get(url, headers=headers) as response:
            response.raise_for_status()
            data = await response.json()
            return data["domainName"]
