import os
from dotenv import load_dotenv
from pathlib import Path


def load_environment():
    current_env = os.environ.get("NODE_ENV", "development")

    env_files = {
        "development": ".env.local",
        "staging": ".env.staging",
        "production": ".env",
    }

    env_file = env_files.get(current_env, ".env.local")
    env_path = Path(os.getcwd()) / env_file

    load_dotenv(env_path)


load_environment()

STATIC_TOKEN = os.getenv("STATIC_TOKEN")
DISCOVERY_URL = os.getenv("DISCOVERY_URL")
SERVER_URL = os.getenv("SERVER_URL")
CSV_FILE_PATH = os.getenv("CSV_FILE_PATH")
