import os
import ssl
import json
import aiohttp
import asyncio
import socketio
from .utils import save_data
from pymediasoup import Device
from pymediasoup import AiortcHandler
from .utils.config_env import STATIC_TOKEN, CSV_FILE_PATH
from .utils.discovery_service import get_mediaserver_endpoint

gaze_ai_instances = {}


class GazeAI:
    def __init__(self, roomId, companyId, isTutorial=False):
        print(
            f"Creating GazeAI instance for roomId: {
                roomId} and companyId: {companyId}"
        )
        self.mediaserverEndpoint = None
        self.isTutorial = isTutorial
        self.companyId = companyId
        self.socket = None
        self.server_socket = None
        self.roomId = roomId
        self.peerId = "gaze-ai-bot"
        self.transport_recv = None
        self.transport_send = None
        self.data_producer = None
        self.device = None
        self.consumer = None
        self.producerPeerId = None
        self.isMurmuring = 0
        self.file_path = None
        self.http_session = None
        self.connector = None

    async def init(self):
        print(f"Connecting to Mediasoup server for uid: {self.peerId}")

        try:
            current_directory = os.path.dirname(os.path.abspath(__file__))
            ca_cert_path = os.path.join(
                current_directory, "..", "certs", "__hyrr_app.ca-bundle"
            )

            ca_cert_path = os.path.abspath(ca_cert_path)

            ssl_context_media_server = ssl.create_default_context()
            ssl_context_media_server.load_verify_locations(cafile=ca_cert_path)

            self.connector = aiohttp.TCPConnector(ssl=ssl_context_media_server)
            self.http_session = aiohttp.ClientSession(
                connector=self.connector, timeout=aiohttp.ClientTimeout(
                    total=30)
            )

            self.socket = socketio.AsyncClient(
                http_session=self.http_session,
                reconnection=False,
            )

            await self.setup_mediasoup_socket()
        except Exception as e:
            print(f"Error in init: {e}")
            await self.cleanup()
            raise

    def set_interviewee(self, intervieweeId):
        print("setting interviewee id and file_path", intervieweeId)
        self.producerPeerId = intervieweeId
        self.file_path = (
            f"{CSV_FILE_PATH}/{self.roomId}_{self.producerPeerId}_tutorial"
            if self.isTutorial
            else f"{CSV_FILE_PATH}/{self.roomId}_{self.producerPeerId}"
        )

    async def setup_mediasoup_socket(self):
        @self.socket.on("connect")
        async def on_connect():
            print("mediasoup socket connected")
            if not self.socket:
                print("Unable to connect to Mediasoup server")
                return None

            print("joining mediasoup room: ", self.roomId)
            handler_factory = AiortcHandler.createFactory()
            self.device = Device(handlerFactory=handler_factory)

            await self.emit_with_response(
                self.socket,
                "joinRoom",
                {
                    "roomId": self.roomId,
                    "peerId": self.peerId,
                    "role": "interviewer",
                },
            )

            router_capabilities = await self.emit_with_response(
                self.socket, "getRouterRtpCapabilities", {
                    "roomId": self.roomId}
            )
            if not router_capabilities:
                print("Failed to get router RTP capabilities")
                return

            await self.device.load(router_capabilities)

            transport_send_data = await self.emit_with_response(
                self.socket,
                "createTransport",
                {"roomId": self.roomId, "peerId": self.peerId, "type": "send"},
            )

            self.transport_send = self.device.createSendTransport(
                id=transport_send_data["id"],
                iceParameters=transport_send_data["iceParameters"],
                iceCandidates=transport_send_data["iceCandidates"],
                dtlsParameters=transport_send_data["dtlsParameters"],
                sctpParameters=transport_send_data["sctpParameters"],
            )

            @self.transport_send.on("connect")
            async def on_transport_send_connect(dtlsParameters):
                await self.emit_with_response(
                    self.socket,
                    "connectTransport",
                    {
                        "roomId": self.roomId,
                        "peerId": self.peerId,
                        "dtlsParameters": dtlsParameters.dict(),
                        "type": "send",
                    },
                )

            @self.transport_send.on("producedata")
            async def on_data_produced(sctpStreamParameters, label, protocol, appData):
                response = await self.emit_with_response(
                    self.socket,
                    "produceData",
                    {
                        "roomId": self.roomId,
                        "peerId": self.peerId,
                        "sctpStreamParameters": sctpStreamParameters.dict(),
                        "label": label,
                        "protocol": protocol,
                        "appData": appData,
                    },
                )
                return response["id"]

            self.data_producer = await self.transport_send.produceData()

            @self.data_producer.on("open")
            def on_data_producer_open():
                print(f"Data producer is open {self.roomId}")

            @self.data_producer.on("close")
            def on_data_producer_close():
                print(f"Data producer is closed {self.roomId}")

            @self.data_producer.on("error")
            def on_data_producer_error():
                print(f"Data producer is error {self.roomId}")

            transport_recv_data = await self.emit_with_response(
                self.socket,
                "createTransport",
                {"roomId": self.roomId, "peerId": self.peerId, "type": "recv"},
            )

            self.transport_recv = self.device.createRecvTransport(
                id=transport_recv_data["id"],
                iceParameters=transport_recv_data["iceParameters"],
                iceCandidates=transport_recv_data["iceCandidates"],
                dtlsParameters=transport_recv_data["dtlsParameters"],
                sctpParameters=transport_recv_data["sctpParameters"],
            )

            @self.transport_recv.on("connect")
            async def on_transport_recv_connect(dtlsParameters):
                await self.emit_with_response(
                    self.socket,
                    "connectTransport",
                    {
                        "roomId": self.roomId,
                        "peerId": self.peerId,
                        "dtlsParameters": dtlsParameters.dict(),
                        "type": "recv",
                    },
                )

        @self.socket.on("connect_error")
        async def on_connect_error(err):
            print(f"Connection failed: {err}")
            try:
                if self.roomId in gaze_ai_instances:
                    instance = gaze_ai_instances.get(self.roomId)
                    if instance:
                        await instance.cleanup()
                        try:
                            del gaze_ai_instances[self.roomId]
                            print(
                                f"Removed GazeAI instance for roomId: {self.roomId}")
                        except KeyError:
                            pass
            except Exception as e:
                print(f"Error in connect_error handler: {e}")

        @self.socket.on("disconnect")
        async def on_disconnect():
            print("Disconnected from server", self.roomId)
            try:
                if self.roomId in gaze_ai_instances:
                    instance = gaze_ai_instances.get(self.roomId)
                    if instance:
                        await instance.cleanup()
                        try:
                            del gaze_ai_instances[self.roomId]
                            print(
                                f"Removed GazeAI instance for roomId: {self.roomId}")
                        except KeyError:
                            # Instance was already removed
                            pass
            except Exception as e:
                print(f"Error in disconnect handler: {e}")

        @self.socket.on("endInterview")
        async def on_endInterview(data=None):
            try:
                print(f"endInterview called for roomId: {self.roomId}")
                if self.roomId in gaze_ai_instances:
                    await self.cleanup()
                    del gaze_ai_instances[self.roomId]
                    print(f"Removed GazeAI instance for roomId: {self.roomId}")
            except Exception as e:
                print(f"Error in endInterview handler: {e}")

        @self.socket.on("endTutorial")
        async def on_endTutorial(data=None):
            try:
                print(f"endTutorial called for roomId: {self.roomId}")
                if self.roomId in gaze_ai_instances:
                    await self.cleanup()
                    del gaze_ai_instances[self.roomId]
                    print(f"Removed GazeAI instance for roomId: {self.roomId}")
            except Exception as e:
                print(f"Error in endTutorial handler: {e}")

        @self.socket.on("newProducer")
        async def on_newProducer(data):
            producerId = data.get("producerId")
            producerPeerId = data.get("peerId")

            if not self.transport_recv:
                print("Transport not created")
                return None

            response = await self.emit_with_response(
                self.socket,
                "consume",
                {
                    "roomId": self.roomId,
                    "peerId": self.peerId,
                    "producerId": producerId,
                    "producerPeerId": producerPeerId,
                    "rtpCapabilities": self.device.rtpCapabilities.dict(),
                },
            )
            if response.get("error"):
                print("Cannot Consume")
                return

            if response.get("appData").get("source") != "camera":
                return None

            self.producerPeerId = producerPeerId
            self.file_path = (
                f"{CSV_FILE_PATH}/{self.roomId}_{self.producerPeerId}_tutorial"
                if self.isTutorial
                else f"{CSV_FILE_PATH}/{self.roomId}_{self.producerPeerId}"
            )
            print("producerPeerId:", self.producerPeerId)

        @self.socket.on("inputData")
        async def on_inputData(data):
            if not self.socket or not self.socket.connected:
                print("Socket not connected, cannot process inputData")
                return None

            if not (self.file_path and self.producerPeerId):
                print("No producerPeerId and file_path")
                return None

            try:
                save_data.save_key_data_to_csv(
                    data.get("keyData"), f"{self.file_path}/key_data.csv"
                )
                save_data.save_input_count_to_csv(
                    data.get("inputCount"), f"{self.file_path}/input_count.csv"
                )
                save_data.save_bot_thinking_to_csv(
                    data.get(
                        "botThinking"), f"{self.file_path}/bot_thinking.csv"
                )
            except Exception as e:
                print(f"Error processing inputData: {e}")

        @self.socket.on("volumeData")
        async def on_volumeData(data):
            if not self.socket or not self.socket.connected:
                print("Socket not connected, cannot process volumeData")
                return None

            if not (self.file_path and self.producerPeerId):
                print("No producerPeerId and file_path")
                return None

            try:
                save_data.save_volume_data_to_csv(
                    data, f"{self.file_path}/volume_data.csv", self.isMurmuring
                )
            except Exception as e:
                print(f"Error processing volumeData: {e}")

        @self.socket.on("producerStats")
        async def on_producer_stats(data):
            if not (self.file_path and self.producerPeerId):
                print("No producerPeerId and file_path")
                return None
            stats = data.get("stats", {})
            producer_index = data.get("producerIndex", -1)
            save_data.save_producer_stats_to_csv(
                stats, f"{self.file_path}/producer_stats.csv", producer_index
            )

        @self.socket.on("badNetworkInterval")
        async def on_bad_network_interval(data):
            if not self.file_path:
                print("No file_path to save bad network interval data")
                return None

            if "type" not in data or "timestamp" not in data:
                print(
                    "Invalid badNetworkInterval data format - missing type or timestamp")
                return None

            event_type = data.get("type")
            timestamp = data.get("timestamp")

            if timestamp is None:
                print("Missing timestamp in badNetworkInterval data")
                return None

            print(f"Bad network interval event: {event_type} at {timestamp}")
            save_data.save_bad_network_interval_to_csv(
                {"type": event_type, "timestamp": timestamp},
                f"{self.file_path}/bad_network_intervals.csv"
            )

        @self.socket.on("tabFocusChange")
        async def on_tab_focus_change(data):
            if not self.socket or not self.socket.connected:
                print("Socket not connected, cannot process tabFocusChange")
                return None

            if not (self.file_path and self.producerPeerId):
                print("No producerPeerId and file_path")
                return None

            try:
                save_data.save_tab_focus_change_to_csv(
                    data, f"{self.file_path}/tab_focus.csv"
                )
            except Exception as e:
                print(f"Error processing tabFocusChange: {e}")

        @self.socket.on("error")
        async def on_error(err):
            print(f"Socket error: {err}")
            try:
                if self.roomId in gaze_ai_instances:
                    instance = gaze_ai_instances.get(self.roomId)
                    if instance:
                        await instance.cleanup()
                        try:
                            del gaze_ai_instances[self.roomId]
                            print(
                                f"Removed GazeAI instance for roomId: {self.roomId}")
                        except KeyError:
                            pass
            except Exception as e:
                print(f"Error in error handler: {e}")

        self.mediaserverEndpoint = await get_mediaserver_endpoint(
            self.companyId, self.roomId
        )

        print(f"Mediaserver endpoint: {self.mediaserverEndpoint}")

        await self.socket.connect(
            self.mediaserverEndpoint,
            auth={"token": f"Bearer {STATIC_TOKEN}"},
            transports=["websocket", "polling"],
        )

    async def emit_with_response(self, socket, event, data):
        future = asyncio.Future()

        def callback(response):
            future.set_result(response)

        await socket.emit(event, data, callback=callback)
        return await future

    async def cleanup(self):
        """Cleanup all resources"""
        cleanup_errors = []

        try:
            if self.socket and self.socket.connected:
                try:
                    await asyncio.wait_for(self.socket.disconnect(), timeout=5.0)
                except Exception as e:
                    cleanup_errors.append(f"Socket disconnect error: {e}")

            if self.consumer:
                try:
                    await asyncio.wait_for(self.consumer.close(), timeout=5.0)
                except Exception as e:
                    cleanup_errors.append(f"Consumer close error: {e}")

            if self.transport_recv:
                try:
                    await asyncio.wait_for(self.transport_recv.close(), timeout=5.0)
                except Exception as e:
                    cleanup_errors.append(f"Transport recv close error: {e}")

            if self.transport_send:
                try:
                    await asyncio.wait_for(self.transport_send.close(), timeout=5.0)
                except Exception as e:
                    cleanup_errors.append(f"Transport send close error: {e}")

            # Close HTTP session and connector last
            if self.http_session and not self.http_session.closed:
                try:
                    await asyncio.wait_for(self.http_session.close(), timeout=5.0)
                except Exception as e:
                    cleanup_errors.append(f"HTTP session close error: {e}")

            if self.connector and not self.connector.closed:
                try:
                    await asyncio.wait_for(self.connector.close(), timeout=5.0)
                except Exception as e:
                    cleanup_errors.append(f"Connector close error: {e}")

        except asyncio.TimeoutError:
            cleanup_errors.append(f"Cleanup timed out for room {self.roomId}")
        except Exception as e:
            cleanup_errors.append(f"General cleanup error: {e}")
        finally:
            # Clear references
            self.socket = None
            self.http_session = None
            self.connector = None
            self.consumer = None
            self.transport_recv = None
            self.transport_send = None

            if cleanup_errors:
                print(f"Cleanup errors for room {self.roomId}:", "\n".join(
                    cleanup_errors))
