# python-server/Dockerfile
FROM nvidia/cuda:11.3.1-cudnn8-runtime-ubuntu20.04

RUN apt-get update && apt-get install -y \
    python3-pip \
    python3-dev 

WORKDIR /usr/src/app

COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 3003

ENV MEDIASOUP_URL=https://streamingserver.hyrr.app/
ENV STATIC_TOKEN=w0321lud6t47h9x4lahuwiivh2yhisirno5hy3ekvx5ztp57lxcyman1w3mfsxdvhtik8tgt02bb29t6q8ot52sn0wi8lb0ir2uvl1ijm3bpwnqmom4he00p6oq7of7pxauhlqhsyj0wksyipq8gczxhxt791dgfe7uhs4nav9vedft85eecdtn8h4ejewsmviglu0cl
ENV AZURE_STORAGE_CONNECTION_STRING=
ENV AZURE_STORAGE_CONTAINER_NAME=

CMD ["python3", "main.py"]

