import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TextBox
from io import String<PERSON>
from scipy.spatial import ConvexHull
from sklearn.cluster import DBSCAN
from scipy.stats import zscore
from matplotlib.path import Path


def load_and_process_data(file_path, start_time_str, end_time_str):
    """Load data from CSV and process it to get eye coordinates."""
    # Read CSV data, skipping lines that begin with "//"
    with open(file_path, "r") as f:
        lines = [line for line in f if not line.startswith("//")]
    data = "".join(lines)
    df = pd.read_csv(StringIO(data))

    # Convert time column to datetime
    df["time_dt"] = pd.to_datetime(df["time"], format="%H:%M:%S.%f")

    # Filter by time window
    start_time = pd.to_datetime(start_time_str, format="%H:%M:%S.%f")
    end_time = pd.to_datetime(end_time_str, format="%H:%M:%S.%f")
    df_window = df[(df["time_dt"] >= start_time) & (df["time_dt"] < end_time)]

    return df_window


def compute_eye_coordinates(df):
    """Compute eye gaze coordinates for each row in dataframe."""
    left_eye_keys = ["EYE_LOOK_IN_LEFT"]
    right_eye_keys = ["EYE_LOOK_IN_RIGHT"]
    top_eye_keys = ["EYE_LOOK_UP_LEFT", "EYE_LOOK_UP_RIGHT"]
    bottom_eye_keys = ["EYE_LOOK_DOWN_LEFT", "EYE_LOOK_DOWN_RIGHT"]

    combined_xs = []
    combined_ys = []

    for _, row in df.iterrows():
        left_avg = np.mean([float(row[k]) for k in left_eye_keys if k in row])
        right_avg = np.mean([float(row[k]) for k in right_eye_keys if k in row])
        x_eye = right_avg - left_avg

        top_avg = np.mean([float(row[k]) for k in top_eye_keys if k in row])
        bottom_avg = np.mean([float(row[k]) for k in bottom_eye_keys if k in row])
        y_eye = top_avg - bottom_avg

        combined_xs.append(x_eye)
        combined_ys.append(y_eye)

    return combined_xs, combined_ys


def remove_outliers(xs, ys, threshold=4):
    """Remove outliers using z-score method."""
    if len(xs) == 0 or len(ys) == 0:
        return np.array([]), np.array([])

    x_scores = np.abs(zscore(xs))
    y_scores = np.abs(zscore(ys))

    mask = (x_scores < threshold) & (y_scores < threshold)
    return np.array(xs)[mask], np.array(ys)[mask]


def remove_outliers_using_clustering(xs, ys, eps=0.05, min_samples=15):
    """
    Remove outliers using DBSCAN clustering.

    Args:
        xs, ys: Coordinate arrays
        eps: Maximum distance between points to be considered neighbors
        min_samples: Minimum number of points required to form a dense region

    Returns:
        Filtered coordinate arrays with outliers removed
    """
    if len(xs) < min_samples or len(ys) < min_samples:
        return xs, ys

    # Combine coordinates into a single array of points
    points = np.vstack([xs, ys]).T

    # Apply DBSCAN clustering
    db = DBSCAN(eps=eps, min_samples=min_samples).fit(points)
    labels = db.labels_

    # Filter out noise points (labeled as -1 by DBSCAN)
    mask = labels != -1
    return points[mask, 0], points[mask, 1]


def create_polygon(xs, ys):
    """Create a convex hull polygon from coordinates."""
    if len(xs) < 3 or len(ys) < 3:
        return np.array([[0, 0], [0, 0], [0, 0]])  # Default triangle

    points = np.vstack([xs, ys]).T
    hull = ConvexHull(points)
    return points[hull.vertices]


# Load full interview dataset once for better performance
def load_full_interview_data(file_path):
    with open(file_path, "r") as f:
        lines = [line for line in f if not line.startswith("//")]
    data = "".join(lines)
    df = pd.read_csv(StringIO(data))
    df["time_dt"] = pd.to_datetime(df["time"], format="%H:%M:%S.%f")
    return df


# Process tutorial data (only needs to be done once)
tutorial_df = load_full_interview_data("x9c4_tut.csv")
tutorial_xs, tutorial_ys = compute_eye_coordinates(tutorial_df)

# First pass: Remove extreme outliers with z-score
clean_xs, clean_ys = remove_outliers(tutorial_xs, tutorial_ys, threshold=4)

# Second pass: Use clustering to remove points outside of clusters
clean_xs, clean_ys = remove_outliers_using_clustering(clean_xs, clean_ys)

# Create polygon from clean data
polygon = create_polygon(clean_xs, clean_ys)
polygon_path = Path(polygon)

# Load full interview data
interview_file = "x9c4.csv"
full_interview_df = load_full_interview_data(interview_file)

# Get min and max datetime from the data for slider bounds
min_time = full_interview_df["time_dt"].min()
max_time = full_interview_df["time_dt"].max()


# Convert to seconds since midnight for sliders
def time_to_seconds(dt):
    return dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1000000


def seconds_to_time_str(seconds):
    h = int(seconds // 3600)
    m = int((seconds % 3600) // 60)
    s = seconds % 60
    return f"{h:02d}:{m:02d}:{s:06.3f}"


min_seconds = time_to_seconds(min_time.time())
max_seconds = time_to_seconds(max_time.time())

# Create the interactive plot
fig, ax = plt.subplots(figsize=(12, 10))
plt.subplots_adjust(bottom=0.25)  # Make room for sliders

# Make sure initial values are within bounds
initial_start = min_seconds
initial_end = min_seconds + 60

# Plot tutorial data (fixed)
tutorial_scatter = ax.scatter(
    tutorial_xs,
    tutorial_ys,
    c="green",
    alpha=0.2,
    s=20,
    label="Original Tutorial Points",
)
clean_tutorial_scatter = ax.scatter(
    clean_xs, clean_ys, c="blue", alpha=0.2, s=30, label="Cleaned Tutorial Points"
)

# Plot polygon from clean data
polygon_plot = ax.fill(
    polygon[:, 0], polygon[:, 1], alpha=0.2, color="blue", label="Tutorial Data Region"
)

# Initialize with empty interview data1
interview_scatter = ax.scatter([], [], c="red", alpha=0.5, s=30, label="Interview Data")

# Add text for percentage
percentage_text = ax.text(
    0.02,
    0.96,
    "",
    transform=ax.transAxes,
    fontsize=12,
    bbox=dict(facecolor="white", alpha=0.7),
)

# Set up axes
ax.set_xlabel("Horizontal Eye Coordinate")
ax.set_ylabel("Vertical Eye Coordinate")
ax.set_title("Eye Gaze Analysis with Cluster-Based Outlier Removal")
ax.legend()
ax.grid(True)

# Create sliders
ax_start = plt.axes([0.15, 0.12, 0.65, 0.03])
ax_end = plt.axes([0.15, 0.07, 0.65, 0.03])
ax_offset_text = plt.axes([0.15, 0.02, 0.45, 0.03])
offset_textbox = TextBox(ax_offset_text, "Time Offset (sec): ", initial="0")
apply_offset_ax = plt.axes([0.65, 0.02, 0.15, 0.03])

apply_offset_button = Button(apply_offset_ax, "Apply Offset")
start_slider = Slider(
    ax_start, "Start Time", min_seconds, max_seconds, valinit=initial_start, valstep=0.1
)
end_slider = Slider(
    ax_end, "End Time", min_seconds, max_seconds, valinit=initial_end, valstep=0.1
)

# Create reset button
reset_ax = plt.axes([0.85, 0.12, 0.1, 0.03])
reset_button = Button(reset_ax, "Reset")

# Labels to show current time values
time_label_ax = plt.axes([0.15, 0.17, 0.65, 0.03])
time_label_ax.axis("off")
time_label = time_label_ax.text(
    0.5,
    0.5,
    f"Window: {seconds_to_time_str(initial_start)} - {seconds_to_time_str(initial_end)}",
    ha="center",
    va="center",
)

current_offset = 0


def apply_offset(event=None):
    """Apply the offset from the text input"""
    global current_offset
    try:
        # Get value from text box and convert to float
        offset_value = float(offset_textbox.text)
        current_offset = offset_value
        # Update the plot
        update(None)
    except ValueError:
        # If input is not a valid number, reset to 0
        offset_textbox.set_val("0")
        current_offset = 0
        update(None)


def update(val):
    """Update the plot when sliders change"""
    start_sec = start_slider.val
    end_sec = end_slider.val
    time_offset = current_offset

    # Ensure end is always after start
    if end_sec <= start_sec:
        end_sec = start_sec + 0.1
        end_slider.set_val(end_sec)

    # Convert slider values to time strings
    start_time_str = seconds_to_time_str(start_sec)
    end_time_str = seconds_to_time_str(end_sec)

    # Update the time label
    time_label.set_text(f"Window: {start_time_str} - {end_time_str}")

    # Filter data based on new time window
    start_time = pd.to_datetime(start_time_str, format="%H:%M:%S.%f")
    end_time = pd.to_datetime(end_time_str, format="%H:%M:%S.%f")

    start_time = start_time + pd.Timedelta(seconds=time_offset)
    end_time = end_time + pd.Timedelta(seconds=time_offset)

    # Filter data based on new time window with offset applied
    df_window = full_interview_df[
        (full_interview_df["time_dt"] >= start_time)
        & (full_interview_df["time_dt"] < end_time)
    ]

    # Calculate new coordinates
    if not df_window.empty:
        interview_xs, interview_ys = compute_eye_coordinates(df_window)

        # Update scatter plot
        interview_scatter.set_offsets(np.column_stack([interview_xs, interview_ys]))

        # Calculate percentage inside polygon
        interview_points = np.vstack([interview_xs, interview_ys]).T
        inside_mask = polygon_path.contains_points(interview_points)
        percent_inside = (
            100 * np.sum(inside_mask) / len(interview_points)
            if len(interview_points) > 0
            else 0
        )

        # Update percentage text
        percentage_text.set_text(f"Inside polygon: {percent_inside:.1f}%")
    else:
        interview_scatter.set_offsets(np.empty((0, 2)))
        percentage_text.set_text("No data in selected range")

    fig.canvas.draw_idle()


def reset(event):
    """Reset sliders to initial values"""
    start_slider.set_val(initial_start)
    end_slider.set_val(initial_end)


# Connect callbacks
start_slider.on_changed(update)
end_slider.on_changed(update)
reset_button.on_clicked(reset)
apply_offset_button.on_clicked(apply_offset)
offset_textbox.on_submit(apply_offset)
# Initial update
update(None)

plt.show()

# Print final percentage (for the last selected window)
if "interview_points" in locals() and "inside_mask" in locals():
    percent_inside = (
        100 * np.sum(inside_mask) / len(interview_points)
        if len(interview_points) > 0
        else 0
    )
    print(
        f"Percentage of interview gaze points inside tutorial polygon: {percent_inside:.1f}%"
    )
