import h5py
import numpy as np
import pandas as pd
from datetime import datetime

BLENDSHAPE_MAPPING = {
    0: "NEUTRAL",
    1: "BROW_DOWN_LEFT",
    2: "BROW_DOWN_RIGHT",
    3: "BROW_INNER_UP",
    4: "BROW_OUTER_UP_LEFT",
    5: "BROW_OUTER_UP_RIGHT",
    6: "CHEEK_PUFF",
    7: "CHEEK_SQUINT_LEFT",
    8: "CHEEK_SQUINT_RIGHT",
    9: "EYE_BLINK_LEFT",
    10: "EYE_BLINK_RIGHT",
    11: "EYE_LOOK_DOWN_LEFT",
    12: "EYE_LOOK_DOWN_RIGHT",
    13: "EYE_LOOK_IN_LEFT",
    14: "EYE_LOOK_IN_RIGHT",
    15: "EYE_LOOK_OUT_LEFT",
    16: "EYE_LOOK_OUT_RIGHT",
    17: "EYE_LOOK_UP_LEFT",
    18: "EYE_LOOK_UP_RIGHT",
    19: "EYE_SQUINT_LEFT",
    20: "EYE_SQUINT_RIGHT",
    21: "EYE_WIDE_LEFT",
    22: "EYE_WIDE_RIGHT",
    23: "JAW_FORWARD",
    24: "JAW_LEFT",
    25: "JAW_OPEN",
    26: "JAW_RIGHT",
    27: "MOUTH_CLOSE",
    28: "MOUTH_DIMPLE_LEFT",
    29: "MOUTH_DIMPLE_RIGHT",
    30: "MOUTH_FROWN_LEFT",
    31: "MOUTH_FROWN_RIGHT",
    32: "MOUTH_FUNNEL",
    33: "MOUTH_LEFT",
    34: "MOUTH_LOWER_DOWN_LEFT",
    35: "MOUTH_LOWER_DOWN_RIGHT",
    36: "MOUTH_PRESS_LEFT",
    37: "MOUTH_PRESS_RIGHT",
    38: "MOUTH_PUCKER",
    39: "MOUTH_RIGHT",
    40: "MOUTH_ROLL_LOWER",
    41: "MOUTH_ROLL_UPPER",
    42: "MOUTH_SHRUG_LOWER",
    43: "MOUTH_SHRUG_UPPER",
    44: "MOUTH_SMILE_LEFT",
    45: "MOUTH_SMILE_RIGHT",
    46: "MOUTH_STRETCH_LEFT",
    47: "MOUTH_STRETCH_RIGHT",
    48: "MOUTH_UPPER_UP_LEFT",
    49: "MOUTH_UPPER_UP_RIGHT",
    50: "NOSE_SNEER_LEFT",
    51: "NOSE_SNEER_RIGHT",
}

# Modify the load_training_data function to use named columns
def load_training_data(file_path):
    with h5py.File(file_path, "r+") as f:
        face_data = f["face_data"]

        blendshapes_list = []
        labels = []
        timestamps = []

        for timestamp in face_data.keys():
            sample = face_data[timestamp]
            blendshapes_raw = sample["blendshapes"][()]

            blendshapes_dict = {}
            for idx, score in blendshapes_raw:
                try:
                    key = BLENDSHAPE_MAPPING.get(int(idx), f"BLEND_{int(idx)}")
                    blendshapes_dict[key] = float(score) if not np.isnan(score) else 0.0
                except (ValueError, TypeError):
                    continue

            timestamps.append(timestamp)
            blendshapes_list.append(blendshapes_dict)
            labels.append(sample.attrs["is_cheating"])

        # Create dtype with named fields
        dtype = [
            (BLENDSHAPE_MAPPING.get(i, f"BLEND_{i}"), "f4")
            for i in range(len(BLENDSHAPE_MAPPING))
        ]

        # Convert to structured array with named fields
        blendshapes = np.array(
            [tuple(b.get(name, 0.0) for name, _ in dtype) for b in blendshapes_list],
            dtype=dtype,
        )

        return {
            "timestamps": np.array(timestamps),
            "blendshapes": blendshapes,
            "is_cheating": np.array(labels),
        }


def convert_timestamp_to_time(timestamp):
    # Convert timestamp to milliseconds and then to datetime
    dt = datetime.fromtimestamp(int(timestamp) / 1000.0)
    return dt.strftime("%H:%M:%S.%f")[:-3]  # Format to HH:MM:SS.sss

def save_to_csv(data, csv_file_path):
    timestamps = data["timestamps"]
    blendshapes = data["blendshapes"]
    is_cheating = data["is_cheating"]

    blendshape_names = blendshapes.dtype.names

    # Convert structured array to regular array
    blendshapes_flat = np.array([list(row) for row in blendshapes])

    # Convert timestamps to actual time
    times = [convert_timestamp_to_time(ts) for ts in timestamps]

    df = pd.DataFrame(blendshapes_flat, columns=blendshape_names)
    df.insert(0, "time", times)
    df["is_cheating"] = is_cheating

    # Format float columns to 15 decimal places
    for col in df.columns:
        if col != "time" and col != "is_cheating":
            df[col] = df[col].apply(lambda x: format(float(x), ".15f"))

    df.to_csv(csv_file_path, index=False)


file_path = "x9c4.h5"
csv_file_path = "x9c4.csv"

data = load_training_data(file_path)
save_to_csv(data, csv_file_path)
