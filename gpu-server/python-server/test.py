# import h5py
# import numpy as np

# file_path = "face_data.h5"


# def load_training_data(file_path):
#     with h5py.File(file_path, "r") as f:
#         face_data = f["face_data"]

#         landmarks_data = []
#         blendshapes_data = []
#         transform_matrix = []
#         labels = []
#         for timestamp in face_data.keys():
#             sample = face_data[timestamp]
#             landmarks_data.append(sample["landmarks"][()])
#             blendshapes_data.append(sample["blendshapes"][()])
#             transform_matrix.append(sample["transform_matrix"][()])
#             labels.append(sample.attrs["is_cheating"])

#         return {
#             "landmarks": np.array(landmarks_data),
#             "blendshapes": np.array(blendshapes_data),
#             "transform_matrix": np.array(transform_matrix),
#             "is_cheating": np.array(labels),
#         }


# print(load_training_data(file_path))

# import h5py
# import numpy as np
# import pandas as pd

# def load_training_data(file_path):
#     with h5py.File(file_path, "r") as f:
#         face_data = f["face_data"]

#         blendshapes_data = []
#         labels = []
#         timestamps = []
#         for timestamp in face_data.keys():
#             sample = face_data[timestamp]
#             blendshapes_data.append(sample["blendshapes"][()])
#             labels.append(sample.attrs["is_cheating"])
#             timestamps.append(timestamp)

#         blendshape_names = face_data[list(face_data.keys())[0]][
#             "blendshapes"
#         ].dtype.names
#         blendshapes = np.array(
#             blendshapes_data, dtype=[(name, "f4") for name in blendshape_names]
#         )

#         return {
#             "timestamps": np.array(timestamps),
#             "blendshapes": blendshapes,
#             "is_cheating": np.array(labels),
#         }


# def save_to_csv(data, csv_file_path):
#     timestamps = data["timestamps"]
#     blendshapes = data["blendshapes"]
#     is_cheating = data["is_cheating"]

#     blendshape_names = blendshapes.dtype.names

#     # Flatten the blendshapes array
#     blendshapes_flat = np.array([list(b[0]) for b in blendshapes])

#     df = pd.DataFrame(blendshapes_flat, columns=blendshape_names)
#     df.insert(0, "timestamp", timestamps)
#     df["is_cheating"] = is_cheating

#     df.to_csv(csv_file_path, index=False)


# file_path = "face_data.h5"
# csv_file_path = "output.csv"

# data = load_training_data(file_path)
# save_to_csv(data, csv_file_path)

# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# import h5py
# import numpy as np
# import pandas as pd
# from datetime import datetime

# BLENDSHAPE_MAPPING = {
#     0: "NEUTRAL",
#     1: "BROW_DOWN_LEFT",
#     2: "BROW_DOWN_RIGHT",
#     3: "BROW_INNER_UP",
#     4: "BROW_OUTER_UP_LEFT",
#     5: "BROW_OUTER_UP_RIGHT",
#     6: "CHEEK_PUFF",
#     7: "CHEEK_SQUINT_LEFT",
#     8: "CHEEK_SQUINT_RIGHT",
#     9: "EYE_BLINK_LEFT",
#     10: "EYE_BLINK_RIGHT",
#     11: "EYE_LOOK_DOWN_LEFT",
#     12: "EYE_LOOK_DOWN_RIGHT",
#     13: "EYE_LOOK_IN_LEFT",
#     14: "EYE_LOOK_IN_RIGHT",
#     15: "EYE_LOOK_OUT_LEFT",
#     16: "EYE_LOOK_OUT_RIGHT",
#     17: "EYE_LOOK_UP_LEFT",
#     18: "EYE_LOOK_UP_RIGHT",
#     19: "EYE_SQUINT_LEFT",
#     20: "EYE_SQUINT_RIGHT",
#     21: "EYE_WIDE_LEFT",
#     22: "EYE_WIDE_RIGHT",
#     23: "JAW_FORWARD",
#     24: "JAW_LEFT",
#     25: "JAW_OPEN",
#     26: "JAW_RIGHT",
#     27: "MOUTH_CLOSE",
#     28: "MOUTH_DIMPLE_LEFT",
#     29: "MOUTH_DIMPLE_RIGHT",
#     30: "MOUTH_FROWN_LEFT",
#     31: "MOUTH_FROWN_RIGHT",
#     32: "MOUTH_FUNNEL",
#     33: "MOUTH_LEFT",
#     34: "MOUTH_LOWER_DOWN_LEFT",
#     35: "MOUTH_LOWER_DOWN_RIGHT",
#     36: "MOUTH_PRESS_LEFT",
#     37: "MOUTH_PRESS_RIGHT",
#     38: "MOUTH_PUCKER",
#     39: "MOUTH_RIGHT",
#     40: "MOUTH_ROLL_LOWER",
#     41: "MOUTH_ROLL_UPPER",
#     42: "MOUTH_SHRUG_LOWER",
#     43: "MOUTH_SHRUG_UPPER",
#     44: "MOUTH_SMILE_LEFT",
#     45: "MOUTH_SMILE_RIGHT",
#     46: "MOUTH_STRETCH_LEFT",
#     47: "MOUTH_STRETCH_RIGHT",
#     48: "MOUTH_UPPER_UP_LEFT",
#     49: "MOUTH_UPPER_UP_RIGHT",
#     50: "NOSE_SNEER_LEFT",
#     51: "NOSE_SNEER_RIGHT",
# }

# # Modify the load_training_data function to use named columns
# def load_training_data(file_path):
#     with h5py.File(file_path, "r+") as f:
#         face_data = f["face_data"]

#         blendshapes_list = []
#         labels = []
#         timestamps = []

#         for timestamp in face_data.keys():
#             sample = face_data[timestamp]
#             blendshapes_raw = sample["blendshapes"][()]

#             blendshapes_dict = {}
#             for idx, score in blendshapes_raw:
#                 try:
#                     key = BLENDSHAPE_MAPPING.get(int(idx), f"BLEND_{int(idx)}")
#                     blendshapes_dict[key] = float(score) if not np.isnan(score) else 0.0
#                 except (ValueError, TypeError):
#                     continue

#             timestamps.append(timestamp)
#             blendshapes_list.append(blendshapes_dict)
#             labels.append(sample.attrs["is_cheating"])

#         # Create dtype with named fields
#         dtype = [
#             (BLENDSHAPE_MAPPING.get(i, f"BLEND_{i}"), "f4")
#             for i in range(len(BLENDSHAPE_MAPPING))
#         ]

#         # Convert to structured array with named fields
#         blendshapes = np.array(
#             [tuple(b.get(name, 0.0) for name, _ in dtype) for b in blendshapes_list],
#             dtype=dtype,
#         )

#         return {
#             "timestamps": np.array(timestamps),
#             "blendshapes": blendshapes,
#             "is_cheating": np.array(labels),
#         }


# def convert_timestamp_to_time(timestamp):
#     # Convert timestamp to milliseconds and then to datetime
#     dt = datetime.fromtimestamp(int(timestamp) / 1000.0)
#     return dt.strftime("%H:%M:%S.%f")[:-3]  # Format to HH:MM:SS.sss

# def save_to_csv(data, csv_file_path):
#     timestamps = data["timestamps"]
#     blendshapes = data["blendshapes"]
#     is_cheating = data["is_cheating"]

#     blendshape_names = blendshapes.dtype.names

#     # Convert structured array to regular array
#     blendshapes_flat = np.array([list(row) for row in blendshapes])

#     # Convert timestamps to actual time
#     times = [convert_timestamp_to_time(ts) for ts in timestamps]

#     df = pd.DataFrame(blendshapes_flat, columns=blendshape_names)
#     df.insert(0, "time", times)
#     df["is_cheating"] = is_cheating

#     # Format float columns to 15 decimal places
#     for col in df.columns:
#         if col != "time" and col != "is_cheating":
#             df[col] = df[col].apply(lambda x: format(float(x), ".15f"))

#     df.to_csv(csv_file_path, index=False)


# file_path = "tut.h5"
# csv_file_path = "tutorial.csv"

# data = load_training_data(file_path)
# save_to_csv(data, csv_file_path)


# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------


# import matplotlib.pyplot as plt
# import numpy as np
# import pandas as pd

# # Define which blendshapes go into each region.
# REGION_KEYS = {
#     "Center": ["EYE_LOOK_IN_LEFT", "EYE_LOOK_IN_RIGHT"],
#     "Out": ["EYE_LOOK_OUT_LEFT", "EYE_LOOK_OUT_RIGHT"],
#     "Top": ["EYE_LOOK_UP_LEFT", "EYE_LOOK_UP_RIGHT"],
#     "Bottom": ["EYE_LOOK_DOWN_LEFT", "EYE_LOOK_DOWN_RIGHT"],
#     "Left": ["EYE_LOOK_DOWN_LEFT", "EYE_LOOK_IN_LEFT", "EYE_LOOK_UP_LEFT"],
#     "Right": ["EYE_LOOK_DOWN_RIGHT", "EYE_LOOK_IN_RIGHT", "EYE_LOOK_UP_RIGHT"],
# }


# def aggregate_regions(sample, region_keys=REGION_KEYS):
#     """Aggregate blendshape values for each region by averaging the constituent values."""
#     region_values = {}
#     for region, keys in region_keys.items():
#         try:
#             vals = [float(sample[k]) for k in keys if k in sample]
#             region_values[region] = np.mean(vals) if vals else 0.0
#         except Exception:
#             region_values[region] = 0.0
#     return region_values


# def count_active_regions(region_values, threshold=0.5):
#     """Count regions where the aggregated value is above a threshold."""
#     return sum(1 for v in region_values.values() if v > threshold)


# def plot_region_radar(region_values, active_count=None):
#     labels = list(region_values.keys())
#     values = list(region_values.values())
#     num_vars = len(values)
#     angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()
#     angles += angles[:1]
#     values_plot = values.copy()
#     values_plot += values_plot[:1]

#     fig, ax = plt.subplots(figsize=(6, 6), subplot_kw=dict(polar=True))
#     ax.plot(angles, values_plot, color="green", linewidth=2, linestyle="solid")
#     ax.fill(angles, values_plot, color="green", alpha=0.25)
#     ax.set_xticks(angles[:-1])
#     ax.set_xticklabels(labels, fontsize=10)
#     ax.set_title("User Gaze Regions Radar Chart", fontsize=14)
#     if active_count is not None:
#         ax.text(
#             0.05,
#             0.95,
#             f"Active Regions: {active_count}",
#             transform=ax.transAxes,
#             fontsize=12,
#             verticalalignment="top",
#             bbox=dict(boxstyle="round", alpha=0.1),
#         )
#     return ax


# def plot_region_bar(region_values):
#     labels = list(region_values.keys())
#     values = list(region_values.values())
#     fig, ax = plt.subplots(figsize=(8, 4))
#     ax.bar(labels, values, color="skyblue")
#     ax.set_title("Region Average Blendshape Values", fontsize=14)
#     ax.set_ylabel("Average Value")
#     ax.set_ylim(0, max(values) * 1.1)
#     return ax


# def plot_region_pie(region_values):
#     labels = list(region_values.keys())
#     values = list(region_values.values())
#     fig, ax = plt.subplots(figsize=(6, 6))
#     wedges, texts, autotexts = ax.pie(
#         values,
#         labels=labels,
#         autopct="%1.1f%%",
#         startangle=140,
#         textprops=dict(color="w"),
#     )
#     ax.set_title("Region Contribution (%)", fontsize=14)
#     return ax


# if __name__ == "__main__":
#     csv_file_path = "top-bottom.csv"
#     df = pd.read_csv(csv_file_path)

#     # Select a sample row; for example, the first row.
#     sample = df.iloc[0].to_dict()

#     # Aggregate gaze (blendshape) data into regions.
#     region_values = aggregate_regions(sample)
#     active_regions = count_active_regions(region_values, threshold=0.5)
#     print(f"User active gaze regions count: {active_regions}")

#     # Create multiple charts for better data understanding.
#     # Radar Chart
#     ax1 = plot_region_radar(region_values, active_count=active_regions)
#     # Bar Chart
#     ax2 = plot_region_bar(region_values)
#     # Pie Chart
#     ax3 = plot_region_pie(region_values)

#     plt.show()


# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------
# -----------------------------------------------------------------------------------

# import pandas as pd
# import numpy as np
# import matplotlib.pyplot as plt
# from io import StringIO

# # Read CSV data, skipping lines that begin with "//"
# with open("output.csv", "r") as f:
#     lines = [line for line in f if not line.startswith("//")]
# data = "".join(lines)
# df = pd.read_csv(StringIO(data))

# # Convert time column to datetime (assuming format "HH:MM:SS.mmm")
# df["time_dt"] = pd.to_datetime(df["time"], format="%H:%M:%S.%f")

# # Define the time window: between 15 and 20 minutes. Adjust hour if needed.
# start_time = pd.to_datetime("17:09:00.000", format="%H:%M:%S.%f")
# end_time = pd.to_datetime("17:10:00.000", format="%H:%M:%S.%f")
# df_window = df[(df["time_dt"] >= start_time) & (df["time_dt"] < end_time)]

# # Define keys for eye signals
# left_eye_keys = ["EYE_LOOK_IN_LEFT", "EYE_LOOK_DOWN_LEFT", "EYE_LOOK_UP_LEFT"]
# right_eye_keys = ["EYE_LOOK_IN_RIGHT", "EYE_LOOK_DOWN_RIGHT", "EYE_LOOK_UP_RIGHT"]
# top_eye_keys = ["EYE_LOOK_UP_LEFT", "EYE_LOOK_UP_RIGHT"]
# bottom_eye_keys = ["EYE_LOOK_DOWN_LEFT", "EYE_LOOK_DOWN_RIGHT"]


# def compute_combined_coordinate(row, w_eye=0.5, w_head=0.5):
#     """
#     Compute a combined coordinate using:
#       - Eye gaze: horizontal = average(right_eye) - average(left_eye)
#                   vertical   = average(top_eye) - average(bottom_eye)
#       - Head movement (as an example):
#                   head_yaw = MOUTH_RIGHT - MOUTH_LEFT
#                   head_roll = MOUTH_ROLL_UPPER - MOUTH_ROLL_LOWER
#       The final coordinate is:
#           x = w_eye * (x_eye) + w_head * (head_yaw)
#           y = w_eye * (y_eye) + w_head * (head_roll)
#     """
#     left_avg = np.mean([float(row[k]) for k in left_eye_keys])
#     right_avg = np.mean([float(row[k]) for k in right_eye_keys])
#     x_eye = right_avg - left_avg

#     top_avg = np.mean([float(row[k]) for k in top_eye_keys])
#     bottom_avg = np.mean([float(row[k]) for k in bottom_eye_keys])
#     y_eye = top_avg - bottom_avg

#     try:
#         head_yaw = float(row["MOUTH_RIGHT"]) - float(row["MOUTH_LEFT"])
#     except KeyError:
#         head_yaw = 0.0

#     try:
#         head_roll = float(row["MOUTH_ROLL_UPPER"]) - float(row["MOUTH_ROLL_LOWER"])
#     except KeyError:
#         head_roll = 0.0

#     combined_x = w_eye * x_eye + w_head * head_yaw
#     combined_y = w_eye * y_eye + w_head * head_roll
#     return combined_x, combined_y


# # Compute coordinates for each sample in the filtered window
# combined_xs = []
# combined_ys = []
# for _, row in df_window.iterrows():
#     x, y = compute_combined_coordinate(row)
#     combined_xs.append(x)
#     combined_ys.append(y)
# df_window["combined_x"] = combined_xs
# df_window["combined_y"] = combined_ys

# plt.figure(figsize=(8, 8))
# plt.scatter(df_window["combined_x"], df_window["combined_y"], c="blue", alpha=0.6, s=30)
# plt.xlabel("Combined Horizontal Coordinate")
# plt.ylabel("Combined Vertical Coordinate")
# plt.title("Combined Eye Gaze & Head Movement Coordinates")

# # Draw a red dashed box representing a "neutral" area (adjust threshold as needed)
# # threshold = 0.1
# # plt.axvline(threshold, color="red", linestyle="--", linewidth=1)
# # plt.axvline(-threshold, color="red", linestyle="--", linewidth=1)
# # plt.axhline(threshold, color="red", linestyle="--", linewidth=1)
# # plt.axhline(-threshold, color="red", linestyle="--", linewidth=1)

# plt.grid(True)
# plt.show()

# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------


# import pandas as pd
# import numpy as np
# import matplotlib.pyplot as plt
# from io import StringIO

# # -----------------------------------------------------------------
# # 1. Read and prepare the data (same as before)
# # -----------------------------------------------------------------
# with open("output.csv", "r") as f:
#     lines = [line for line in f if not line.startswith("//")]
# data = "".join(lines)
# df = pd.read_csv(StringIO(data))

# # Convert time column to datetime objects (adjust format as needed)
# df["time_dt"] = pd.to_datetime(df["time"], format="%H:%M:%S.%f")

# # Define the time window (adjust as desired)
# start_time = pd.to_datetime("17:09:00.000", format="%H:%M:%S.%f")
# end_time = pd.to_datetime("17:10:00.000", format="%H:%M:%S.%f")
# df_window = df[(df["time_dt"] >= start_time) & (df["time_dt"] < end_time)].copy()

# # Define keys for eye signals (for gaze component)
# left_eye_keys = ["EYE_LOOK_IN_LEFT", "EYE_LOOK_DOWN_LEFT", "EYE_LOOK_UP_LEFT"]
# right_eye_keys = ["EYE_LOOK_IN_RIGHT", "EYE_LOOK_DOWN_RIGHT", "EYE_LOOK_UP_RIGHT"]
# top_eye_keys = ["EYE_LOOK_UP_LEFT", "EYE_LOOK_UP_RIGHT"]
# bottom_eye_keys = ["EYE_LOOK_DOWN_LEFT", "EYE_LOOK_DOWN_RIGHT"]


# # -----------------------------------------------------------------
# # 2. Compute a combined (small-angle) deviation (in radians)
# #    We use a weighted sum from eye signals and head signals.
# # -----------------------------------------------------------------
# def compute_combined_angle(row, w_eye=0.6, w_head=0.4):
#     # Eye-based horizontal deviation:
#     left_avg = np.mean([float(row[k]) for k in left_eye_keys])
#     right_avg = np.mean([float(row[k]) for k in right_eye_keys])
#     angle_x_eye = left_avg - right_avg  # positive => turning left

#     # Eye-based vertical deviation:
#     top_avg = np.mean([float(row[k]) for k in top_eye_keys])
#     bottom_avg = np.mean([float(row[k]) for k in bottom_eye_keys])
#     angle_y_eye = top_avg - bottom_avg  # positive => looking up

#     # Head-based components (from mouth features as a proxy)
#     try:
#         head_yaw = float(row["MOUTH_RIGHT"]) - float(row["MOUTH_LEFT"])
#     except KeyError:
#         head_yaw = 0.0

#     try:
#         head_roll = float(row["MOUTH_ROLL_UPPER"]) - float(row["MOUTH_ROLL_LOWER"])
#     except KeyError:
#         head_roll = 0.0

#     combined_angle_x = w_eye * angle_x_eye + w_head * head_yaw
#     combined_angle_y = w_eye * angle_y_eye + w_head * head_roll
#     return combined_angle_x, combined_angle_y


# angles_x = []
# angles_y = []
# for _, row in df_window.iterrows():
#     ax, ay = compute_combined_angle(row)
#     angles_x.append(ax)
#     angles_y.append(ay)
# df_window["angle_x"] = angles_x
# df_window["angle_y"] = angles_y

# # -----------------------------------------------------------------
# # 3. Map angles to intersection coordinates using a ray-plane approximation.
# #    For small angles: X = D * angle_x, Y = D * angle_y.
# # -----------------------------------------------------------------
# screen_distance = 0.6  # in meters (parameter can be adjusted)
# screen_x = screen_distance * np.array(angles_x)
# screen_y = screen_distance * np.array(angles_y)
# df_window["screen_x"] = screen_x
# df_window["screen_y"] = screen_y

# # ---------------screen_distance--------------------------------------------------
# # 4. Visualize the data without imposing any predefined screen size.
# # -----------------------------------------------------------------

# # Scatter Plot: Shows raw intersection coordinates.
# plt.figure(figsize=(10, 8))
# plt.scatter(df_window["screen_x"], df_window["screen_y"], c="purple", alpha=0.6, s=30)
# plt.xlabel("Screen X (meters)")
# plt.ylabel("Screen Y (meters)")
# plt.title("Estimated Gaze Intersections")
# plt.grid(True)
# plt.show()

# # 2D Histogram (Heatmap): Visualizes density of gaze points.
# plt.figure(figsize=(10, 8))
# plt.hist2d(df_window["screen_x"], df_window["screen_y"], bins=30, cmap="viridis")
# plt.colorbar(label="Counts")
# plt.xlabel("Screen X (meters)")
# plt.ylabel("Screen Y (meters)")
# plt.title("2D Density of Gaze Intersections")
# plt.grid(True)
# plt.show()


# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------


# import pandas as pd
# import numpy as np
# import matplotlib.pyplot as plt
# from io import StringIO

# # Read CSV data, skipping lines that begin with "//"
# with open("output.csv", "r") as f:
#     lines = [line for line in f if not line.startswith("//")]
# data = "".join(lines)
# df = pd.read_csv(StringIO(data))

# # Convert time column to datetime (assuming format "HH:MM:SS.mmm")
# df["time_dt"] = pd.to_datetime(df["time"], format="%H:%M:%S.%f")

# # Define the time window: between 15 and 20 minutes. Adjust hour if needed.
# start_time = pd.to_datetime("17:05:00.000", format="%H:%M:%S.%f")
# end_time = pd.to_datetime("17:07:00.000", format="%H:%M:%S.%f")
# df_window = df[(df["time_dt"] >= start_time) & (df["time_dt"] < end_time)]


# left_eye_keys = [
#     "EYE_LOOK_IN_LEFT",
# ]
# right_eye_keys = [
#     "EYE_LOOK_IN_RIGHT",
# ]

# # left_eye_keys = [
# #     "EYE_LOOK_IN_LEFT",
# #     "EYE_LOOK_DOWN_LEFT",
# #     "EYE_LOOK_UP_LEFT",
# #     "EYE_WIDE_LEFT",
# # ]
# # right_eye_keys = [
# #     "EYE_LOOK_IN_RIGHT",
# #     "EYE_LOOK_DOWN_RIGHT",
# #     "EYE_LOOK_UP_RIGHT",
# #     "EYE_WIDE_RIGHT",
# # ]

# top_eye_keys = ["EYE_LOOK_UP_LEFT", "EYE_LOOK_UP_RIGHT"]
# bottom_eye_keys = ["EYE_LOOK_DOWN_LEFT", "EYE_LOOK_DOWN_RIGHT"]


# def compute_combined_coordinate(row):
#     left_avg = np.mean([float(row[k]) for k in left_eye_keys])
#     right_avg = np.mean([float(row[k]) for k in right_eye_keys])
#     x_eye = right_avg - left_avg

#     top_avg = np.mean([float(row[k]) for k in top_eye_keys])
#     bottom_avg = np.mean([float(row[k]) for k in bottom_eye_keys])
#     y_eye = top_avg - bottom_avg

#     return x_eye, y_eye


# combined_xs = []
# combined_ys = []
# for _, row in df_window.iterrows():
#     x, y = compute_combined_coordinate(row)
#     combined_xs.append(x)
#     combined_ys.append(y)
# df_window["combined_x"] = combined_xs
# df_window["combined_y"] = combined_ys

# plt.figure(figsize=(8, 8))
# plt.scatter(df_window["combined_x"], df_window["combined_y"], c="blue", alpha=0.6, s=30)
# plt.xlabel("Combined Horizontal Coordinate")
# plt.ylabel("Combined Vertical Coordinate")
# plt.title("Combined Eye Gaze & Head Movement Coordinates")

# plt.grid(True)
# plt.show()


# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------


# import pandas as pd
# import numpy as np
# import matplotlib.pyplot as plt
# from io import StringIO
# from scipy.spatial import ConvexHull
# from sklearn.cluster import DBSCAN
# from scipy.stats import zscore


# def load_and_process_data(file_path, start_time_str, end_time_str):
#     """Load data from CSV and process it to get eye coordinates."""
#     # Read CSV data, skipping lines that begin with "//"
#     with open(file_path, "r") as f:
#         lines = [line for line in f if not line.startswith("//")]
#     data = "".join(lines)
#     df = pd.read_csv(StringIO(data))

#     # Convert time column to datetime
#     df["time_dt"] = pd.to_datetime(df["time"], format="%H:%M:%S.%f")

#     # Filter by time window
#     start_time = pd.to_datetime(start_time_str, format="%H:%M:%S.%f")
#     end_time = pd.to_datetime(end_time_str, format="%H:%M:%S.%f")
#     df_window = df[(df["time_dt"] >= start_time) & (df["time_dt"] < end_time)]

#     return df_window


# def compute_eye_coordinates(df):
#     """Compute eye gaze coordinates for each row in dataframe."""
#     left_eye_keys = ["EYE_LOOK_IN_LEFT"]
#     right_eye_keys = ["EYE_LOOK_IN_RIGHT"]
#     top_eye_keys = ["EYE_LOOK_UP_LEFT", "EYE_LOOK_UP_RIGHT"]
#     bottom_eye_keys = ["EYE_LOOK_DOWN_LEFT", "EYE_LOOK_DOWN_RIGHT"]

#     combined_xs = []
#     combined_ys = []

#     for _, row in df.iterrows():
#         left_avg = np.mean([float(row[k]) for k in left_eye_keys if k in row])
#         right_avg = np.mean([float(row[k]) for k in right_eye_keys if k in row])
#         x_eye = right_avg - left_avg

#         top_avg = np.mean([float(row[k]) for k in top_eye_keys if k in row])
#         bottom_avg = np.mean([float(row[k]) for k in bottom_eye_keys if k in row])
#         y_eye = top_avg - bottom_avg

#         combined_xs.append(x_eye)
#         combined_ys.append(y_eye)

#     return combined_xs, combined_ys


# def remove_outliers(xs, ys, threshold=2):
#     """Remove outliers using z-score method."""
#     x_scores = np.abs(zscore(xs))
#     y_scores = np.abs(zscore(ys))

#     mask = (x_scores < threshold) & (y_scores < threshold)
#     return np.array(xs)[mask], np.array(ys)[mask]


# def remove_outliers_using_clustering(xs, ys, eps=0.05, min_samples=10):
#     """
#     Remove outliers using DBSCAN clustering.

#     Args:
#         xs, ys: Coordinate arrays
#         eps: Maximum distance between points to be considered neighbors
#         min_samples: Minimum number of points required to form a dense region

#     Returns:
#         Filtered coordinate arrays with outliers removed
#     """
#     # Combine coordinates into a single array of points
#     points = np.vstack([xs, ys]).T

#     # Apply DBSCAN clustering
#     db = DBSCAN(eps=eps, min_samples=min_samples).fit(points)
#     labels = db.labels_

#     # Filter out noise points (labeled as -1 by DBSCAN)
#     mask = labels != -1
#     return points[mask, 0], points[mask, 1]


# def create_polygon(xs, ys):
#     """Create a convex hull polygon from coordinates."""
#     points = np.vstack([xs, ys]).T
#     hull = ConvexHull(points)
#     return points[hull.vertices]


# # Process tutorial data
# tutorial_df = load_and_process_data(
#     "s6lz-l9eo-sh9c_tut.csv", "22:00:00.000", "22:06:00.000"
# )
# tutorial_xs, tutorial_ys = compute_eye_coordinates(tutorial_df)

# # First pass: Remove extreme outliers with z-score
# clean_xs, clean_ys = remove_outliers(tutorial_xs, tutorial_ys, threshold=4)

# # Second pass: Use clustering to remove points outside of clusters
# clean_xs, clean_ys = remove_outliers_using_clustering(clean_xs, clean_ys)

# # Create polygon from clean data
# polygon = create_polygon(clean_xs, clean_ys)

# # Process interview data
# interview_df = load_and_process_data(
#     "s6lz-l9eo-sh9c.csv", "22:04:40.000", "22:05:50.000"
# )
# interview_xs, interview_ys = compute_eye_coordinates(interview_df)

# # Visualize the data
# plt.figure(figsize=(12, 10))

# # Plot original and cleaned tutorial data
# plt.scatter(
#     tutorial_xs,
#     tutorial_ys,
#     c="green",
#     alpha=0.6,
#     s=20,
#     label="Original Tutorial Points",
# )
# plt.scatter(
#     clean_xs, clean_ys, c="blue", alpha=0.6, s=30, label="Cleaned Tutorial Points"
# )

# # Plot polygon from clean data
# plt.fill(
#     polygon[:, 0], polygon[:, 1], alpha=0.2, color="blue", label="Tutorial Data Region"
# )

# # Plot interview data points
# plt.scatter(
#     interview_xs, interview_ys, c="red", alpha=0.5, s=30, label="Interview Data"
# )

# # Add labels and title
# plt.xlabel("Horizontal Eye Coordinate")
# plt.ylabel("Vertical Eye Coordinate")
# plt.title("Eye Gaze Analysis with Cluster-Based Outlier Removal")
# plt.legend()
# plt.grid(True)

# plt.show()

# # Calculate how many interview points fall inside the polygon
# from matplotlib.path import Path

# polygon_path = Path(polygon)
# interview_points = np.vstack([interview_xs, interview_ys]).T
# inside_mask = polygon_path.contains_points(interview_points)
# percent_inside = 100 * np.sum(inside_mask) / len(interview_points)

# print(
#     f"Percentage of interview gaze points inside tutorial polygon: {percent_inside:.1f}%"
# )

# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------
# ----------------------------------------------------------------------------


import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, Button, TextBox
from io import StringIO
from scipy.spatial import ConvexHull
from sklearn.cluster import DBSCAN
from scipy.stats import zscore
from matplotlib.path import Path


def load_and_process_data(file_path, start_time_str, end_time_str):
    """Load data from CSV and process it to get eye coordinates."""
    # Read CSV data, skipping lines that begin with "//"
    with open(file_path, "r") as f:
        lines = [line for line in f if not line.startswith("//")]
    data = "".join(lines)
    df = pd.read_csv(StringIO(data))

    # Convert time column to datetime
    df["time_dt"] = pd.to_datetime(df["time"], format="%H:%M:%S.%f")

    # Filter by time window
    start_time = pd.to_datetime(start_time_str, format="%H:%M:%S.%f")
    end_time = pd.to_datetime(end_time_str, format="%H:%M:%S.%f")
    df_window = df[(df["time_dt"] >= start_time) & (df["time_dt"] < end_time)]

    return df_window


def compute_eye_coordinates(df):
    """Compute eye gaze coordinates for each row in dataframe."""
    left_eye_keys = ["EYE_LOOK_IN_LEFT"]
    right_eye_keys = ["EYE_LOOK_IN_RIGHT"]
    top_eye_keys = ["EYE_LOOK_UP_LEFT", "EYE_LOOK_UP_RIGHT"]
    bottom_eye_keys = ["EYE_LOOK_DOWN_LEFT", "EYE_LOOK_DOWN_RIGHT"]

    combined_xs = []
    combined_ys = []

    for _, row in df.iterrows():
        left_avg = np.mean([float(row[k]) for k in left_eye_keys if k in row])
        right_avg = np.mean([float(row[k]) for k in right_eye_keys if k in row])
        x_eye = right_avg - left_avg

        top_avg = np.mean([float(row[k]) for k in top_eye_keys if k in row])
        bottom_avg = np.mean([float(row[k]) for k in bottom_eye_keys if k in row])
        y_eye = top_avg - bottom_avg

        combined_xs.append(x_eye)
        combined_ys.append(y_eye)

    return combined_xs, combined_ys


def remove_outliers(xs, ys, threshold=4):
    """Remove outliers using z-score method."""
    if len(xs) == 0 or len(ys) == 0:
        return np.array([]), np.array([])

    x_scores = np.abs(zscore(xs))
    y_scores = np.abs(zscore(ys))

    mask = (x_scores < threshold) & (y_scores < threshold)
    return np.array(xs)[mask], np.array(ys)[mask]


def remove_outliers_using_clustering(xs, ys, eps=0.05, min_samples=15):
    """
    Remove outliers using DBSCAN clustering.

    Args:
        xs, ys: Coordinate arrays
        eps: Maximum distance between points to be considered neighbors
        min_samples: Minimum number of points required to form a dense region

    Returns:
        Filtered coordinate arrays with outliers removed
    """
    if len(xs) < min_samples or len(ys) < min_samples:
        return xs, ys

    # Combine coordinates into a single array of points
    points = np.vstack([xs, ys]).T

    # Apply DBSCAN clustering
    db = DBSCAN(eps=eps, min_samples=min_samples).fit(points)
    labels = db.labels_

    # Filter out noise points (labeled as -1 by DBSCAN)
    mask = labels != -1
    return points[mask, 0], points[mask, 1]


def create_polygon(xs, ys):
    """Create a convex hull polygon from coordinates."""
    if len(xs) < 3 or len(ys) < 3:
        return np.array([[0, 0], [0, 0], [0, 0]])  # Default triangle

    points = np.vstack([xs, ys]).T
    hull = ConvexHull(points)
    return points[hull.vertices]


# Load full interview dataset once for better performance
def load_full_interview_data(file_path):
    with open(file_path, "r") as f:
        lines = [line for line in f if not line.startswith("//")]
    data = "".join(lines)
    df = pd.read_csv(StringIO(data))
    df["time_dt"] = pd.to_datetime(df["time"], format="%H:%M:%S.%f")
    return df


# Process tutorial data (only needs to be done once)
tutorial_df = load_full_interview_data("k1.csv")
tutorial_xs, tutorial_ys = compute_eye_coordinates(tutorial_df)

# First pass: Remove extreme outliers with z-score
clean_xs, clean_ys = remove_outliers(tutorial_xs, tutorial_ys, threshold=6)

# Second pass: Use clustering to remove points outside of clusters
clean_xs, clean_ys = remove_outliers_using_clustering(clean_xs, clean_ys)

# Create polygon from clean data
polygon = create_polygon(clean_xs, clean_ys)
polygon_path = Path(polygon)

# Load full interview data
interview_file = "k2.csv"
full_interview_df = load_full_interview_data(interview_file)

# Get min and max datetime from the data for slider bounds
min_time = full_interview_df["time_dt"].min()
max_time = full_interview_df["time_dt"].max()


# Convert to seconds since midnight for sliders
def time_to_seconds(dt):
    return dt.hour * 3600 + dt.minute * 60 + dt.second + dt.microsecond / 1000000


def seconds_to_time_str(seconds):
    h = int(seconds // 3600)
    m = int((seconds % 3600) // 60)
    s = seconds % 60
    return f"{h:02d}:{m:02d}:{s:06.3f}"


min_seconds = time_to_seconds(min_time.time())
max_seconds = time_to_seconds(max_time.time())

# Create the interactive plot
fig, ax = plt.subplots(figsize=(12, 10))
plt.subplots_adjust(bottom=0.25)  # Make room for sliders

# Make sure initial values are within bounds
initial_start = min_seconds
initial_end = min_seconds + 60

# Plot tutorial data (fixed)
tutorial_scatter = ax.scatter(
    tutorial_xs,
    tutorial_ys,
    c="green",
    alpha=0.2,
    s=20,
    label="Original Tutorial Points",
)
clean_tutorial_scatter = ax.scatter(
    clean_xs, clean_ys, c="blue", alpha=0.2, s=30, label="Cleaned Tutorial Points"
)

# Plot polygon from clean data
polygon_plot = ax.fill(
    polygon[:, 0], polygon[:, 1], alpha=0.2, color="blue", label="Tutorial Data Region"
)

# Initialize with empty interview data1
interview_scatter = ax.scatter([], [], c="red", alpha=0.5, s=30, label="Interview Data")

# Add text for percentage
percentage_text = ax.text(
    0.02,
    0.96,
    "",
    transform=ax.transAxes,
    fontsize=12,
    bbox=dict(facecolor="white", alpha=0.7),
)

# Set up axes
ax.set_xlabel("Horizontal Eye Coordinate")
ax.set_ylabel("Vertical Eye Coordinate")
ax.set_title("Eye Gaze Analysis with Cluster-Based Outlier Removal")
ax.legend()
ax.grid(True)

# Create sliders
ax_start = plt.axes([0.15, 0.12, 0.65, 0.03])
ax_end = plt.axes([0.15, 0.07, 0.65, 0.03])
ax_offset_text = plt.axes([0.15, 0.02, 0.45, 0.03])
offset_textbox = TextBox(ax_offset_text, "Time Offset (sec): ", initial="0")
apply_offset_ax = plt.axes([0.65, 0.02, 0.15, 0.03])

apply_offset_button = Button(apply_offset_ax, "Apply Offset")
start_slider = Slider(
    ax_start, "Start Time", min_seconds, max_seconds, valinit=initial_start, valstep=0.1
)
end_slider = Slider(
    ax_end, "End Time", min_seconds, max_seconds, valinit=initial_end, valstep=0.1
)

# Create reset button
reset_ax = plt.axes([0.85, 0.12, 0.1, 0.03])
reset_button = Button(reset_ax, "Reset")

# Labels to show current time values
time_label_ax = plt.axes([0.15, 0.17, 0.65, 0.03])
time_label_ax.axis("off")
time_label = time_label_ax.text(
    0.5,
    0.5,
    f"Window: {seconds_to_time_str(initial_start)} - {seconds_to_time_str(initial_end)}",
    ha="center",
    va="center",
)

current_offset = 0


def apply_offset(event=None):
    """Apply the offset from the text input"""
    global current_offset
    try:
        # Get value from text box and convert to float
        offset_value = float(offset_textbox.text)
        current_offset = offset_value
        # Update the plot
        update(None)
    except ValueError:
        # If input is not a valid number, reset to 0
        offset_textbox.set_val("0")
        current_offset = 0
        update(None)


def update(val):
    """Update the plot when sliders change"""
    start_sec = start_slider.val
    end_sec = end_slider.val
    time_offset = current_offset

    # Ensure end is always after start
    if end_sec <= start_sec:
        end_sec = start_sec + 0.1
        end_slider.set_val(end_sec)

    # Convert slider values to time strings
    start_time_str = seconds_to_time_str(start_sec)
    end_time_str = seconds_to_time_str(end_sec)

    # Update the time label
    time_label.set_text(f"Window: {start_time_str} - {end_time_str}")

    # Filter data based on new time window
    start_time = pd.to_datetime(start_time_str, format="%H:%M:%S.%f")
    end_time = pd.to_datetime(end_time_str, format="%H:%M:%S.%f")

    start_time = start_time + pd.Timedelta(seconds=time_offset)
    end_time = end_time + pd.Timedelta(seconds=time_offset)

    # Filter data based on new time window with offset applied
    df_window = full_interview_df[
        (full_interview_df["time_dt"] >= start_time)
        & (full_interview_df["time_dt"] < end_time)
    ]

    # Calculate new coordinates
    if not df_window.empty:
        interview_xs, interview_ys = compute_eye_coordinates(df_window)

        # Update scatter plot
        interview_scatter.set_offsets(np.column_stack([interview_xs, interview_ys]))

        # Calculate percentage inside polygon
        interview_points = np.vstack([interview_xs, interview_ys]).T
        inside_mask = polygon_path.contains_points(interview_points)
        percent_inside = (
            100 * np.sum(inside_mask) / len(interview_points)
            if len(interview_points) > 0
            else 0
        )

        # Update percentage text
        percentage_text.set_text(f"Inside polygon: {percent_inside:.1f}%")
    else:
        interview_scatter.set_offsets(np.empty((0, 2)))
        percentage_text.set_text("No data in selected range")

    fig.canvas.draw_idle()


def reset(event):
    """Reset sliders to initial values"""
    start_slider.set_val(initial_start)
    end_slider.set_val(initial_end)


# Connect callbacks
start_slider.on_changed(update)
end_slider.on_changed(update)
reset_button.on_clicked(reset)
apply_offset_button.on_clicked(apply_offset)
offset_textbox.on_submit(apply_offset)
# Initial update
update(None)

plt.show()

# Print final percentage (for the last selected window)
if "interview_points" in locals() and "inside_mask" in locals():
    percent_inside = (
        100 * np.sum(inside_mask) / len(interview_points)
        if len(interview_points) > 0
        else 0
    )
    print(
        f"Percentage of interview gaze points inside tutorial polygon: {percent_inside:.1f}%"
    )
