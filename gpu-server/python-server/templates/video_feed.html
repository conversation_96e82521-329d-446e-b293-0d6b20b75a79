<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Video Feed for Room {{ room_id }}</title>
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet" />
		<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3"></script>
		<style>
			body {
				background-color: white;
			}
			.fullscreen {
				position: fixed;
				top: 0;
				left: 0;
				width: 100vw;
				height: 100vh;
				z-index: 1000;
				background-color: white;
			}
			#chart-container {
				background-color: white;
			}
			.container-fullscreen {
				width: 90%;
				height: 90%;
			}
		</style>
	</head>

	<body>
		<div class="w-full mt-1">
			<div class="row">
				<div class="col-md-5">
					<img id="processed-video" src="/video_feed/{{ room_id }}" alt="Video Feed" class="img-fluid" />
					<div class="col">
						<div class="row mt-4">
							<div class="col col-md-4">
								<button class="btn btn-primary mb-3" onclick="toggleIsCheating()">Toggle Is Cheating</button>
								<p id="ischeating-status">Is Cheating: 0</p>
							</div>
							<div class="col col-md-4">
								<button class="btn btn-primary mb-3" onclick="toggleIsMurmuring()">Toggle Is Murmuring</button>
								<p id="ismurmuring-status">Is Murmuring: 0</p>
							</div>
							<div class="col-md-3">
								<button class="btn btn-warning mb-3" onclick="sendCheatAlert()">Cheat Alert</button>
							</div>
							<div class="col col-md-5">
								<div class="col">
									<button class="btn btn-primary mb-3" onclick="joinRoom('False')">Join Room</button>
									<button class="btn btn-danger mb-3" onclick="joinRoom('True')">Force Join</button>
								</div>
								<p id="join-room"></p>
							</div>
						</div>
						<div class="row mt-5">
							<div class="col">
								<label for="y-axis-limit">Set Y-Axis Max:</label>
								<input type="number" id="y-axis-limit" class="form-control" value="1" />
								<button class="btn btn-success mt-2" onclick="updateYAxisLimit()">Update Y-Axis</button>
							</div>
							<div class="col">
								<button id="cleanup-button" class="btn btn-danger mt-4" onclick="cleanUpData()">Clean Up Old Data</button>
							</div>
							<div class="col">
								<button class="btn btn-primary mt-4" onclick="toggleFullscreen()">Full-Screen Chart</button>
							</div>
						</div>
					</div>
				</div>
				<div class="col-md-7">
					<div id="chart-container">
						<canvas id="blendshapeChart" width="100%" height="70px"></canvas>
					</div>
					<div class="col">
						<label for="blendshapes">Select Blendshape:</label>
						<select id="blendshapes" multiple class="form-select" style="height: 150px">
							<option value="_neutral">_neutral</option>
							<option value="eyeBlinkLeft">eyeBlinkLeft</option>
							<option value="eyeBlinkRight">eyeBlinkRight</option>
							<option value="eyeLookDownLeft">eyeLookDownLeft</option>
							<option value="eyeLookDownRight">eyeLookDownRight</option>
							<option value="eyeLookInLeft">eyeLookInLeft</option>
							<option value="eyeLookInRight">eyeLookInRight</option>
							<option value="eyeLookOutLeft">eyeLookOutLeft</option>
							<option value="eyeLookOutRight">eyeLookOutRight</option>
							<option value="eyeLookUpLeft">eyeLookUpLeft</option>
							<option value="eyeLookUpRight">eyeLookUpRight</option>
							<option value="eyeSquintLeft">eyeSquintLeft</option>
							<option value="eyeSquintRight">eyeSquintRight</option>
							<option value="eyeWideLeft">eyeWideLeft</option>
							<option value="eyeWideRight">eyeWideRight</option>
							<option value="browDownLeft">browDownLeft</option>
							<option value="browDownRight">browDownRight</option>
							<option value="browInnerUp">browInnerUp</option>
							<option value="browOuterUpLeft">browOuterUpLeft</option>
							<option value="browOuterUpRight">browOuterUpRight</option>
							<option value="cheekPuff">cheekPuff</option>
							<option value="cheekSquintLeft">cheekSquintLeft</option>
							<option value="cheekSquintRight">cheekSquintRight</option>
							<option value="jawForward">jawForward</option>
							<option value="jawLeft">jawLeft</option>
							<option value="jawOpen">jawOpen</option>
							<option value="jawRight">jawRight</option>
							<option value="mouthClose">mouthClose</option>
							<option value="mouthDimpleLeft">mouthDimpleLeft</option>
							<option value="mouthDimpleRight">mouthDimpleRight</option>
							<option value="mouthFrownLeft">mouthFrownLeft</option>
							<option value="mouthFrownRight">mouthFrownRight</option>
							<option value="mouthFunnel">mouthFunnel</option>
							<option value="mouthLeft">mouthLeft</option>
							<option value="mouthLowerDownLeft">mouthLowerDownLeft</option>
							<option value="mouthLowerDownRight">mouthLowerDownRight</option>
							<option value="mouthPressLeft">mouthPressLeft</option>
							<option value="mouthPressRight">mouthPressRight</option>
							<option value="mouthPucker">mouthPucker</option>
							<option value="mouthRight">mouthRight</option>
							<option value="mouthRollLower">mouthRollLower</option>
							<option value="mouthRollUpper">mouthRollUpper</option>
							<option value="mouthShrugLower">mouthShrugLower</option>
							<option value="mouthShrugUpper">mouthShrugUpper</option>
							<option value="mouthSmileLeft">mouthSmileLeft</option>
							<option value="mouthSmileRight">mouthSmileRight</option>
							<option value="mouthStretchLeft">mouthStretchLeft</option>
							<option value="mouthStretchRight">mouthStretchRight</option>
							<option value="mouthUpperUpLeft">mouthUpperUpLeft</option>
							<option value="mouthUpperUpRight">mouthUpperUpRight</option>
							<option value="noseSneerLeft">noseSneerLeft</option>
							<option value="noseSneerRight">noseSneerRight</option>
						</select>
					</div>
				</div>
			</div>
		</div>
		<script>
			const ctx = document.getElementById("blendshapeChart").getContext("2d");
			const chart = new Chart(ctx, {
				type: "line",
				data: {
					labels: [],
					datasets: [],
				},
				options: {
					layout: {
						padding: 0,
					},
					scales: {
						x: {
							type: "time",
							title: { display: false, text: "Time" },
						},
						y: {
							min: 0,
							max: 0.8,
							title: { display: false, text: "Blendshape Value" },
						},
					},
				},
			});

			function updateDatasets(selectedBlendshapes) {
				chart.data.datasets = chart.data.datasets.filter((dataset) => selectedBlendshapes.includes(dataset.label));

				selectedBlendshapes.forEach((blendshape) => {
					if (!chart.data.datasets.find((dataset) => dataset.label === blendshape)) {
						chart.data.datasets.push({
							label: blendshape,
							data: [],
							borderColor: getRandomColor(),
							fill: false,
						});
					}
				});

				chart.update();
			}

			document.getElementById("blendshapes").addEventListener("change", () => {
				const selectedBlendshapes = Array.from(document.getElementById("blendshapes").selectedOptions).map((option) => option.value);
				updateDatasets(selectedBlendshapes);
			});

			const ws = new WebSocket("/get_data/{{ room_id }}");
			ws.onmessage = (event) => {
				const data = JSON.parse(event.data);
				const timestamp = new Date();

				const selectedBlendshapes = Array.from(document.getElementById("blendshapes").selectedOptions).map((option) => option.value);

				updateDatasets(selectedBlendshapes);

				selectedBlendshapes.forEach((blendshape) => {
					updateChart(chart, blendshape, data[blendshape], timestamp);
				});

				cleanupOldData(chart, 210);
			};

			function updateChart(chart, blendshape, value, timestamp) {
				let dataset = chart.data.datasets.find((ds) => ds.label === blendshape);

				if (dataset) {
					dataset.data.push({ x: timestamp, y: value });
				}

				chart.update("none");
			}

			function cleanupOldData(chart, limit) {
				chart.data.datasets.forEach((dataset) => {
					if (dataset.data.length > limit) {
						dataset.data.splice(0, dataset.data.length - limit);
					}
				});
				chart.update("none");
			}

			function getRandomColor() {
				const letters = "0123456789ABCDEF";
				let color = "#";
				for (let i = 0; i < 6; i++) {
					color += letters[Math.floor(Math.random() * 16)];
				}
				return color;
			}

			function updateYAxisLimit() {
				const newYMax = Number(document.getElementById("y-axis-limit").value);
				chart.options.scales.y.max = newYMax;
				chart.update();
			}

			function cleanUpData() {
				chart.data.labels = [];
				chart.data.datasets.forEach((dataset) => {
					dataset.data = [];
				});
				chart.update();
			}

			function toggleFullscreen() {
				const chartContainer = document.getElementById("chart-container");
				if (!document.fullscreenElement) {
					chartContainer.requestFullscreen().catch((err) => console.error("Error attempting to enable fullscreen mode:", err));
				} else {
					document.exitFullscreen().catch((err) => console.error("Error attempting to exit fullscreen mode:", err));
				}
			}

			async function toggleIsCheating() {
				const response = await fetch(`/toggle_ischeating/{{ room_id }}`, { method: "POST" });
				const data = await response.json();
				document.getElementById("ischeating-status").innerText = `Is Cheating: ${data.isCheating}`;
			}

			async function toggleIsMurmuring() {
				const response = await fetch(`/toggle_ismurmuring/{{ room_id }}`, { method: "POST" });
				const data = await response.json();
				document.getElementById("ismurmuring-status").innerText = `Is Murmuring: ${data.isMurmuring}`;
			}
			async function sendCheatAlert() {
				await fetch(`/cheat_alert/{{ room_id }}`, { method: "POST" });
			}

			async function joinRoom(force_join) {
				const response = await fetch(`/internal/join-room/${force_join}`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						Authorization: `Bearer w0321lud6t47h9x4lahuwiivh2yhisirno5hy3ekvx5ztp57lxcyman1w3mfsxdvhtik8tgt02bb29t6q8ot52sn0wi8lb0ir2uvl1ijm3bpwnqmom4he00p6oq7of7pxauhlqhsyj0wksyipq8gczxhxt791dgfe7uhs4nav9vedft85eecdtn8h4ejewsmviglu0cl`,
					},
					body: JSON.stringify({ roomId: `{{ room_id }}` }),
				});
				const data = await response.json();
				document.getElementById("join-room").innerText = `join status: ${JSON.stringify(data)}`;
			}

			ws.onerror = (error) => console.error("WebSocket error:", error);
			ws.onclose = () => console.log("WebSocket closed");
		</script>
	</body>
</html>
