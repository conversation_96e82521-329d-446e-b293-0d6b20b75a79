const dotenv = require("dotenv");
const { getNodeEnv } = require("./utils/nodeEnvMap");
const path = require("path");

dotenv.config({
  path: path.resolve(
    process.cwd(),
    getNodeEnv(process.env.NODE_ENV || "development")
  ),
});

const RabbitMQManager = require("./utils/rbmq");
const { mergeService } = require("./services/mergeService");

async function main() {
  try {
    await RabbitMQManager.connect();
    console.log("Successfully connected to RabbitMQ");

    RabbitMQManager.consumeQueue(process.env.BG_PROCESS_QUEUE, mergeService);

    process.on("SIGINT", () => {
      console.log("Closing connection to RabbitMQ");
      RabbitMQManager.connection.close();
      process.exit(0);
    });
  } catch (error) {
    console.error("Failed to set up RabbitMQ consumer:", error);
    process.exit(1);
  }
}

main();
