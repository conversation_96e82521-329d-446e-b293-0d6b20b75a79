const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");
const { removeFile } = require("./deleteFiles");

const USE_GPU = process.env.FFMPEG_PROCESSOR === "GPU";

const concatVideoFiles = (file1, file2) => {
  return new Promise((resolve, reject) => {
    const video1 = path.join(process.env.RECORD_FILE_LOCATION_PATH, "outputs", file1);
    const video2 = path.join(process.env.RECORD_FILE_LOCATION_PATH, "outputs", file2);
    const output = path.join(process.env.RECORD_FILE_LOCATION_PATH, "outputs", `temp_${file1}`);

    if (!fs.existsSync(video1) || !fs.existsSync(video2)) {
      reject(new Error("One or both input files do not exist"));
      return;
    }

    let command = ['-y'];

    if (USE_GPU) {
      command = command.concat([
        '-hwaccel', 'cuda',
        '-hwaccel_output_format', 'cuda',
        '-i', video1,
        '-hwaccel', 'cuda',
        '-hwaccel_output_format', 'cuda',
        '-i', video2
      ]);
    } else {
      command = command.concat([
        '-i', video1,
        '-i', video2
      ]);
    }

    command = command.concat([
      '-filter_complex', '[0:v][0:a][1:v][1:a]concat=n=2:v=1:a=1[vout][aout]',
      '-map', '[vout]',
      '-map', '[aout]'
    ]);

    if (USE_GPU) {
      command.push(
        '-c:v', 'h264_nvenc',
        '-preset', 'p4',
      );
    } else {
      command.push('-c:v', 'libx264', '-preset', 'fast');
    }

    command.push(
      "-threads", "6",
      '-fpsmax', '30',
      '-vsync', 'vfr',
      '-movflags', '+faststart'
    );

    command.push(output);

    console.log("Executing ffmpeg command...");
    console.log("\n\nffmpeg", command.join(" "), "\n\n");

    const ffmpegProcess = spawn("ffmpeg", command);
    let errorLog = "";

    ffmpegProcess.stderr.on("data", (data) => {
      const message = data.toString();
      errorLog += message;

      if (!message.includes("frame=") && !message.includes("fps=")) {
        console.error(`FFmpeg error: ${message}`);
      }
    });

    ffmpegProcess.on("error", (err) => {
      reject(new Error(`Failed to start FFmpeg process: ${err.message}`));
    });

    ffmpegProcess.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`FFmpeg process failed (code ${code}): ${errorLog}`));
        return;
      }

      fs.rename(output, video1, (err) => {
        if (err) {
          reject(new Error(`Failed to rename output file: ${err.message}`));
          return;
        }
        removeFile(video2);
        resolve();
      });
    });
  });
};

module.exports = concatVideoFiles;