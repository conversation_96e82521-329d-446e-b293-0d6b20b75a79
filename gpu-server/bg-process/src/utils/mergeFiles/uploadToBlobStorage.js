const fs = require("fs");
const { BlobServiceClient } = require("@azure/storage-blob");

const getContainerClient = () => {
  const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
  const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME;
  const blobServiceClient =
    BlobServiceClient.fromConnectionString(connectionString);
  return blobServiceClient.getContainerClient(containerName);
};

const uploadToBlobStorage = async (audioFilePath) => {
  const blobName = audioFilePath.split("/").pop();
  console.log(`Uploading file to Azure Blob Storage: ${blobName}`);
  console.log(`file path: ${audioFilePath}`);
  try {
    const containerClient = getContainerClient();
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    const data = fs.readFileSync(audioFilePath);
    const uploadBlobResponse = await blockBlobClient.upload(data, data.length);
    console.log(`file uploaded successfully.`, uploadBlobResponse.requestId);
    return blockBlobClient.url;
  } catch (error) {
    console.error("Error uploading file to Azure Blob Storage:", error);
    throw error;
  }
};

module.exports = { uploadToBlobStorage };
