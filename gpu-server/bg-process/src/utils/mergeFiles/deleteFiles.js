const fs = require("fs");

function removeFile(filePath) {
  try {
    fs.unlinkSync(filePath);
    console.log(`Removed ${filePath}`);
  } catch (err) {
    console.log(`Error removing ${filePath}:`, err);
    throw err;
  }
}

function removeFolder(folderPath) {
  try {
    fs.rmSync(folderPath, { recursive: true, force: true });
    console.log(`Removed ${folderPath}`);
  } catch (err) {
    console.log(`Error removing ${folderPath}:`, err);
    throw err;
  }
}

module.exports = { removeFile, removeFolder };
