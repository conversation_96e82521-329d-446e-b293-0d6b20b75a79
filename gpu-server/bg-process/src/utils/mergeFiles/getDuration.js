const { promisify } = require("util");
const execFile = promisify(require("child_process").execFile);

const getDuration = async (filePath) => {
  try {
    const { stdout } = await execFile("ffprobe", [
      "-v",
      "error",
      "-show_entries",
      "format=duration",
      "-of",
      "default=noprint_wrappers=1:nokey=1",
      "-i",
      filePath,
    ]);
    return parseFloat(stdout);
  } catch (error) {
    console.error(`Error getting duration for ${filePath}:`, error);
    return 0;
  }
};

module.exports = { getDuration };
