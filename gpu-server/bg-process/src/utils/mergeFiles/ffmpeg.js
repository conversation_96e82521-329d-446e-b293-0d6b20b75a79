const fs = require("fs");
const path = require("path");
const { spawn } = require("child_process");
const { generateSegments } = require("./generateSegments");
const { getDuration } = require("./getDuration");

const USE_GPU = process.env.FFMPEG_PROCESSOR === "GPU";
const MAX_RETRY_COUNT = 3;

const mergeFiles = async (recordingFolderPath, outputFilePath, roomId) => {
  try {
    if (!fs.existsSync(recordingFolderPath)) {
      console.log("Recording folder does not exist.");
      return;
    }

    const files = fs.readdirSync(recordingFolderPath);
    const audioInputs = [];
    const screenInputs = [];
    const cameraInputs = [];

    console.log("Generating inputs for ffmpeg...");
    for (const file of files) {
      const filePath = path.join(recordingFolderPath, file);
      const timestamp = parseInt(file.split(".")[0].split("_")[2], 10);
      const duration = await getDuration(filePath);
      if (typeof duration !== "number" || isNaN(duration) || duration === 0) {
        console.log("Duration is", duration, "skipping file:", filePath);
        continue;
      }

      const fileData = {
        filePath,
        timestamp,
        duration,
        delay: 0,
      };

      if (file.includes("_mic_")) {
        audioInputs.push(fileData);
      } else if (file.includes("_screen_")) {
        screenInputs.push(fileData);
      } else if (file.includes("_camera_")) {
        cameraInputs.push(fileData);
      }
    }

    if (audioInputs.length === 0 && screenInputs.length === 0) {
      console.log("No valid audio or video files found.");
      return;
    }

    const earliestStart = Math.min(
      ...audioInputs.map((file) => file.timestamp),
      ...screenInputs.map((file) => file.timestamp),
      ...cameraInputs.map((file) => file.timestamp)
    );

    // Calculate delays
    [audioInputs, screenInputs, cameraInputs].forEach((inputs) => {
      inputs.forEach(
        (file) => (file.delay = (file.timestamp - earliestStart) / 1000)
      );
    });

    const totalDuration = Math.max(
      ...[...audioInputs, ...screenInputs, ...cameraInputs].map(
        (input) => input.delay + input.duration
      )
    );

    console.log("audioInputs:", audioInputs);
    console.log("screenInputs:", screenInputs);
    console.log("cameraInputs", cameraInputs);

    const screenSegments = generateSegments(
      screenInputs,
      totalDuration,
      "1280"
    );
    const cameraSegments = generateSegments(cameraInputs, totalDuration, "400");

    console.log("screenSegments:", screenSegments);
    console.log("cameraSegments:", cameraSegments);

    console.log(`Using ${USE_GPU ? 'GPU' : 'CPU'} for processing`);

    let command = ["-y"];

    if (USE_GPU) {
      command.push("-hwaccel", "cuda", "-hwaccel_output_format", "cuda");
    }

    let inputIndex = 0;
    let filterComplex = [];
    let audioMerge = [];
    let screenMerge = [];
    let cameraMerge = [];

    // Process audio inputs
    audioInputs.forEach((audio, index) => {
      command.push("-i", audio.filePath);
      filterComplex.push(
        `[${inputIndex}]adelay=${Math.round(audio.delay * 1000)}|${Math.round(
          audio.delay * 1000
        )}[a${index}]`
      );
      audioMerge.push(`[a${index}]`);
      inputIndex++;
    });

    // Merge audio
    if (audioMerge.length > 0) {
      filterComplex.push(
        `${audioMerge.join("")}amix=inputs=${audioMerge.length
        }:duration=longest,volume=2[aout]`
      );
    }

    // Process screen segments
    screenSegments.forEach((segment, index) => {
      if (segment.type === "input") {
        command.push("-i", segment.filePath);
        filterComplex.push(
          `[${inputIndex}]scale=w='min(1280\\,iw)':h='min(1024\\,ih)':force_original_aspect_ratio=1,` +
          `setsar=1:1,pad=1280:1024:(1280-iw*min(1280/iw\\,1024/ih))/2:(1024-ih*min(1280/iw\\,1024/ih))/2[s${index}]`
        );
        screenMerge.push(`[s${index}]`);
        inputIndex++;
      } else {
        // Blank segment
        command.push(
          "-f",
          "lavfi",
          "-i",
          `color=size=1280x1024:duration=${segment.duration}:rate=30:color=black`
        );
        filterComplex.push(
          `[${inputIndex}]trim=duration=${segment.duration}[s${index}]`
        );
        screenMerge.push(`[s${index}]`);
        inputIndex++;
      }
    });

    // Process camera segments
    cameraSegments.forEach((segment, index) => {
      if (segment.type === "input") {
        command.push("-i", segment.filePath);
        filterComplex.push(
          `[${inputIndex}]scale=400:-1:force_original_aspect_ratio=decrease,pad=400:1024:(ow-iw)/2:(oh-ih)/2[c${index}]`
        );
        cameraMerge.push(`[c${index}]`);
        inputIndex++;
      } else {
        // Blank segment
        command.push(
          "-f",
          "lavfi",
          "-i",
          `color=size=400x1024:duration=${segment.duration}:rate=30:color=black`
        );
        filterComplex.push(
          `[${inputIndex}]trim=duration=${segment.duration}[c${index}]`
        );
        cameraMerge.push(`[c${index}]`);
        inputIndex++;
      }
    });

    // concat screen segments
    if (screenMerge.length > 0) {
      filterComplex.push(
        `${screenMerge.join("")}concat=n=${screenMerge.length
        }:v=1:a=0[screenout]`
      );
    }

    // concat camera segments
    if (cameraMerge.length > 0) {
      filterComplex.push(
        `${cameraMerge.join("")}concat=n=${cameraMerge.length
        }:v=1:a=0[cameraout]`
      );
    }

    // stack screen and camera side by side
    filterComplex.push(`[screenout][cameraout]hstack=inputs=2[vout]`);

    command.push("-filter_complex", filterComplex.join(";"));

    command.push("-map", "[vout]", "-map", "[aout]");

    if (USE_GPU) {
      command.push(
        "-c:v",
        "h264_nvenc",
        "-preset",
        "p4",
        "-tune",
        "hq",
        "-rc",
        "vbr_hq",
        "-cq",
        "22",
        "-b:v",
        "0",
        "-bf",
        "2",
        "-spatial-aq",
        "0",
        "-temporal-aq",
        "0",
        "-aq-strength",
        "5",
        "-rc-lookahead",
        "10",
        "-i_qfactor",
        "0.75",
        "-b_qfactor",
        "1.2",
        "-level",
        "4.2",
      );
    } else {
      command.push(
        "-c:v",
        "libx264",
        "-preset",
        "fast", 
        "-crf",
        "23",
        "-level",
        "5.1",
      );
    }

    command.push(
      "-profile:v",
      "high",
      "-maxrate",
      "5M",
      "-bufsize",
      "10M"
    );

    command.push(
      "-threads",
      "6",
      "-c:a",
      "aac",
      "-b:a",
      "128k",
      "-movflags",
      "+faststart",
      "-fpsmax",
      "30",
      "-vsync",
      "vfr",
      "-shortest",
      outputFilePath
    );

    console.log("Executing ffmpeg command...");
    console.log("\n\nffmpeg", command.join(" "), "\n\n");

    const executeFFmpeg = async (retryCount = 0) => {
      return new Promise((resolve, reject) => {
        console.log(`FFmpeg attempt ${retryCount + 1}/${MAX_RETRY_COUNT + 1} for room ${roomId}`);

        const ffmpeg = spawn("ffmpeg", command);
        let errorOutput = "";

        ffmpeg.stderr.on("data", (data) => {
          errorOutput += data.toString();
        });

        ffmpeg.on("close", async (code) => {
          if (code !== 0) {
            if (errorOutput) {
              console.error(`Error output (attempt ${retryCount + 1}): ${errorOutput}`);
            }

            if (retryCount < MAX_RETRY_COUNT) {
              console.log(`FFmpeg failed with code ${code}. Retrying (${retryCount + 1}/${MAX_RETRY_COUNT})...`);
              try {
                await executeFFmpeg(retryCount + 1);
                resolve();
              } catch (error) {
                reject(error);
              }
            } else {
              reject(`FFmpeg process failed after ${MAX_RETRY_COUNT + 1} attempts with code ${code} ${roomId}`);
            }
          } else {
            console.log(`FFmpeg process completed successfully on attempt ${retryCount + 1} ${roomId}`);
            resolve();
          }
        });
      });
    };

    return executeFFmpeg();
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

module.exports = { mergeFiles };
