// Function to generate segments with blanks where needed
function generateSegments(inputs, totalDuration, size) {
  // Sort inputs by delay
  inputs.sort((a, b) => a.delay - b.delay);

  let segments = [];
  let previousEndTime = 0;

  inputs.forEach((input) => {
    let startTime = input.delay;
    let endTime = input.delay + input.duration;

    if (startTime > previousEndTime) {
      // Gap detected, add blank segment
      segments.push({
        type: "blank",
        duration: startTime - previousEndTime,
        size: size,
      });
    }

    // Add input segment
    segments.push({
      type: "input",
      filePath: input.filePath,
      duration: input.duration,
      // For input segments, duration is explicit
    });

    previousEndTime = endTime;
  });

  // Check for any remaining duration after last input
  if (previousEndTime < totalDuration) {
    segments.push({
      type: "blank",
      duration: totalDuration - previousEndTime,
      size: size,
    });
  }

  return segments;
}

module.exports = { generateSegments };
