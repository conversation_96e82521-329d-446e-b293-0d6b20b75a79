const { BlobServiceClient } = require("@azure/storage-blob");
const fs = require("fs");

const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME;

async function downloadFromBlobStorage(blobName, downloadFilePath) {
  let fileStream = null;

  return Promise.race([
    new Promise(async (resolve, reject) => {
      try {
        const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
        const containerClient = blobServiceClient.getContainerClient(containerName);
        const blobClient = containerClient.getBlobClient(blobName);

        const properties = await blobClient.getProperties();
        const fileSize = properties.contentLength;
        if (fileSize > 1 * 1024 * 1024 * 1024) { // 1 GB threshold
          return reject(new Error(`File '${blobName}' is too big (${(fileSize / (1024 * 1024 * 1024)).toFixed(2)} GB)`));
        }

        fileStream = fs.createWriteStream(downloadFilePath);
        const downloadBlockBlobResponse = await blobClient.download(0);

        downloadBlockBlobResponse.readableStreamBody.pipe(fileStream);

        fileStream.on("finish", () => {
          console.log(`Download of '${blobName}' completed to '${downloadFilePath}'`);
          resolve();
        });

        fileStream.on("error", (err) => {
          console.error(`Error downloading '${blobName}' to '${downloadFilePath}'`, err);
          reject(err);
        });
      } catch (error) {
        console.error(`Error in blob download process: ${error.message}`);
        if (fileStream) {
          fileStream.destroy();
        }
        reject(error);
      }
    }),
    new Promise((_, reject) => {
      setTimeout(() => {
        if (fileStream) {
          fileStream.destroy();
        }
        reject(new Error(`Download timeout after 5 minutes for '${blobName}'`));
      }, 5 * 60 * 1000);
    })
  ]);
}

module.exports = downloadFromBlobStorage;