const amqp = require("amqp-connection-manager");

class RabbitMQManager {
  static connection = null;

  static connect(url) {
    return new Promise((resolve, reject) => {
      try {
        this.connection = amqp.connect(url || process.env.AMQP_URL);
        this.connection.on("connect", () => {
          console.log("Connected to RabbitMQ");
          resolve(this);
        });
        this.connection.on("disconnect", (err) => {
          console.error("Disconnected from RabbitMQ:", err);
          reject(err);
        });
      } catch (error) {
        console.error("Error connecting to RabbitMQ:", error);
        reject(error);
      }
    });
  }

  static consumeQueue(queueName, processMessage) {
    if (!this.connection) {
      throw new Error(
        "Not connected to RabbitMQ. Call connect() first."
      );
    }

    const channelWrapper = this.connection.createChannel({
      json: true,
      heartbeatIntervalInSeconds: parseInt(process.env.HEARTBEAT_INTERVAL_IN_SECONDS, 10) || 90,
      reconnectTimeInSeconds: parseInt(process.env.RECONNECT_TIME_IN_SECONDS, 10) || 5,
      setup: async (channel) => {
        console.log(
          `Connected to AMQP server, setting up consumer for queue: ${queueName}`
        );

        // Ensure the queue exists
        await channel.assertQueue(queueName, { durable: true });

        // Set prefetch to 1 for sequential processing
        await channel.prefetch(1);

        // Start consuming messages
        channel.consume(queueName, async (msg) => {
          if (msg) {
            const message = JSON.parse(msg.content.toString());
            console.log("New Message:", message);

            try {
              await processMessage(message);
              channel.ack(msg); // Acknowledge message after successful processing
            } catch (error) {
              console.error("Error processing message:", error);
              channel.ack(msg); // Requeue the message for retry
            }
          }
        });

        console.log(`Started consuming messages from queue: ${queueName}`);
      },
    });

    return channelWrapper;
  }
}

module.exports = RabbitMQManager;
