const { mergeFiles } = require("../utils/mergeFiles/ffmpeg");
const { removeFile, removeFolder } = require("../utils/mergeFiles/deleteFiles");
const {
  uploadToBlobStorage,
} = require("../utils/mergeFiles/uploadToBlobStorage");
const path = require("path");
const fs = require("fs");
const axios = require('axios');
const downloadFromBlobStorage = require("../utils/downloadFromBlobStorage");
const concatVideoFiles = require("../utils/mergeFiles/concatVideoFiles");

async function mergeService(message) {
  const { recordingDir, companyId } = message;
  const roomId = recordingDir.split("_")[0];
  const time = new Date().getTime();

  if (["a98faaef", "demoairtel1", "demoairtel2", "demoairtel3", "9ez2-lx49-oavz"].includes(roomId)) {
    console.log("Skipping room:", roomId);
    return;
  }

  const recordingsDir = path.join(
    process.env.RECORD_FILE_LOCATION_PATH,
    recordingDir
  );

  if (!fs.existsSync(recordingsDir)) {
    console.error("Recordings directory not found:", recordingsDir);
    return;
  }

  console.log("Merging files for room:", roomId);

  let recordingsOutputFilePath = path.join(
    process.env.RECORD_FILE_LOCATION_PATH,
    "outputs",
    `${roomId}_${time}.mp4`
  );

  await mergeFiles(recordingsDir, recordingsOutputFilePath, roomId);

  try {
    const res = await axios.get(`${process.env.BASE_URL}internal/get-create-time?roomId=${roomId}&companyId=${companyId}`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.STATIC_TOKEN}`,
        },
      },
    );
    console.log("get-create-time:", res.status, res.data)
    if (res.status === 200) {
      if (res.data.time) {
        const file = `${roomId}_${res.data.time}.mp4`;
        recordingsOutputFilePath = path.join(
          process.env.RECORD_FILE_LOCATION_PATH,
          "outputs",
          file
        );
        try {
          await downloadFromBlobStorage(file, recordingsOutputFilePath);
          console.log("Videos downloaded successfully");
          await concatVideoFiles(file, `${roomId}_${time}.mp4`);
          console.log("Videos concatenated successfully");
        } catch (error) {
          console.log("Error during download or concatenation:", error);
          removeFile(recordingsOutputFilePath);
          return;
        }
      } else {
        const data = await axios.post(
          `${process.env.BASE_URL}internal/update-create-time`,
          {
            roomId: roomId,
            time: time.toString(),
            companyId: companyId,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${process.env.STATIC_TOKEN}`,
            },
          },
        );
        console.log("update-create-time:", data.status, data.data)
      }
    }
  } catch (error) {
    console.error("Error storing room createTime:", error);
  }

  if (process.env.NODE_ENV !== "development") {
    // removeFolder(recordingsDir); //! remove raw files
    await uploadToBlobStorage(recordingsOutputFilePath);
    removeFile(recordingsOutputFilePath);
  }
}

module.exports = { mergeService };