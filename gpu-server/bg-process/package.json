{"name": "bg-process", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/index.js", "dev": "nodemon src/index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@azure/storage-blob": "^12.24.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.4", "axios": "^1.7.7", "dotenv": "^16.4.5", "fluent-ffmpeg": "^2.1.3"}, "devDependencies": {"nodemon": "^3.1.4"}}