# Build server
FROM node:18.16.0-alpine3.17 AS server
WORKDIR /app/server
COPY server/package*.json ./
RUN npm install
COPY server/. .

# Build upload-server
FROM node:18.16.0-alpine3.17 AS upload-server
WORKDIR /app/upload-server
COPY upload-server/package*.json ./
RUN npm install
COPY upload-server/. .

# Final stage
FROM node:18.16.0-alpine3.17
WORKDIR /app
RUN mkdir /opt/certs
COPY --from=server /app/server ./server
COPY --from=upload-server /app/upload-server ./upload-server
COPY --from=server /app/server/certs/DigiCertGlobalRootCA.crt.pem /opt/certs/DigiCertGlobalRootCA.crt.pem
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/server/certs/hyra-720a2-2f9c8fd85be7.json
EXPOSE 5005

RUN echo -e "#!/bin/sh\ncd /app/server && node src/index.js &\ncd /app/upload-server && node index.js" > /app/run.sh
RUN chmod +x /app/run.sh
CMD ["/app/run.sh"]
