@app.get("/video_feed/{room_id}")
async def video_feed(room_id: str):
    gaze_ai_instance = gaze_ai_instances.get(room_id)
    if gaze_ai_instance and gaze_ai_instance.consumer:

        def image_generator():
            frame_queue = []

            def image_callback(jpeg):
                frame_queue.append(jpeg)

            gaze_ai_instance.register_image_callback(image_callback)

            while True:
                if frame_queue:
                    jpeg = frame_queue.pop(0)
                    yield (
                        b"--frame\r\n"
                        b"Content-Type: image/jpeg\r\n\r\n" + jpeg.tobytes() + b"\r\n"
                    )

        return StreamingResponse(
            image_generator(),
            media_type="multipart/x-mixed-replace; boundary=frame",
        )
    else:
        return JSONResponse(
            content={"error": "Consumer not available for this room"}, status_code=500
        )
