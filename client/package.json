{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/aspect-ratio": "^0.4.2", "ace-builds": "^1.37.1", "axios": "^1.7.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.4", "cookies-next": "^4.1.1", "css-loader": "^7.1.2", "date-fns": "^3.6.0", "firebase": "^10.8.0", "framer-motion": "^11.0.8", "hark": "^1.2.3", "howler": "^2.2.4", "lucide-react": "^0.340.0", "mediasoup-client": "^3.7.8", "mixpanel-browser": "^2.49.0", "monaco-editor": "^0.52.2", "monaco-editor-webpack-plugin": "^7.1.0", "next": "14.1.0", "react": "^18", "react-ace": "^13.0.0", "react-day-picker": "^8.10.0", "react-device-detect": "^2.2.3", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.0", "react-resizable-panels": "^2.0.18", "redaxios": "^0.5.1", "sharp": "^0.33.4", "socket.io-client": "^4.7.4", "style-loader": "^4.0.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/react": "^18.2.58", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.3.0"}}