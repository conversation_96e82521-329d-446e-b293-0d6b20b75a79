export const MAX_FILE_SIZE = 20; //in MB
export const MAX_FILE_COUNT = 200;
export const EXCLUDED_FOLDERS = [
  '.git',
  'node_modules',
  'venv',
  '__pycache__',
  'build',
  'dist',
  'target',
];
export const assignmentSubmissionStatus = {
  SUBMITTED: 'Submitted',
  NOT_SUBMITTED: 'Submit',
  SUBMITTING: 'Submitting...',
};

export const githubRepoUrlRegex =
  /^(https:\/\/github.com\/[a-zA-Z0-9_-]+\/[a-zA-Z0-9_-]+)(\.git)?(\/tree\/[^/]+.*)?$/;
