const errors = {
  'internal-server-error': {
    title: 'Oops! Something went wrong',
    message:
      'Internal server error. Please try again later. If the problem persists, please contact support for further assistance.',
  },
  'invalid-room': {
    title: 'Invalid Room',
    message:
      "It seems like you've clicked on an incorrect link or you may not be invited to attend the interview meeting. Please double-check the link provided or contact the organizer for assistance.\n" +
      'If you believe this is an error, please try again or contact support for further assistance.',
  },
  'email-mismatch': {
    title: 'Authentication Mismatch',
    message: ({intendedEmail, currentEmail}) => `
    You are currently logged in as [${currentEmail}], but this action was intended for [${intendedEmail}]. Please log out and sign in with the correct email address to continue, or contact the organizer if you need assistance.
    `,
  },
  'interview-ended': {
    title: 'Interview Ended',
    message:
      'The interview has ended. If you believe this is an error, please try again or contact support for further assistance.',
  },
  'interview-taken': {
    title: 'Interview Taken',
    message: 'The interview has already been taken.',
  },
  'already-booked-slot': {
    title: 'Already Booked',
    message:
      'This slot is already booked. Please try again with a different slot.',
  },
  'room-canceled-or-rescheduled': {
    title: 'Interview Rescheduled or Canceled',
    message:
      'The interview has been rescheduled or canceled. Please check your interview schedule link for further details.',
  },
  'booking-temporarily-unavailable': {
    title: 'Booking Temporarily Unavailable',
    message:
      'The booking system is temporarily unavailable. Please try again after a few minutes or contact support for further assistance.',
  },
  'room-full': {
    title: 'Room Full',
    message:
      'The room is full. Please try again later or contact the organizer for further assistance.',
  },
  unauthorized: {
    title: 'Unauthorized',
    message: 'You are not authorized to perform this action.',
  },
  'not-found': {
    title: 'Not Found',
    message:
      'The requested resource was not found. Please check the URL or contact support for further assistance.',
  },
  'already-booked': {
    title: 'Already Booked',
    message:
      'You have already booked a slot for this interview. Please check your email for further details.',
  },
  'booking-temporarily-unavailable': {
    title: 'Booking Temporarily Unavailable',
    message:
      'The booking system is temporarily unavailable. Please try again after a few minutes or contact support for further assistance.',
  },
  'unsupported-file-type': {
    title: 'Unsupported File Type',
    message:
      'The file you are trying to access is not supported. Please try again with a different file.',
  },
  'file-fetch-error': {
    title: 'Error Fetching File',
    message:
      'There was an error fetching the file content. Please try again later.',
  },
  'file-too-large': {
    title: 'File Too Large',
    message:
      'The file you are trying to access is too large. Please try again with a smaller file.',
  },
  'browser-not-supported': {
    title: 'Browser Not Supported',
    message:
      'This browser is not supported. Please use Google Chrome for the best experience.',
  },
  'join-room-error': {
    title: 'Error Joining Room',
    message:
      'There was an error joining the room. Please try again later or contact support for further assistance.',
  },
  'join-tutorial-error': {
    title: 'Error Joining Tutorial',
    message:
      'There was an error joining the tutorial. Please try again later or contact support for further assistance.',
  },
  'reconnect-error': {
    title: 'Error Reconnecting',
    message:
      'There was an error reconnecting. Please try again later or contact support for further assistance.',
  },
  'job-form-closed': {
    title: 'Job Form is Closed',
    message:
      'This job form is closed. Please contact the administrator for more information.',
  },
  'speech-to-text-error': {
    title: 'Speech Recognization Error',
    message:
      'There was an error recognizing your speech. Please refresh the page and try again.',
  },
};

export default errors;
