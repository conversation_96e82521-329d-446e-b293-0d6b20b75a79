import {io} from 'socket.io-client';
import {auth} from './lib/firebase/firebase';
import {getMediasoupEndpoint} from './lib/utilities/mediasoupService';

class MediasoupSocket {
  constructor() {
    this.socket = {};
    this.roomId = null;
    this.peerId = null;
    this.peers = {};
    this.transportSend = null;
    this.transportRecv = null;
    this.device = null;
  }

  getMediasoupSocket = async (companyId, roomId) => {
    const uid = auth.currentUser.uid;

    if (this.socket.hasOwnProperty(uid)) return this.socket[uid];
    console.log('connecting to mediaserver');
    this.companyId = companyId;
    this.roomId = roomId;

    const mediaserverEndpoint = await getMediasoupEndpoint(companyId, roomId);
    this.socket[uid] = io(mediaserverEndpoint, {
      auth: cb => {
        auth.currentUser.getIdToken().then(bearerToken => {
          cb({token: 'Bearer ' + bearerToken});
        });
      },
      autoConnect: false,
      reconnection: true,
      reconnectionAttempts: 5,
      transports: ['websocket', 'polling'],
    });

    return this.socket[uid];
  };

  deleteMediasoupSocket = () => {
    if (this.socket.hasOwnProperty(uid)) delete this.socket[uid];
  };
}

const mediasoupSocketHandler = new MediasoupSocket();

export {mediasoupSocketHandler};
