import {sendPasswordResetEmail} from 'firebase/auth';
import {db, auth} from './firebase';
import {ref, trackEvent} from './firebaseWrapper';
import { reportError } from '../utilities/api';

let client_id;

export const checkIfUrlValid = () => {
  const params = new Proxy(new URLSearchParams(window.location.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });
  value = params.client_id;
  //api check here
  if (value) {
    client_id = value;
    return true;
  }
  return false;
};

export const getSubDomain = hostname => {
  var regexParse = new RegExp('[a-z-0-9]{2,63}.[a-z.]{2,5}$');
  var urlParts = regexParse.exec(hostname);
  const sub = hostname.replace(urlParts[0], '').slice(0, -1);
  if (!sub) {
    return 'undefined';
  }
  return sub;
};

export const getGameRef = gameName => {
  const uid = auth.currentUser.uid;
  client_id = getSubDomain(window.location.hostname);
  console.log('client_id', client_id)
  return ref(db, `/clients/${client_id}/${uid}/${gameName}/`);
};

export const handleSendPasswordResetEmail = async (email, setEmail) => {
  setEmail(state => ({...state, sending: true}));
  await sendPasswordResetEmail(auth, email.address)
    .then(() => {
      setEmail(state => ({
        ...state,
        sent: true,
        sending: false,
        error: false,
        errorCode: '',
      }));
    })
    .catch(error => {
      trackEvent('sendPasswordResetEmail', { err: error });
      setEmail(state => ({
        ...state,
        error: true,
        sending: false,
        errorCode: error.code,
      }));
      console.log(error.code);
      reportError("PasswordResetEmail", error, false);
    });
};
