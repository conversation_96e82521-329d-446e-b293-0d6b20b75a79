import {
  ref as firebaseRef,
  update as firebaseUpdate,
  child as firebase<PERSON>hild,
  push as firebasePush,
  set as firebaseSet,
  onValue as firebaseOnValue,
  off as firebaseOff,
} from 'firebase/database';
import { auth } from '@/lib/firebase/firebase';

import mixpanel from 'mixpanel-browser';
mixpanel.init('f7aed417232c3e24301668df9fb9c993', {
  debug: true,
  track_pageview: true,
  persistence: 'localStorage',
  ignore_dnt: true,
});

export const specificEmail = '<EMAIL>';
const demoUrl = 'https://demo.hyrr.app';

export const ref = (db, path) => {
  if (
    (auth.currentUser && auth.currentUser.email === specificEmail) ||
    window.location.href === demoUrl
  ) {
    return {
      set: () => Promise.resolve(),
      update: () => Promise.resolve(),
      remove: () => Promise.resolve(),
    };
  }
  return firebaseRef(db, path);
};

export const update = (ref, data) => {
  if (
    (auth.currentUser && auth.currentUser.email === specificEmail) ||
    window.location.href === demoUrl
  ) {
    return Promise.resolve();
  }
  return firebaseUpdate(ref, data);
};

export const set = (ref, data) => {
  if (
    (auth.currentUser && auth.currentUser.email === specificEmail) ||
    window.location.href === demoUrl
  ) {
    return Promise.resolve();
  }
  return firebaseSet(ref, data);
};

export const child = (ref, path) => {
  if (
    (auth.currentUser && auth.currentUser.email === specificEmail) ||
    window.location.href === demoUrl
  ) {
    return {
      set: () => Promise.resolve(),
      update: () => Promise.resolve(),
      remove: () => Promise.resolve(),
    };
  }
  return firebaseChild(ref, path);
};

export const push = (child, data) => {
  if (
    (auth.currentUser && auth.currentUser.email === specificEmail) ||
    window.location.href === demoUrl
  ) {
    return Promise.resolve();
  }
  return firebasePush(child, data);
};

export const onValue = (ref, callback) => {
  if (
    (auth.currentUser && auth.currentUser.email === specificEmail) ||
    window.location.href === demoUrl
  ) {
    return () => { };
  }
  return firebaseOnValue(ref, callback);
};

export const off = (ref, eventType, callback) => {
  if (
    (auth.currentUser && auth.currentUser.email === specificEmail) ||
    window.location.href === demoUrl
  ) {
    return;
  }
  return firebaseOff(ref, eventType, callback);
};


export const trackEvent = (eventName, value) => {
  try {
    mixpanel.track(eventName, value);
  } catch (e) {
    console.log('error while tracking events', e);
  }
}
