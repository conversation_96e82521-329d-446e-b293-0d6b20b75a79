export const checkIfFilePathExists = (filePath, files) => {
  const pathParts = filePath.split('/').filter(Boolean);

  let currentItem = files;

  for (let i = 0; i < pathParts.length; i++) {
    const part = pathParts[i];

    if (i === 0 && currentItem.name === part) {
      continue;
    }

    const nextItem = currentItem.items.find(item => item.name === part);
    if (!nextItem) {
      return false;
    }
    currentItem = nextItem;
  }

  return true;
};
