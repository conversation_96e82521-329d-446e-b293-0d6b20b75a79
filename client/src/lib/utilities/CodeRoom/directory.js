export function addProperties(obj, properties) {
  if (properties[obj.type]) {
    Object.assign(obj, properties[obj.type]);
  }

  if (obj.items) {
    obj.items.forEach(item => addProperties(item, properties));
  }

  return obj;
}

export function addFileToStructure(path, structure) {
  const pathParts = path.split('/').filter(Boolean).slice(1);
  let currentDir = structure;

  function findOrCreateFolder(name, items) {
    let folder = items.find(item => item.name === name && item.type === 0);
    if (!folder) {
      folder = {name, type: 0, items: []};
      items.push(folder);
    }
    return folder;
  }

  for (let i = 0; i < pathParts.length - 1; i++) {
    const folderName = pathParts[i];
    currentDir = findOrCreateFolder(folderName, currentDir.items);
  }

  const fileName = pathParts[pathParts.length - 1];
  if (
    !currentDir.items.some(item => item.name === fileName && item.type === 1)
  ) {
    currentDir.items.push({name: fileName, type: 1});
  }

  return structure;
}

export function mergePathsIntoStructure(structure, files) {
  for (const file of files) {
    structure = addFileToStructure(file, structure);
  }
  return structure;
}

export function sortItems(obj) {
  if (obj.items) {
    obj.items.sort((a, b) => a.type - b.type || a.name.localeCompare(b.name));
    obj.items.forEach(item => sortItems(item));
  }
  return obj;
}

export const findCodeFile = (files) => {
  if (!files || !files.items) return null;

  const codeFile = files.items.find(item =>
    item.type === 1 && item.name && item.name.startsWith("code.")
  );

  return codeFile;
};