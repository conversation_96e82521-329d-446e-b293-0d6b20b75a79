function prepareFiles(filesData, isPublish = false) {
  const filesToBeSent = Object.keys(filesData)
    .filter(file =>
      isPublish
        ? filesData[file].publishedToServer == false
        : filesData[file].sentToServer === false,
    )
    .filter(file => filesData[file].change !== null)
    .map(file => {
      return {
        path: file,
        ...filesData[file],
      };
    });

  return filesToBeSent;
}

export default prepareFiles;
