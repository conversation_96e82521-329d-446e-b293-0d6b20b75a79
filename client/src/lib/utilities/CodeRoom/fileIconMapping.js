const fileIconMapping = {
  stl: '3d',
  obj: '3d',
  ac: '3d',
  blend: '3d',
  mesh: '3d',
  mqo: '3d',
  pmd: '3d',
  pmx: '3d',
  skp: '3d',
  vac: '3d',
  vdp: '3d',
  vox: '3d',
  abc: 'abc',
  as: 'actionscript',
  ada: 'ada',
  adb: 'ada',
  ads: 'ada',
  ali: 'ada',
  ch: 'advpl_include',
  prx: 'advpl_prw',
  ptm: 'advpl_ptm',
  tlpp: 'advpl_tlpp',
  ai: 'ai',
  apk: 'android',
  smali: 'androd',
  dex: 'android',
  'component.ts': 'angular-component',
  'component.js': 'angular-component',
  'directive.ts': 'angular-directive',
  'directive.js': 'angular-directive',
  'guard.ts': 'angular-guard',
  'guard.js': 'angular-guard',
  'pipe.ts': 'angular-pipe',
  'pipe.js': 'angular-pipe',
  'resolver.ts': 'angular-resolver',
  'resolver.js': 'angular-resolver',
  'service.ts': 'angular-service',
  'service.js': 'angular-service',
  apib: 'apiblueprint',
  apiblueprint: 'apiblueprint',
  applescript: 'applescript',
  ipa: 'applescript',
  gs: 'apps-script',
  ino: 'arduino',
  ad: 'asciidoc',
  adoc: 'asciidoc',
  asciidoc: 'asciidoc',
  asm: 'assembly',
  a51: 'assembly',
  nasm: 'assembly',
  s: 'assembly',
  ms: 'assembly',
  agc: 'assembly',
  ags: 'assembly',
  aea: 'assembly',
  argus: 'assembly',
  miligus: 'assembly',
  binsource: 'assembly',
  astro: 'astro',
  mp3: 'audio',
  ogg: 'audio',
  wav: 'audio',
  ahk: 'autohotkey',
  au3: 'autoit',
  azcli: 'azure',
  'azure-pipelines.yml': 'azure-pipelines',
  'azure-pipelines.yaml': 'azure-pipelines',
  bal: 'ballerina',
  balx: 'ballerina',
  bzl: 'bazel',
  bazel: 'bazel',
  bicep: 'bicep',
  'blade.php': 'blade',
  'inky.php': 'blade',
  blade: 'blade',
  blink: 'blink',
  b: 'brainfuck',
  bf: 'brainfuck',
  cmj: 'bucklescript',
  gemfile: 'bundler',
  'gemfile.lock': 'bundler',
  c: 'c',
  i: 'c',
  mi: 'c',
  cabal: 'cabal',
  cake: 'cake',
  crt: 'certificate',
  cer: 'certificate',
  cert: 'certificate',
  pgn: 'chess',
  fen: 'chess',
  clj: 'clojure',
  cljs: 'clojure',
  cljc: 'clojure',
  cmake: 'cmake',
  coarc: 'coala',
  coafile: 'coala',
  cob: 'cobol',
  cbl: 'cobol',
  coco: 'coconut',
  '.codeclimate.yml': 'code-climate',
  coffee: 'coffee',
  cson: 'coffee',
  iced: 'coffee',
  cfml: 'coldfusion',
  cfc: 'coldfusion',
  lucee: 'coldfusion',
  cfm: 'coldfusion',
  command: 'command',
  cmd: 'console',
  sh: 'console',
  ksh: 'console',
  csh: 'console',
  tcsh: 'console',
  zsh: 'console',
  bash: 'console',
  bat: 'console',
  awk: 'console',
  fish: 'console',
  exp: 'console',
  cpp: 'cpp',
  cc: 'cpp',
  cxx: 'cpp',
  'c++': 'cpp',
  cp: 'cpp',
  mm: 'cpp',
  mii: 'cpp',
  ii: 'cpp',
  cr: 'crystal',
  ecr: 'crystal',
  csx: 'csharp',
  cs: 'csharp',
  css: 'css',
  'css.map': 'cssmap',
  feature: 'cucumber',
  cu: 'cuda',
  cuh: 'cuda',
  d: 'd',
  dart: 'dart',
  pdb: 'database',
  sql: 'database',
  pks: 'database',
  pkb: 'database',
  accdb: 'database',
  mdb: 'database',
  sqlite: 'database',
  sqlite3: 'database',
  pgsql: 'database',
  postgres: 'database',
  psql: 'database',
  db: 'database',
  db3: 'database',
  dsc: 'denizenscript',
  dhall: 'dhall',
  dhallb: 'dhall',
  bubble: 'dinophp',
  'html.bubble': 'dinophp',
  'php.bubble': 'dinophp',
  iso: 'disc',
  djt: 'django',
  dockerignore: 'docker',
  dockerfile: 'docker',
  'docker-compose.yml': 'docker',
  'docker-compose.yaml': 'docker',
  'compose.yaml': 'docker',
  'compose.yml': 'docker',
  txt: 'document',
  def: 'dotjs',
  dot: 'dotjs',
  jst: 'dotjs',
  drawio: 'drawio',
  dio: 'drawio',
  'drone.yml': 'drone',
  edge: 'edge',
  eex: 'elixir',
  ejs: 'ejs',
  ex: 'elixir',
  exs: 'elixir',
  leex: 'elixir',
  heex: 'elixir',
  elm: 'elm',
  ics: 'email',
  env: 'env',
  erl: 'erlang',
  exe: 'exe',
  msi: 'exe',
  ico: 'favicon',
  fig: 'figma',
  swf: 'flash',
  woff2: 'font',
  woff: 'font',
  ttf: 'font',
  otf: 'font',
  '4th': 'forth',
  fth: 'forth',
  frt: 'forth',
  f: 'fortran',
  f77: 'fortran',
  f90: 'fortran',
  f95: 'fortran',
  f08: 'fortran',
  fxp: 'foxpro',
  prg: 'foxpro',
  fs: 'fsharp',
  fsx: 'fsharp',
  fsi: 'fsharp',
  fsproj: 'fsharp',
  gmi: 'gemini',
  gemini: 'gemini',
  patch: 'git',
  go: 'go',
  gd: 'godot',
  godot: 'godot-assets',
  tres: 'godot-assets',
  tscn: 'godot-assets',
  gradle: 'gradle',
  gr: 'grain',
  graphcool: 'graphcool',
  graphql: 'graphql',
  gql: 'graphql',
  groovy: 'groovy',
  h: 'h',
  haml: 'haml',
  hbs: 'handlebars',
  mustache: 'handlebars',
  hs: 'haskell',
  hx: 'haxe',
  hcl: 'hcl',
  dat: 'hex',
  bin: 'hex',
  hex: 'hex',
  hh: 'hpp',
  hpp: 'hpp',
  hxx: 'hpp',
  'h++': 'hpp',
  hp: 'hpp',
  tcc: 'hpp',
  inl: 'hpp',
  html: 'html',
  xhtml: 'html',
  html_vm: 'html',
  asp: 'html',
  http: 'http',
  rest: 'http',
  pot: 'i18n',
  po: 'i18n',
  mo: 'i18n',
  idr: 'idris',
  ibc: 'idris',
  png: 'image',
  jpg: 'image',
  jepg: 'image',
  gif: 'image',
  webp: 'image',
  imba: 'imba',
  jar: 'jar',
  java: 'java',
  jsp: 'java',
  class: 'javaclass',
  jenkins: 'jenkins',
  jinja: 'jinja',
  jinja2: 'jinja',
  j2: 'jinja',
  'jinja-html': 'jinja',
  js: 'js',
  cjs: 'js',
  jsconfig: 'jsconfig',
  esx: 'js',
  mjs: 'js',
  'js.map': 'jsmap',
  json: 'json',
  tsbuildinfo: 'json',
  json5: 'json',
  jsonl: 'json',
  ndjson: 'json',
  jl: 'julia',
  ipynb: 'jupyter',
  key: 'key',
  pem: 'key',
  pub: 'key',
  asc: 'key',
  gpg: 'key',
  passwd: 'key',
  kv: 'kivy',
  kl: 'kl',
  kt: 'kotlin',
  kts: 'kotlin',
  kql: 'kusto',
  less: 'less',
  lib: 'lib',
  bib: 'lib',
  ly: 'lilypond',
  liquid: 'liquid',
  lisp: 'lisp',
  lsp: 'lisp',
  cl: 'lisp',
  fast: 'lisp',
  ls: 'livescript',
  ll: 'llvm',
  bc: 'llvm',
  log: 'log',
  lock: 'lock',
  lol: 'lolcode',
  makefile: 'makefile',
  md: 'markdown',
  markdown: 'markdown',
  rst: 'markdown',
  marko: 'markojs',
  nb: 'mathematica',
  mex: 'matlab',
  mexn: 'matlab',
  mexrs6: 'matlab',
  mn: 'matlab',
  mum: 'matlab',
  mx: 'matlab',
  mx3: 'matlab',
  rwd: 'matlab',
  slx: 'matlab',
  slddc: 'matlab',
  smv: 'matlab',
  xvc: 'matlab',
  mdx: 'mdx',
  svx: 'mdsvex',
  merlin: 'merlin',
  wrap: 'meson',
  mcfunction: 'minecraft',
  mcmeta: 'minecraft',
  mcr: 'minecraft',
  mca: 'minecraft',
  mcgame: 'minecraft',
  mclevel: 'minecraft',
  mcworld: 'minecraft',
  mine: 'minecraft',
  mus: 'minecraft',
  mint: 'mint',
  mjml: 'mjml',
  moon: 'moonscript',
  mxml: 'mxml',
  'ndst.yml': 'ndst',
  'ndst.yaml': 'ndst',
  'ndst.json': 'ndst',
  nim: 'nim',
  nimble: 'nim',
  nix: 'nix',
  njk: 'nunjucks',
  nunjucks: 'nunjucks',
  ml: 'ocaml',
  mli: 'ocaml',
  cmx: 'ocaml',
  odin: 'odin',
  rego: 'opa',
  opam: 'opam',
  vert: 'opengl',
  frag: 'opengl',
  tesc: 'opengl',
  tese: 'opengl',
  geom: 'opengl',
  comp: 'opengl',
  glsl: 'opengl',
  hlsl: 'opengl',
  pas: 'pascal',
  pwn: 'pawn',
  amx: 'pawn',
  pdf: 'pdf',
  pm: 'perl',
  raku: 'perl',
  psd: 'photoshop',
  php: 'php',
  pipeline: 'pipeline',
  pcss: 'postcssconfig',
  sss: 'postcssconfig',
  pptx: 'powerpoint',
  ppt: 'powerpoint',
  pptm: 'powerpoint',
  potx: 'powerpoint',
  potm: 'powerpoint',
  ppsx: 'powerpoint',
  ppsm: 'powerpoint',
  pps: 'powerpoint',
  ppam: 'powerpoint',
  ppa: 'powerpoint',
  ps1: 'powershell',
  psm1: 'powershell',
  psd1: 'powershell',
  ps1xml: 'powershell',
  psc1: 'powershell',
  pssc: 'powershell',
  prisma: 'prisma',
  pde: 'processing',
  pro: 'prolog',
  p: 'prolog',
  pl: 'prolog',
  proto: 'proto',
  jade: 'pug',
  pug: 'pug',
  pp: 'puppet',
  pure: 'purescript',
  purs: 'purescript',
  py: 'python',
  pyc: 'python-misc',
  whl: 'python-misc',
  qs: 'qsharp',
  r: 'r',
  rmd: 'r',
  rkt: 'racket',
  raml: 'raml',
  cshtml: 'razor',
  vbhtml: 'razor',
  tsx: 'reactts',
  jsx: 'react',
  re: 'reason',
  rei: 'reason',
  red: 'red',
  res: 'rescript',
  resi: 'rescript',
  rql: 'restql',
  restql: 'restql',
  riot: 'riot',
  tag: 'riot',
  'routing.ts': 'routing',
  'routing.tsx': 'routing',
  'routing.js': 'routing',
  'routing.jsx': 'routing',
  'routes.ts': 'routing',
  'routes.tsx': 'routing',
  'routes.js': 'routing',
  'routes.jsx': 'routing',
  rb: 'ruby',
  erb: 'ruby',
  rake: 'ruby',
  rs: 'rust',
  san: 'san',
  sas: 'sas',
  sas7bdat: 'sas',
  sashdat: 'sas',
  astore: 'sas',
  ast: 'sas',
  sast: 'sas',
  sass: 'sass',
  scss: 'sass',
  sbt: 'sbt',
  scala: 'scala',
  sc: 'scala',
  ss: 'scheme',
  scm: 'scheme',
  'code-search': 'search',
  ini: 'settings',
  dlc: 'settings',
  dll: 'settings',
  config: 'settings',
  conf: 'settings',
  properties: 'settings',
  prop: 'settings',
  settings: 'settings',
  option: 'settings',
  props: 'settings',
  toml: 'settings',
  prefs: 'settings',
  'sln.dotsettings': 'settings',
  'sln.dotsettings.user': 'settings',
  cfg: 'settings',
  unity: 'shaderlab',
  sy: 'siyuan',
  sketch: 'sketch',
  slim: 'slim',
  tpl: 'smarty',
  sml: 'sml',
  mlton: 'sml',
  mlb: 'sml',
  sig: 'sml',
  fun: 'sml',
  cm: 'sml',
  lex: 'sml',
  use: 'sml',
  grm: 'sml',
  sol: 'solidity',
  'steadybit.yml': 'steadybit',
  'steadybit.yaml': 'steadybit',
  'story.js': 'storybook',
  'story.jsx': 'storybook',
  'story.ts': 'storybook',
  'story.tsx': 'storybook',
  'story.mdx': 'storybook',
  'stories.js': 'storybook',
  'stories.jsx': 'storybook',
  'stories.ts': 'storybook',
  'stories.tsx': 'storybook',
  'stories.mdx': 'storybook',
  'stories.svelte': 'storybook',
  styl: 'stylus',
  'sublime-project': 'sublime',
  'sublime-workspace': 'sublime',
  svelte: 'svelte',
  svg: 'svg',
  swc: 'swc',
  swift: 'swift',
  xls: 'table',
  xlsx: 'table',
  csv: 'table',
  tsv: 'table',
  tauri: 'tauri',
  tcl: 'tcl',
  tl: 'teal',
  template: 'template',
  tf: 'terraform',
  tfstate: 'terraform',
  tfvars: 'terraform',
  'tf.json': 'terraform',
  'test.js': 'testjs',
  'test.mjs': 'testjs',
  'test.cjs': 'testjs',
  'test.snap': 'testjs',
  'e2e-spec.js': 'testjs',
  'e2e-spec.cjs': 'testjs',
  'spec.js': 'testjs',
  'spec.mjs': 'testjs',
  'spec.cjs': 'testjs',
  'spec.tsx': 'testjsx',
  'test.tsx': 'testjsx',
  'tsx.snap': 'testjsx',
  'spec.jsx': 'testjsx',
  'test.jsx': 'testjsx',
  'jsx.snap': 'testjsx',
  'spec.ts': 'testts',
  'e2e-spec.ts': 'testts',
  'test.ts': 'testts',
  'ts.snap': 'testts',
  texi: 'tex',
  sty: 'tex',
  dtx: 'tex',
  ltx: 'tex',
  tobi: 'tobi',
  todo: 'todo',
  '.travis.yml': 'travis',
  'tsconfig.json': 'tsconfig',
  twig: 'twig',
  tw: 'twine',
  twee: 'twine',
  'd.ts': 'typescriptdef',
  ts: 'typescript',
  iuml: 'uml',
  pu: 'uml',
  puml: 'uml',
  plantuml: 'uml',
  wsd: 'uml',
  url: 'url',
  vagrantfile: 'vagrant',
  vala: 'vala',
  vm: 'velocity',
  fhtml: 'velocity',
  vtl: 'velocity',
  vhd: 'verilog',
  sv: 'verilog',
  svh: 'verilog',
  vfl: 'vfl',
  mp4: 'video',
  mov: 'video',
  vim: 'vim',
  vimrc: 'vim',
  gvimrc: 'vim',
  exrc: 'vim',
  viminfo: 'vim',
  vdi: 'virtual',
  vbox: 'virtual',
  'vbox-prev': 'virtual',
  csproj: 'visualstudio',
  ruleset: 'visualstudio',
  sln: 'visualstudio',
  suo: 'visualstudio',
  vb: 'visualstudio',
  vbs: 'visualstudio',
  vcxitems: 'visualstudio',
  'vcxitems.filters': 'visualstudio',
  vcxproj: 'visualstudio',
  'vcxproj.filters': 'visualstudio',
  v: 'vlang',
  vsix: 'vscode',
  vsixmanifest: 'vscode',
  vscodeignore: 'vscode',
  'code-workplace': 'vscode',
  vue: 'vue',
  '.wakatime-project': 'wakatime',
  wasm: 'webassembly',
  wat: 'webassembly',
  wl: 'wolframlanguage',
  wls: 'wolframlanguage',
  doc: 'word',
  docx: 'word',
  rtf: 'word',
  xaml: 'xaml',
  xml: 'xml',
  plist: 'xml',
  xsd: 'xml',
  dtd: 'xml',
  xsl: 'xml',
  xslt: 'xml',
  resx: 'xml',
  iml: 'xml',
  xquery: 'xml',
  tmLanguage: 'xml',
  manifest: 'xml',
  project: 'xml',
  'xml.dist': 'xml',
  'xml.dist.sample': 'xml',
  dmn: 'xml',
  yaml: 'yaml',
  yml: 'yaml',
  'yml.dist': 'yaml',
  'yaml.dist': 'yaml',
  'YAML-tmLanguage': 'yaml',
  yang: 'yang',
  zig: 'zig',
  zip: 'zip',
  rar: 'zip',
  '7z': 'zip',
  tar: 'zip',
  gz: 'zip',
  bzip2: 'zip',
  xz: 'zip',
  bz2: 'zip',
  zipx: 'zip',
  tgz: 'zip',
  lua: 'lua',
};

export default fileIconMapping;
