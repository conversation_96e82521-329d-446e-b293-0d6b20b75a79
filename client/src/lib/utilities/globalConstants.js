export const isProduction = process.env.NODE_ENV === 'production';

export const BASE_URL = isProduction
  ? process.env.NEXT_PUBLIC_BASE_URL || 'https://api.neusort.com/'
  : 'http://localhost:5005/';

export const DISCOVERY_SERVER_URL = isProduction
  ? process.env.NEXT_PUBLIC_DISCOVERY_SERVER_URL ||
    'https://discovery.neusort.com/'
  : 'http://localhost:3004/';

export const SPEECH_TOKEN_REFRESH_INTERVAL_IN_SEC = 540; // 9 min
export const TUTORIAL_RESET_TIMEOUT_IN_SEC = 7 * 24 * 60 * 60; // (1 Week)
export const MIN_SECONDS_TO_REFRESH = 60;

// export const BASE_URL =
//   'https://44c7-2401-4900-1c3d-7a41-54ae-fc7e-7d9a-8573.ngrok-free.app/';

export const IFRAME_URL = 'https://onelogin.neusort.com/';
// export const IFRAME_URL = 'http://localhost:3001/';

export const HOST_NAMES = [
  'neusort.com',
  'hyrr.app',
  'techyrr.com',
  'localhost',
];

export const TEST_IPS = ['**************'];
