export const fetchMetaInfo = async (sendMetaInfoToBackend, metaInfo) => {
  const userAgent = navigator.userAgent;
  const language = navigator.language;
  const connectionType = navigator.connection ? navigator.connection.effectiveType : "Unknown";
  const memory = navigator.deviceMemory || "Unknown";
  const cpuCores = navigator.hardwareConcurrency || "Unknown";
  const screenWidth = screen.width;
  const screenHeight = screen.height;
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const hasOrientation = "DeviceOrientationEvent" in window;
  const downlink = navigator.connection ? navigator.connection.downlink : "Unknown";

  const os = userAgent.match(/\((.*?)\)/) ? userAgent.match(/\((.*?)\)/)[1] : "Unknown OS";
  const browser = userAgent.match(/(Firefox|Chrome|Safari|Edge|Opera|Trident)/) ? userAgent.match(/(Firefox|Chrome|Safari|Edge|Opera|Trident)/)[0] : "Unknown Browser";

  const bluetooth = navigator.bluetooth ? true : false;
  const clipboard = navigator.clipboard ? true : false;
  const contacts = navigator.contacts ? true : false;
  const credentials = navigator.credentials ? true : false;
  const geolocation = navigator.geolocation ? true : false;
  const gpu = navigator.gpu ? true : false;
  const hid = navigator.hid ? true : false;
  const keyboard = navigator.keyboard ? true : false;
  const serviceWorker = navigator.serviceWorker ? true : false;
  const maxTouchPoints = navigator.maxTouchPoints || "Unknown";
  const languages = navigator.languages || ["Unknown"];
  const vendor = navigator.vendor || "Unknown";
  const platform = navigator.platform || "Unknown";
  const colorDepth = screen.colorDepth || "Unknown";

  const battery = navigator.getBattery ? await navigator.getBattery() : null;
  const batteryLevel = battery ? battery.level * 100 : "Unknown";

  const availWidth = screen.availWidth;
  const availHeight = screen.availHeight;
  const pixelDepth = screen.pixelDepth || "Unknown";

  const performanceTiming = window.performance ? window.performance.timing : null;

  const geolocationData = await new Promise((resolve, reject) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
          });
        },
        () => reject("Geolocation not available")
      );
    } else {
      reject("Geolocation not supported");
    }
  }).catch(() => "Geolocation unavailable");

  const plugins = [];
  if (navigator.plugins) {
    for (let i = 0; i < navigator.plugins.length; i++) {
      const plugin = navigator.plugins[i];
      plugins.push({
        name: plugin.name,
        description: plugin.description,
        filename: plugin.filename,
      });
    }
  }
  const mediaDevicesAvailable = navigator.mediaDevices ? true : false;
  const mediaDevices = mediaDevicesAvailable
    ? await navigator.mediaDevices.enumerateDevices()
    : [];

  const audioInputs = mediaDevices.filter((device) => device.kind === "audioinput");
  const videoInputs = mediaDevices.filter((device) => device.kind === "videoinput");
  const audioOutputs = mediaDevices.filter((device) => device.kind === "audiooutput");

  let cameraAccess = false;
  let microphoneAccess = false;
  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
    try {
      await navigator.mediaDevices.getUserMedia({ video: true });
      cameraAccess = true;
    } catch {
      cameraAccess = false;
    }

    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      microphoneAccess = true;
    } catch {
      microphoneAccess = false;
    }
  }
  const screenSharingSupport = navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia ? true : false;

  let usbDevices = [];
  if (navigator.usb) {
    try {
      usbDevices = await navigator.usb.getDevices();
    } catch (error) {
      console.warn("Unable to fetch USB devices:", error);
    }
  }
  const mediaCapabilities = navigator.mediaCapabilities || "Not Supported";

  const usbSupport = navigator.usb ? true : false;

  let audioContextInfo = {};
  if (window.AudioContext || window.webkitAudioContext) {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    audioContextInfo = {
      sampleRate: audioContext.sampleRate,
      outputLatency: audioContext.outputLatency,
    };
    audioContext.close();
  }

  const storageEstimate =
    navigator.storage && navigator.storage.estimate
      ? await navigator.storage.estimate()
      : {quota: 'Unknown', usage: 'Unknown'};

  const isScreenExtended = screen.isExtended;
  const displayCount = await new Promise(async resolve => {
    try {
      const res = await window.getScreenDetails();
      resolve(res.screens.length);
    } catch {
      resolve(1);
    }
  });

  const metaData = {
    userAgent,
    language,
    onlineStatus: navigator.onLine,
    connectionType,
    memory,
    cpuCores,
    screenWidth,
    screenHeight,
    isScreenExtended,
    displayCount,
    timeZone,
    hasOrientation,
    downlink,
    bluetooth,
    clipboard,
    contacts,
    credentials,
    geolocation,
    gpu,
    hid,
    keyboard,
    mediaDevices,
    serviceWorker,
    geolocationData,
    os,
    browser,
    plugins,
    maxTouchPoints,
    languages,
    vendor,
    platform,
    colorDepth,
    batteryLevel,
    availWidth,
    availHeight,
    pixelDepth,
    performanceTiming,
    mediaDevicesAvailable,
    audioInputs: audioInputs.map((device) => device.label || "Unknown Audio Input"),
    videoInputs: videoInputs.map((device) => device.label || "Unknown Video Input"),
    audioOutputs: audioOutputs.map((device) => device.label || "Unknown Audio Output"),
    cameraAccess,
    microphoneAccess,
    screenSharingSupport,
    usbDevices: usbDevices.map((device) => ({
      productName: device.productName,
      manufacturerName: device.manufacturerName,
    })),
    mediaCapabilities,
    usbSupport,
    audioContextInfo,
    storageEstimate,
  };

  metaInfo.current = metaData;

  sendMetaInfoToBackend(metaData);
};