export function disableShortcuts(event) {
  const activeElement = document.activeElement;
  if (activeElement?.tagName === 'TEXTAREA' && activeElement.classList.contains('custom-textarea')) {
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      console.log('Shortcut Ctrl+S is disabled.');
      return;
    }
    return; 
  }

  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'c':
      case 'x':
      case 'v':
      case 's': 
        event.preventDefault();
        console.log(`Shortcut Ctrl+${event.key.toUpperCase()} is disabled.`);
        break;
      default:
        break;
    }
  }
}
