import redaxios from 'redaxios';
import axios from 'axios';
import {setCookie, getCookie} from 'cookies-next';
import {
  BASE_URL,
  SPEECH_TOKEN_REFRESH_INTERVAL_IN_SEC,
} from '@/lib/utilities/globalConstants';
import {auth} from '../firebase/firebase';
import {trackEvent} from '../firebase/firebaseWrapper';
import {getMediasoupEndpoint} from './mediasoupService';

export const getFinalCompanyId = companyId => {
  if (!!companyId) return companyId;
  else return '';
};

export const getGeneratedQuestions = async submissionId => {
  const res = await redaxios.get(
    `${BASE_URL}super-admin/get-generated-questions/${submissionId}`,
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const scheduleInterview = async interviewDetails => {
  const res = await redaxios.post(
    `${BASE_URL}super-admin/schedule-interview`,
    {...interviewDetails},
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const getAssignmentDetials = async (assignmentId, companyId) => {
  const res = await redaxios.get(
    `${BASE_URL}get-assignment-details/${assignmentId}`,
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        companyid: getFinalCompanyId(companyId),
      },
    },
  );
  return res.data;
};

export const submitAssignmentByGithubLink = async (
  URL,
  assignmentId,
  companyId,
) => {
  const res = await redaxios.post(
    `${BASE_URL}save-user-assignment-by-github-link/${assignmentId}`,
    {
      assignmentLink: URL,
      assignmentId: assignmentId,
      companyId: getFinalCompanyId(companyId),
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        companyid: getFinalCompanyId(companyId),
      },
    },
  );
  return res.data;
};

export const submitAssignmentByDirectoryUpload = async (
  files,
  assignmentId,
  uploadProgressCallback,
  signal,
  companyId,
) => {
  const formData = new FormData();
  files.forEach((file, index) => {
    formData.append(file.path, file);
  });

  try {
    const res = await axios.post(
      `${BASE_URL}save-user-assignment-by-directory-upload/${assignmentId}`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
          companyid: getFinalCompanyId(companyId),
        },
        onUploadProgress: progressEvent => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total,
          );
          uploadProgressCallback(progress);
        },
        signal: signal,
      },
    );
    return res.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Upload cancelled');
      throw {
        response: {
          data: {
            success: false,
            code: 'upload-cancelled',
            message: 'Upload cancelled',
          },
        },
      };
    }
    throw error;
  }
};

export const getFeedbackQuestions = async companyId => {
  try {
    const token = await auth.currentUser.getIdToken();
    const res = await redaxios.get(`${BASE_URL}getFeedbackQuestions`, {
      headers: {
        Authorization: `Bearer ${token}`,
        companyid: companyId,
      },
    });
    return res.data.questions;
  } catch (error) {
    console.error('Error fetching questions:', error);
    throw new Error('Failed to fetch questions. Please try again later.');
  }
};

export const fillFeedback = async (roomId, companyId, feedbackData) => {
  const res = await redaxios.post(
    `${BASE_URL}fill-feedback`,
    {
      roomid: roomId,
      feedback: feedbackData,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        companyid: getFinalCompanyId(companyId),
      },
    },
  );
  return res.data;
};

export const checkRoom = async (roomId, companyId) => {
  const res = await redaxios.post(
    `${BASE_URL}check-room/${roomId}`,
    {
      email: auth.currentUser.email,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        companyid: getFinalCompanyId(companyId),
      },
    },
  );
  return res.data;
};

export const checkMediasoupConnection = async (companyId, roomId) => {
  const mediaserverEndpoint = await getMediasoupEndpoint(companyId, roomId);

  const checkConnectionURL = new URL('check-connection', mediaserverEndpoint)
    .href;

  const res = await redaxios.post(
    checkConnectionURL,
    {
      roomId,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const disconnectMediasoupConnection = async (companyId, roomId) => {
  const mediaserverEndpoint = await getMediasoupEndpoint(companyId, roomId);

  const disconnectUrl = new URL('disconnect', mediaserverEndpoint).href;

  const res = await redaxios.post(
    disconnectUrl,
    {
      roomId,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const connectToRoom = async (
  roomId,
  companyId,
  conversationMode,
  reconnect,
) => {
  const res = await redaxios.post(
    `${BASE_URL}join-room`,
    {
      roomId,
      companyId,
      conversationMode,
      reconnect,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const connectToTutorialRoom = async (roomId, companyId, reconnect) => {
  const res = await redaxios.post(
    `${BASE_URL}join-tutorial-room`,
    {
      roomId,
      companyId,
      reconnect,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

async function getSpeechToken() {
  const res = await redaxios.get(`${BASE_URL}get-speech-token`, {
    headers: {
      Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
    },
  });
  const {token, region, expiresIn} = res.data;
  const speechToken = region + ':' + token;
  const timestamp = new Date().getTime();

  setCookie('speech-token', JSON.stringify({speechToken, timestamp}), {
    maxAge: SPEECH_TOKEN_REFRESH_INTERVAL_IN_SEC,
  });
  // console.log('Token fetched from back-end: ' + token,region, expiresIn);

  return {authToken: token, region: region, expiresIn};
}

export async function getTokenOrRefresh() {
  console.log('Fetching speech token');
  const speechTokenCookie = getCookie('speech-token');
  const currentTime = new Date().getTime();

  if (!speechTokenCookie) {
    try {
      const res = await getSpeechToken();
      return res;
    } catch (err) {
      console.log(err);
      trackEvent('getTokenOrRefresh', {
        err: {
          message: err.message || 'No error message available',
          stack: err.stack || 'No stack trace available',
        },
      });
      reportError('getTokenOrRefresh', err, false);
      return {authToken: null, error: err};
    }
  } else {
    const speechTokenData = JSON.parse(speechTokenCookie);
    const {timestamp, speechToken} = speechTokenData;
    console.log('Token fetched from cookie: ' + speechToken);
    const idx = speechToken.indexOf(':');
    const expiresIn =
      (SPEECH_TOKEN_REFRESH_INTERVAL_IN_SEC * 1000 -
        (currentTime - timestamp)) /
      1000;

    if (expiresIn < 60) {
      console.log('Token expired, fetching new token');
      try {
        const res = await getSpeechToken();
        return res;
      } catch (err) {
        console.log(err);
        trackEvent('getTokenOrRefresh', {
          err: {
            message: err.message || 'No error message available',
            stack: err.stack || 'No stack trace available',
          },
        });
        return {authToken: null, error: err};
      }
    }

    return {
      authToken: speechToken.slice(idx + 1),
      region: speechToken.slice(0, idx),
      expiresIn, // in seconds
    };
  }
}

export const checkUserAssignmentValid = async submissionId => {
  const res = await redaxios.get(
    `${BASE_URL}check-assignment/${submissionId}`,
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const checkAlreadyBooked = async submissionId => {
  const res = await redaxios.get(
    `${BASE_URL}check-already-booked/${submissionId}`,
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const getNumSlotsPerDay = async (submissionId, companyId) => {
  const res = await redaxios.get(`${BASE_URL}get-num-slots/${submissionId}`, {
    headers: {
      Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      companyid: getFinalCompanyId(companyId),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
  });
  return res.data;
};

export const getAvailableSlots = async (date, submissionId, companyId) => {
  const res = await redaxios.post(
    `${BASE_URL}get-available-slots`,
    {
      date,
      submissionId,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        companyid: getFinalCompanyId(companyId),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
    },
  );

  return res.data;
};

export const bookSlot = async (data, companyId) => {
  const res = await redaxios.post(
    `${BASE_URL}book-slot`,
    {...data},
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        companyid: getFinalCompanyId(companyId),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
    },
  );
  return res.data;
};

export const getRepoAllocationDetails = async (
  companyId,
  formId,
  assignmentPoolId,
) => {
  const res = await redaxios.get(
    `${BASE_URL}super-admin/get-repo-allocation-details/${getFinalCompanyId(companyId)}/${formId}/${assignmentPoolId}`,
    {
      headers: {
        authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const sendAssignmentForParsing = async (
  companyId,
  formId,
  assignmentPoolId,
  assignmentLink,
) => {
  const res = await redaxios.post(
    `${BASE_URL}super-admin/allocate-repo-interview/${getFinalCompanyId(companyId)}/${formId}/${assignmentPoolId}`,
    {
      assignmentLink,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const getQuestionsApprovalDetails = async params => {
  const res = await redaxios.post(
    `${BASE_URL}super-admin/get-allocated-questions/`,
    {
      ...params,
      companyId: getFinalCompanyId(params.companyId),
    },
    {
      headers: {
        authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const approveQuestions = async (approved, params) => {
  const res = await redaxios.post(
    `${BASE_URL}super-admin/approve-allocated-questions/`,
    {
      approved: approved,
      ...params,
      companyId: getFinalCompanyId(params.companyId),
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
      },
    },
  );
  return res.data;
};

export const cancelSlot = async (submissionId, companyId) => {
  const res = await redaxios.post(
    `${BASE_URL}cancel-slot/${submissionId}`,
    {
      submissionId,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        companyid: getFinalCompanyId(companyId),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
    },
  );
  return res.data;
};

export const rescheduleSlot = async (data, companyId) => {
  const res = await redaxios.post(
    `${BASE_URL}book-slot`,
    {
      ...data,
      isRescheduleRequest: true,
    },
    {
      headers: {
        Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        companyid: getFinalCompanyId(companyId),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
    },
  );
  return res.data;
};

export const reportError = async (errorFunction, error, isSendMail) => {
  let deviceDetails;
  try {
    deviceDetails = {
      userAgent: navigator.userAgent,
      userAgentData: navigator.userAgentData,
    };
  } catch (error) {}
  try {
    const res = await redaxios.post(
      `${BASE_URL}report-error`,
      {
        error,
        deviceDetails: deviceDetails,
        errorFunction,
        isSendMail,
      },
      {
        headers: {
          Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
        },
      },
    );
    return res.data;
  } catch (err) {
    console.error('Error reporting error:', err);
  }
};

export const sendImages = async (
  frameData,
  roomId,
  testType,
  createCSV = false,
) => {
  const formData = new FormData();

  if (testType === 'camera') {
    formData.append('testType', testType);
    formData.append('createCSV', createCSV);
    frameData.forEach((blob, index) => {
      formData.append(`frame_${index}`, blob, `frame_${index}.jpg`);
    });
  } else if (testType === 'click') {
    formData.append('testType', testType);
    frameData.frames.forEach((blob, index) => {
      formData.append(`frame_${index}`, blob, `frame_${index}.jpg`);
    });
    formData.append('clickPosition', JSON.stringify(frameData.clickPosition));
    formData.append(
      'screenDimensions',
      JSON.stringify(frameData.screenDimensions),
    );
    formData.append('stepId', frameData.stepId);
    formData.append('timestamp', frameData.timestamp);
  } else {
    throw new Error('Invalid test type');
  }

  const response = await fetch(`${BASE_URL}send-image/${roomId}`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Server error: ${response.status}`);
  }

  const result = await response.json();
  return result;
};
