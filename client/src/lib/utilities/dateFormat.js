function formatDateInfo(startTime, endTime) {
  const months = [
    "jan",
    "feb",
    "mar",
    "apr",
    "may",
    "jun",
    "jul",
    "aug",
    "sep",
    "oct",
    "nov",
    "dec",
  ];

  const start = new Date(startTime);
  const end = new Date(endTime);

  const formatTime = (date) => {
    const hours = date.getUTCHours().toString().padStart(2, "0");
    const minutes = date.getUTCMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const formatDate = (date) => {
    const day = date.getUTCDate();
    const month = months[date.getUTCMonth()];
    const year = date.getUTCFullYear();

    return `${day} ${month} ${year}`;
  };

  const formattedStartTime = `${formatDate(start)} ~ ${formatTime(start)}`;
  const formattedEndTime = `${formatDate(end)} ~ ${formatTime(end)}`;

  const roomLengthInMinutes = (end - start) / 60000;

  return {
    roomLengthInMinutes,
    day: start.getUTCDate(),
    month: months[start.getUTCMonth()],
    formattedStartTime,
    formattedEndTime,
  };
}

export { formatDateInfo };
