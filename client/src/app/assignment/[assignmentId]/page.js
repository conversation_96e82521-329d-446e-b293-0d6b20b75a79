'use client';
import SubmissionForm from '@/components/assignment/SubmissionForm';
import {trackEvent} from '@/lib/firebase/firebaseWrapper';
import { getAssignmentDetials, reportError } from '@/lib/utilities/api';
import {UserAuth} from '@/provider/AuthProvider';
import errors from '@/values/errorMessages';
import {useEffect, useRef, useState} from 'react';

const page = ({params}) => {
  const {assignmentId} = params;
  const {user, initializing, authChecked} = UserAuth();
  const problemStatementIframe = useRef(null);
  const [error, setError] = useState(null);
  const [displayError, setDisplayError] = useState(null); // this error will be displayed on the page along with assignment details instead of covering the whole page. (It is used in case assignment deadline has passed or assignment has already been submitted)
  const [assignmentDetails, setAssignmentDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState(false);

  const fetchAssignmentDetails = async () => {
    try {
      const response = await getAssignmentDetials(assignmentId);

      setAssignmentDetails(response.data);
    } catch (err) {
      console.log('Error fetching assignment details:', err);
      trackEvent('fetchAssignmentDetailsError', {
        err: {
          message: err.message || 'No error message available',
          stack: err.stack || 'No stack trace available',
        },
      });
      if (err?.data?.code === 'alreadySubmitted') {
        setSubmissionStatus(true);
        setAssignmentDetails(err.data.data);
      }
      if (
        err?.data?.code === 'alreadySubmitted' ||
        err?.data?.code === 'deadlinePassed'
      ) {
        setDisplayError(err.data.message);
        setAssignmentDetails(err.data.data);
        return;
      }

      if (err?.data?.message) {
        setError(err.data.message);
      } else {
        let errorCode = err?.data;
        if (!errors.hasOwnProperty(errorCode))
          errorCode = 'internal-server-error';
        setError(errors[errorCode].message);
      }

      reportError("fetchAssignmentDetails", err, false);
    }
  };

  const onLoad = () => {
    problemStatementIframe.current.height =
      Number.parseInt(
        problemStatementIframe.current.contentWindow.document.body.scrollHeight,
      ) + 40;
    console.log(
      problemStatementIframe.current.contentWindow.document.body.scrollHeight,
      problemStatementIframe.current.style.height,
    );
  };

  useEffect(() => {
    if (initializing || !user || !authChecked) return;
    fetchAssignmentDetails();
  }, [initializing, user, authChecked]);

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <p className="font-regular text-center text-lg text-destructive">
          {error}
        </p>
      </div>
    );
  }

  return (
    <div className="flex h-full w-full flex-col">
      <h3 className="text-4xl font-bold text-primary">
        EvalAI Assignment Submission
      </h3>
      {displayError && (
        <p className="mt-4 text-xl text-destructive">{displayError}</p>
      )}
      <h6 className="text-2xl font-bold text-primary">Problem Statement</h6>
      <div className="mb-10 flex h-full w-full flex-col md:flex-row">
        <div className="w-full md:max-h-[calc(100vh-10rem)] md:w-1/2 md:overflow-y-auto">
          <iframe
            title="HTML Content"
            id="frame"
            ref={problemStatementIframe}
            onLoad={onLoad}
            srcDoc={
              assignmentDetails?.problemStatement ||
              '<p>Loading assignment details...</p>'
            }
            style={{
              width: '100%',
              height: '100%',
              minHeight: 200,
              border: 'none',
            }}
          />
        </div>
        <div className="w-full md:max-h-[calc(100vh-10rem)] md:w-1/2 md:overflow-y-auto">
          <SubmissionForm
            assignmentId={assignmentId}
            assignmentDetails={assignmentDetails}
            disabled={!!displayError}
            loading={loading}
            submissionStatus={submissionStatus}
            setLoading={setLoading}
            setSubmissionStatus={setSubmissionStatus}
          />
        </div>
      </div>
    </div>
  );
};

export default page;
