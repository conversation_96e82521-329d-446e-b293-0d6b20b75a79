'use client';

import Chip from '@/components/Chip';
import {UserAuth} from '@/provider/AuthProvider';
import { useSearchParams } from 'next/navigation';

const {default: Loader} = require('@/components/Loader');
const {
  BASE_URL,
} = require('@/lib/utilities/globalConstants');
const {useState, useEffect, useRef} = require('react');

const page = () => {
  // const {repoUrl, branch} = params;
  const {user} = UserAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [uid, setUid] = useState(user?.uid);
  const codeBrowserIframeRef = useRef(null);
  const searchParams = useSearchParams();
  const repoUrl = searchParams.get("url");
  const branch = searchParams.get("branch");

  const handleDownloadZip = async () => {
    try {
      const response = await fetch(`${BASE_URL}download-zip`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify({
          repoUrl: repoUrl,
          branch: branch,
          uid: uid,
          is_admin: true,
        }),
      });

      if (response.status !== 200) {
        throw new Error('Error downloading file');
      }
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${uid}.zip`;
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(link.href);
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  useEffect(() => {
    if (!user || !user?.uid) return;
    setUid(user.uid);
  }, [user]);

  useEffect(() => {
    setLoading(true);
    if (!user || !user?.accessToken || !uid) return;
    try {
      const domain = ".neusort.com";
      document.cookie = `authToken=${user.accessToken}; path=/; domain=${domain}; Secure; SameSite=Lax;`;
      document.cookie = `repoData=${JSON.stringify({
        repoUrl: decodeURIComponent(repoUrl),
        branch: decodeURIComponent(branch),
        uid: uid,
        is_admin: true,
      })}; path=/; domain=${domain}; Secure; SameSite=Lax;`;
    } catch (error) {
      console.error(error);
      setError(error);
    } finally {
      setLoading(false);
    }
  }, [user, uid, repoUrl]);

  if (loading) return <Loader />;

  if (error)
    return (
      <div className="absolute left-0 top-0 flex h-dvh w-dvw flex-col items-center justify-center px-4 sm:px-24">
        <p className="font-regular text-center text-lg text-destructive">
          An error occured
        </p>
      </div>
    );

  return (
    <div style={{width: '100%', height: '100vh', position: 'relative'}}>
      <Chip
        className="absolute right-5 top-6 z-10 cursor-pointer rounded border border-[#30363d] bg-[#0d1117] text-[#c9d1d9] hover:border-[#30363d] hover:bg-[#161b22]"
        title="Download"
        onClick={handleDownloadZip}
        src={'/download.svg'}>
        Download
      </Chip>
      <iframe
        title="Code Browser"
        id="code-browser"
        ref={codeBrowserIframeRef}
        src={`${BASE_URL}assignment-repo/${uid}/`}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
        }}
        sandbox="allow-same-origin allow-scripts"
      />
    </div>
  );
};

export default page;
