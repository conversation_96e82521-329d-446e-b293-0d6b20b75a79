import {Fira_Sans_Condensed as FontSans} from 'next/font/google';
import {cn} from '@/lib/utils';
import {AuthProvider} from '@/provider/AuthProvider';
import {UserMediaProvider} from '@/provider/UserMediaProvider';
import {InterviewProvider} from '@/provider/InterviewProvider';
import {Toaster} from '@/components/ui/toaster';
import {Suspense} from 'react';

import './globals.css';
import {TutorialProvider} from '@/provider/TutorialProvider';

const originalLog = console.log;

console.log = function (...args) {
  const timestamp = new Date().toISOString();
  originalLog.apply(console, [timestamp, ...args]);
};

export const fontSans = FontSans({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-sans',
});

export const metadata = {
  title: 'EvalAi',
  description: 'EvalAi is a platform for conducting ai interviews',
  lang: 'en',
};

export default function RootLayout({children}) {
  return (
    <html lang="en">
      <head>
        <meta httpEquiv="Content-Language" content="en" />
        <meta name="google" content="notranslate" />
      </head>
      <body
        className={cn(
          'min-h-dvh bg-background font-sans antialiased',
          fontSans.variable,
        )}>
        <Suspense>
          <AuthProvider>
            <UserMediaProvider>
              <InterviewProvider>
                <TutorialProvider>
                  {children}
                  <Toaster />
                </TutorialProvider>
              </InterviewProvider>
            </UserMediaProvider>
          </AuthProvider>
        </Suspense>
      </body>
    </html>
  );
}
