'use client';

import Chip from '@/components/Chip';
import Loader from '@/components/Loader';
import ScheduleInterviewForm from '@/components/interview/ScheduleInterviewForm';
import {toast} from '@/components/ui/use-toast';
import { getGeneratedQuestions, reportError } from '@/lib/utilities/api';
import {UserAuth} from '@/provider/AuthProvider';
import errors from '@/values/errorMessages';

const {trackEvent} = require('@/lib/firebase/firebaseWrapper');
const {useEffect, useState} = require('react');

const ScheduleInterview = ({params}) => {
  const {submissionId} = params;
  const {user} = UserAuth();
  const [roomId, setRoomId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [generatedQuestions, setGeneratedQuestions] = useState([]);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  useEffect(() => {
    user
      ?.getIdTokenResult()
      .then(async idTokenResult => {
        if (idTokenResult.claims.superAdmin) {
          setIsSuperAdmin(true);
          try {
            const generatedQuestions =
              await getGeneratedQuestions(submissionId);
            setGeneratedQuestions(generatedQuestions);
          } catch (err) {
            console.log('Error getting generated questions', err);
            trackEvent('getGeneratedQuestions', {
              err: {
                message: err.message || 'No error message available',
                stack: err.stack || 'No stack trace available',
              },
            });
            let errorCode = err?.data;
            if (!errors.hasOwnProperty(errorCode)) {
              errorCode = 'internal-server-error';
            }

            setError(errors[errorCode].message);
            reportError("getGeneratedQuestions", err, false);
          }
        } else {
          console.log('Access denied');
          setIsSuperAdmin(false);
          setError('Access denied');
        }
      })
      .catch(err => {
        console.log('Error getting id token result', err);
        trackEvent('getIdTokenResultError', {err});
        setError(err);
        reportError("getIdTokenResultError", err, false);
      })
      .finally(() => {
        setLoading(false);
      });
    // else
    //   window.location.href = `https://neusort.com/signin?next=${window.location.href}`;
  }, [user]);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="absolute left-0 top-0 flex h-dvh w-dvw flex-col items-center justify-center px-4 sm:px-24">
        <p className="font-regular text-center text-lg text-destructive">
          Error: {error}
        </p>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col gap-4 py-5 sm:w-1/2">
      {roomId && (
        <div className="flex flex-col gap-4">
          <h2 className="text-2xl font-semibold">Interview Scheduled</h2>
          <div className="flex flex-wrap gap-4">
            <Chip
              onClick={() => {
                navigator.clipboard.writeText(roomId);
                toast({
                  variant: 'success',
                  description: 'Room ID copied to clipboard',
                });
              }}
              src="/icons/language.svg">
              Room ID: {roomId}
            </Chip>
          </div>
        </div>
      )}
      <div className="flex w-full justify-between">
        <h2 className="text-2xl font-semibold">Schedule New Interview</h2>
      </div>
      <div className="flex flex-wrap gap-4">
        <Chip src={'/icons/language.svg'}>Submission ID: {submissionId}</Chip>
      </div>
      <ScheduleInterviewForm
        submissionId={submissionId}
        firebaseLink={generatedQuestions.firebaseLink}
        questions={generatedQuestions.questions?.flat(1)}
        userEmail={generatedQuestions.userEmail}
        setRoomId={setRoomId}
      />
    </div>
  );
};

export default ScheduleInterview;
