import {Navbar} from '@/components/Navbar';
import Link from 'next/link';
import {buttonVariants} from '@/components/ui/button';

export default function NotFound() {
  const errorPossibleCauses = [
    'The requested resource has been deleted or moved to a different URL.',
    'The URL was mistyped into the browser.',
    'Your firewall blocked the request.',
  ];
  return (
    <div className="h-screen w-full">
      <Navbar />
      <div className="container flex flex-col items-center justify-center py-40">
        <div className="flex w-full flex-col gap-8">
          <h2 className="text-xl font-bold">Not Found</h2>
          <p>Could not find requested resource</p>
          <div>
            <p className="font-bold">
              Possible reasons for this error include the following:
            </p>
            <ul className='pl-4'>
              {errorPossibleCauses.map((cause, index) => (
                <li key={index} className="list-disc">
                  {cause}
                </li>
              ))}
            </ul>
          </div>
          <Link className={buttonVariants({className: 'w-fit'})} href="/">
            Return Home
          </Link>
        </div>
      </div>
    </div>
  );
}
