import {EXCLUDED_FOLDERS} from '@/values/assignment';

const page = () => {
  return (
    <div className="p-2">
      <h2 className="font-medium"># Excluded Folders</h2>
      <p className="text-xs">
        These folders are excluded from the assignment submission.
      </p>
      <ul className="mt-1 text-sm">
        {EXCLUDED_FOLDERS.map((folder, index) => (
          <li key={index}>{folder}</li>
        ))}
      </ul>
    </div>
  );
};

export default page;
