'use client';

import Loader from '@/components/Loader';
import Calendar from '@/components/BookSlot/Calendar';
import SlotTimings from '@/components/BookSlot/SlotTimings';
import {BookSlot} from '@/provider/BookSlotProvider';
import {ClockIcon, EnvelopeClosedIcon} from '@radix-ui/react-icons';

const page = () => {
  const {loading, error, calendarData, selectedDate, slotBooked} = BookSlot();

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="absolute left-0 top-0 flex h-dvh w-dvw flex-col items-center justify-center px-4 sm:px-24">
        <p className="font-regular text-center text-lg text-destructive">
          {error}
        </p>
      </div>
    );
  }

  if (slotBooked) {
    return (
      <div className="absolute left-0 top-0 flex h-dvh w-dvw flex-col items-center justify-center px-4 sm:px-24">
        <p className="font-regular text-center text-lg">
          Your Interview is confirmed. A copy of the interview information has
          been sent to your email.
        </p>
      </div>
    );
  }

  return (
    <div className="py-4">
      <h2 className="mb-6 text-2xl font-semibold ">Book Slot</h2>
      <div className="grid grid-cols-1 gap-4 align-top md:grid-cols-2 lg:grid-cols-3 lg:gap-8">
        <div className="col-span-1 md:col-span-2 lg:col-span-1">
          <p className="text-md">
            <ClockIcon size={24} className="mr-2 inline" />
            {calendarData.roomLengthInMinutes} min
          </p>
          <p className="text-md">
            <EnvelopeClosedIcon size={24} className="mr-2 inline" />
            Web conferencing details provided upon confirmation
          </p>
        </div>
        <div className="flex flex-col items-start justify-start lg:items-center">
          <h3 className="mb-4 w-full text-left font-semibold">Select Date</h3>
          <Calendar />
        </div>
        {selectedDate && (
          <div className="flex flex-col items-start justify-start ">
            <h3 className="mb-4 font-semibold">Select Time Slot</h3>
            <SlotTimings />
          </div>
        )}
      </div>
    </div>
  );
};

export default page;
