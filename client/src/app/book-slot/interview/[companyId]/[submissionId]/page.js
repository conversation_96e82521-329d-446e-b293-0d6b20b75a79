'use client';

import Loader from '@/components/Loader';
import Calendar from '@/components/BookSlot/Calendar';
import SlotTimings from '@/components/BookSlot/SlotTimings';
import {BookSlot} from '@/provider/BookSlotProvider';
import {ClockIcon, EnvelopeClosedIcon} from '@radix-ui/react-icons';
import {Button} from '@/components/ui/button';
import LoadingSpinner from '@/components/LoadingSpinner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

const page = () => {
  const {
    loading,
    error,
    calendarData,
    selectedDate,
    slotBooked,
    slotDetails,
    reschedulingSlot,
    setReschedulingSlot,
    handleCancel,
    onOpenCancelDialog,
    cancelOpen,
    interviewTaken,
  } = BookSlot();

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="absolute left-0 top-0 flex h-dvh w-dvw flex-col items-center justify-center px-4 sm:px-24">
        <p className="font-regular text-center text-lg text-destructive">
          {error}
        </p>
      </div>
    );
  }

  return (
    <div className="size-full py-4">
      {slotBooked && !reschedulingSlot && (
        <div
          className={`flex ${!reschedulingSlot && 'h-full'} w-full flex-col items-center justify-center px-4 sm:px-24`}>
          <p className="font-regular text-center text-lg">
            Your Interview is confirmed. A copy of the interview information has
            been sent to your email.
          </p>
          <div className="mt-4">
            <p className="text-md">
              <ClockIcon size={24} className="mr-2 inline" />
              {slotDetails?.roomLengthInMinutes} minutes
            </p>
            <p className="text-md">
              <EnvelopeClosedIcon size={24} className="mr-2 inline" />
              from {slotDetails?.formattedStartTime} to{' '}
              {slotDetails?.formattedEndTime}
            </p>
            <div className="mt-4 flex gap-4">
              {interviewTaken ? (
                <p className="text-destructive">Interview already taken.</p>
              ) : (
                <div className="">
                  <p className="text-md">
                    You may join the interview by clicking on this{' '}
                    <a
                      href={slotDetails?.interviewLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 underline">
                      link
                    </a>
                  </p>
                  <div className="mt-4 flex gap-4">
                    <Button onClick={() => setReschedulingSlot(true)}>
                      Reschedule
                    </Button>
                    <Dialog open={cancelOpen} onOpenChange={onOpenCancelDialog}>
                      <DialogTrigger asChild={true}>
                        <Button variant="destructive">Cancel Slot</Button>
                      </DialogTrigger>
                      <DialogContent
                        className="sm:max-w-[425px]"
                        onInteractOutside={e => {
                          e.preventDefault();
                        }}>
                        <DialogHeader>
                          <DialogTitle>Cancel Slot</DialogTitle>
                        </DialogHeader>
                        <DialogDescription>
                          Are you sure you want to cancel the slot from{' '}
                          {slotDetails?.formattedStartTime} to{' '}
                          {slotDetails?.formattedEndTime}? <br /> Even after
                          cancelling, you can reschedule the slot by visiting
                          the same link.
                        </DialogDescription>

                        <DialogFooter>
                          <Button type="submit" onClick={handleCancel}>
                            {loading ? <LoadingSpinner /> : 'Confirm'}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {(!slotBooked || reschedulingSlot) && (
        <>
          <h2 className="mb-6 text-2xl font-semibold ">
            {slotBooked ? 'Reschedule' : 'Book'} Slot
          </h2>
          <div className="grid grid-cols-1 gap-4 align-top md:grid-cols-2 lg:grid-cols-3 lg:gap-8">
            <div className="col-span-1 md:col-span-2 lg:col-span-1">
              <p className="text-md">
                <ClockIcon size={24} className="mr-2 inline" />
                {calendarData.roomLengthInMinutes} min
              </p>
              <p className="text-md">
                <EnvelopeClosedIcon size={24} className="mr-2 inline" />
                Web conferencing details provided upon confirmation
              </p>
            </div>
            <div className="flex flex-col items-start justify-start lg:items-center">
              <h3 className="mb-4 w-full text-left font-semibold">
                Select Date
              </h3>
              <Calendar />
            </div>
            {selectedDate && (
              <div className="flex flex-col items-start justify-start ">
                <h3 className="mb-4 font-semibold">Select Time Slot</h3>
                <SlotTimings />
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default page;
