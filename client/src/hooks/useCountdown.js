import {useState, useEffect} from 'react';

function useCountdown(onTimeUp, triggers = []) {
  const [remainingTime, setRemainingTime] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [endTime, setEndTime] = useState(null);

  useEffect(() => {
    let timerId;

    if (isActive) {
      timerId = setInterval(() => {
        const currentTime = Date.now();
        const newTime = endTime - currentTime;

        if (newTime <= 0) {
          clearInterval(timerId);
          setRemainingTime(0);
          onTimeUp();
          setIsActive(false);
        } else {
          setRemainingTime(newTime);
          triggers.forEach(trigger => {
            if (newTime <= trigger.time && remainingTime > trigger.time) {
              trigger.callback();
            }
          });
        }
      }, 1000);
    }

    return () => clearInterval(timerId);
  }, [isActive, onTimeUp, triggers, endTime, remainingTime]);

  const setTime = milliseconds => {
    setEndTime(Date.now() + milliseconds);
    setRemainingTime(milliseconds);
    setIsActive(true);
  };

  const stop = () => {
    setIsActive(false);
    setRemainingTime(0);
  };

  const formatTime = time => {
    const hours = Math.floor(time / 3600000);
    const minutes = Math.floor((time - hours * 3600000) / 60000);
    const seconds = Math.floor(
      (time - hours * 3600000 - minutes * 60000) / 1000,
    );

    const formatNumber = num => `0${num}`.slice(-2);

    return {
      hours: formatNumber(hours),
      minutes: formatNumber(minutes),
      seconds: formatNumber(seconds),
    };
  };

  return {
    remainingTime: formatTime(remainingTime),
    time: remainingTime,
    setTime,
    stop,
  };
}

export default useCountdown;
