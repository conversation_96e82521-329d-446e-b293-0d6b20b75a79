'use client';

import {useState, useEffect, useMemo, useRef} from 'react';
import {Interview} from '@/provider/InterviewProvider';
import {UserMedia} from '@/provider/UserMediaProvider';
import {
  checkMediasoupConnection,
  disconnectMediasoupConnection,
  connectToRoom,
  connectToTutorialRoom,
  reportError,
} from '@/lib/utilities/api';
import errors from '@/values/errorMessages';
import useMediasoup from '@/hooks/useMediasoup';
import {setCookie} from 'cookies-next';
import {TUTORIAL_RESET_TIMEOUT_IN_SEC} from '@/lib/utilities/globalConstants';
import prepareFiles from '@/lib/utilities/CodeRoom/prepareFiles';
import {auth} from '@/lib/firebase/firebase';
import conversationModes from '@/values/conversationModes';
import botStates from '@/values/botStates';
import {Howl} from 'howler';
import {useToast} from '@/components/ui/use-toast';
import {saveCodeRoomState} from '@/lib/utilities/codeRoomStatePersistence';
import useSpeechEvent from './useSpeechEvent';

export default function useConversation(filesData) {
  const {toast} = useToast();
  const {mediaStream, screenStream, userMediaEventListenerAbortController} =
    UserMedia();
  const {
    setError,
    conversationMode,
    setConversationMode,
    interviewDetails,
    role,
    setBlinkOnPublish,
    routeToFeedback,
    interviewEnded,
  } = Interview();

  const speechEvent = useSpeechEvent();
  const [botConnected, setBotConnected] = useState(false);
  const [botState, setBotState] = useState(botStates.IDLE);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [duration, setDuration] = useState(0);
  const [plantUMLDiagram, setPlantUMLDiagram] = useState('');
  const [mute, setMute] = useState(false);
  const [questionData, setQuestionData] = useState(null);
  const [messageHistory, setMessageHistory] = useState([]);

  const {...mediasoupSocketProps} = useMediasoup({
    onBotDisconnect: handleBotDisconnected,
    onTranscript: sendChatMessage,
  });
  const {
    messenger,
    mediasoupSocket,
    mediasoupConnected,
    roomId,
    companyId,
    peerId,
    producers,
    getSocketEventResponse,
    sendVolumeData,
    handleBotPausedRef,
  } = mediasoupSocketProps;

  speechEvent.sendVolumeDataCallback.current = sendVolumeData;

  const [botPaused, setBotPaused] = useState(
    interviewDetails?.botPaused || false,
  );
  const [cheatingActive, setCheatingActive] = useState(false);

  async function toggleCheatingStatus() {
    try {
      const newCheatingActiveState = !cheatingActive;
      const currentTimestamp = new Date().toISOString();

      messenger.emit('toggleCheating', {
        isCheatingActive: newCheatingActiveState,
        timestamp: currentTimestamp,
      });

      setCheatingActive(newCheatingActiveState);
    } catch (error) {
      console.error('Error toggling cheating status:', error);
    }
  }

  const msgSound = useRef(
    new Howl({
      src: ['/sounds/message.mp3'],
      volume: 0.5,
    }),
  ).current;

  const loading =
    role === 'interviewee'
      ? !mediasoupConnected || !botConnected
      : !mediasoupConnected;

  messenger.onNewMessage = (message, callback) => {
    console.log('new message (no CB registered):', message);
    switch (message.event) {
      case 'toggleCheating': {
        const {isCheatingActive} = message.data;
        if (role === 'interviewer') {
          setCheatingActive(isCheatingActive);
        }
      }

      case 'currentButtonsState': {
        const {isCheatingActive, paused} = message.data;
        if (role === 'interviewer') {
          setCheatingActive(isCheatingActive);
        }
        setBotPaused(paused);
      }

      case 'botStateChange': {
        changeBotState(message.data.state);
        break;
      }

      case 'response': {
        const {peerId, peerName, response} = message.data;
        if (conversationMode === conversationModes.text) msgSound.play();
        setMessageHistory(prevMessages => [
          ...prevMessages,
          {
            peerId: peerId,
            peerName: peerName,
            message: response,
          },
        ]);
        break;
      }

      case 'newQuestion': {
        if (role === 'interviewer') break;
        handleNewQuestionData(message.data);
        break;
      }

      case 'getChanges': {
        if (role === 'interviewer') break;
        callback({changes: prepareFiles(filesData.current)});
        for (let file in filesData.current) {
          filesData.current[file].sentToServer = true;
        }
        saveCodeRoomState(filesData.current);
        break;
      }

      case 'canPublish': {
        if (role === 'interviewer') break;
        setBlinkOnPublish(true);
        setTimeout(() => {
          setBlinkOnPublish(false);
        }, 20000);
        break;
      }

      case 'lastResponse': {
        handleLastResponse();
        break;
      }

      default:
        console.log('Unhandled data consumer message:', message);
    }
  };

  const joinRoom = async (roomId, companyId, reconnect = false) => {
    try {
      if (role === 'interviewer') return;
      console.log('joining room:', roomId, companyId);

      const {
        remainingTime,
        chatHistory,
        questionData,
        botPaused,
        plantUMLDiagram = '',
      } = await connectToRoom(roomId, companyId, conversationMode, reconnect);
      if (conversationMode === conversationModes.voice && !reconnect) {
        try {
          await speechEvent.startSpeechEvents();
        } catch (error) {
          console.log('Error starting speech events', error);
          reportError('startSpeechEvents', error, false);
        }
      }

      await getSocketEventResponse(mediasoupSocket, 'roomJoined', {
        roomId: roomId,
        type: 'join',
        timestamp: new Date().getTime(),
      });

      setMessageHistory([...chatHistory]);
      handleNewQuestionData(questionData);
      setPlantUMLDiagram(plantUMLDiagram);
      setDuration(remainingTime);
      setBotPaused(botPaused);
      setBotConnected(true);
    } catch (err) {
      let errorCode = err?.data;
      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = reconnect ? 'reconnect-error' : 'join-room-error';
      }
      setError(errors[errorCode]);
      reportError('joinRoom', errorCode, false);
    }
  };

  const joinTutorialRoom = async (roomId, companyId, reconnect = false) => {
    try {
      if (role === 'interviewer') return;
      console.log('joining tutorial:', roomId, companyId);
      const {remainingTime, questionData} = await connectToTutorialRoom(
        roomId.includes('_TUTORIAL') ? roomId : `${roomId}_TUTORIAL`,
        companyId,
        reconnect,
      );
      handleNewQuestionData(questionData);
      setDuration(remainingTime);
      setBotConnected(true);
    } catch (err) {
      console.error('Error joining tutorial room:', err);
      let errorCode = err?.data;
      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = reconnect ? 'reconnect-error' : 'join-tutorial-error';
      }
      setError(errors[errorCode]);
      reportError('joinTutorialRoom', errorCode, false);
    }
  };

  async function handleBotDisconnected() {
    if (interviewEnded.current) return;
    setBotConnected(false);

    if (roomId.includes('_TUTORIAL')) {
      await joinTutorialRoom(roomId, companyId, true);
    } else {
      await joinRoom(roomId, companyId, true);
    }
  }

  function changeBotState(state) {
    setBotState(state);
  }

  function handleNewQuestionData({
    question = '',
    fileName = '',
    questionNumber = '',
    totalQuestions = '',
    plantUMLDiagram = '',
    exampleInputOutput = '',
    expectedComplexity = '',
    constraints = '',
  }) {
    setQuestionData({
      question,
      fileName,
      questionNumber,
      totalQuestions,
      plantUMLDiagram,
      exampleInputOutput,
      expectedComplexity,
      constraints,
    });
  }

  const handleBotPaused = pauseBot => {
    setBotPaused(pauseBot);
  };

  async function endInterview() {
    try {
      console.log('Ending interview');
      interviewEnded.current = true;
      await getSocketEventResponse(mediasoupSocket, 'endInterview', {
        roomId,
        peerId,
      });
      console.log('Interview ended successfully');
      localStorage.clear();
      console.log('Local storage cleared');
      routeToFeedback();
      console.log('Redirecting to feedback page');
    } catch (err) {
      console.error('Error ending interview:', err);
      reportError('handleEndInterview', err, false);
    }
  }

  async function handleEndTutorial() {
    try {
      localStorage.clear();
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCookie('tutorial-finished', 'true', {
        maxAge: TUTORIAL_RESET_TIMEOUT_IN_SEC,
      });
      interviewEnded.current = true;
      await getSocketEventResponse(mediasoupSocket, 'endTutorial', {
        roomId,
        peerId,
      });
      window.location.reload();
    } catch (err) {
      console.error('Error ending tutorial interview:', err);
      reportError('handleEndTutorial', err, false);
    }
  }

  function handleLastResponse() {
    interviewEnded.current = true;
  }

  function getChatHistory() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        toast({
          variant: 'destructive',
          description: 'Request timed out. Please try again.',
        });
        reject();
      }, 60000);

      messenger.emit('getChatHistory', {}, ({chatHistory}) => {
        clearTimeout(timeout);
        setMessageHistory([...chatHistory]);
        resolve(chatHistory);
      });
    });
  }

  async function toggleConversationMode(bool) {
    const prevConversationMode = conversationMode;
    const timeoutDuration = 15000;

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () => reject(new Error('Operation timed out')),
        timeoutDuration,
      );
    });

    try {
      setConversationMode(null);

      await Promise.race([
        (async () => {
          if (bool) {
            try {
              await speechEvent.startSpeechEvents();
            } catch (error) {
              console.log('Error starting speech events', error);
            }
            await getSocketEventResponse(
              mediasoupSocket,
              'toggleMode',
              {mode: conversationModes.voice, roomId},
              () => {},
            );
            await getChatHistory();
            setConversationMode(conversationModes.voice);
          } else {
            await getSocketEventResponse(
              mediasoupSocket,
              'toggleMode',
              {mode: conversationModes.text, roomId},
              () => {},
            );
            await getChatHistory();
            speechEvent.stopSpeechEvents();
            setConversationMode(conversationModes.text);
            changeBotStatus({paused: false});
          }
        })(),
        timeoutPromise,
      ]);
    } catch (error) {
      setConversationMode(prevConversationMode);

      if (error.message === 'Operation timed out') {
        toast({
          variant: 'destructive',
          description:
            'Toggling conversation mode timed out. Please try again.',
        });
      } else {
        toast({
          variant: 'destructive',
          description: 'Error toggling conversation mode. Please try again.',
        });
      }
    }
  }

  async function changeBotStatus({paused}) {
    getSocketEventResponse(mediasoupSocket, 'togglePause', {
      roomId,
      paused,
      peerId,
    });
    handleBotPaused(paused);
  }

  function sendResponse(response) {
    console.log('response sent:', response);
    messenger.emit('response', {
      peerId: peerId,
      peerName: auth.currentUser.displayName,
      response,
      filesData: prepareFiles(filesData.current, true),
    });

    for (let file in filesData.current) {
      filesData.current[file].sentToServer = true;
    }
    saveCodeRoomState(filesData.current);
  }

  function sendChatMessage(message) {
    setMessageHistory(prevMessages => [
      ...prevMessages,
      {
        peerId: peerId,
        peerName: 'You',
        message,
      },
    ]);
    sendResponse(message);
  }

  async function connectHere(companyId, roomId) {
    try {
      await disconnectMediasoupConnection(companyId, roomId);
      setShowConfirmationDialog(false);
      mediasoupSocket.connect();
    } catch (error) {
      let errorCode = error?.data;
      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = 'internal-server-error';
      }
      setError(errors[errorCode]);
    }
  }

  function toggleMute() {
    if (mediaStream.current.getAudioTracks().length > 0) {
      mediaStream.current.getAudioTracks().forEach(track => {
        track.enabled = !track.enabled;
      });

      setMute(mute => {
        if (producers.mic) {
          if (mute) {
            producers.mic.resume();
          } else {
            producers.mic.pause();
          }
        }
        return !mute;
      });
    }
  }

  useEffect(() => {
    async function initSockets() {
      if (!mediasoupSocket) return;

      try {
        console.log('Checking mediasoup connection');
        const response = await checkMediasoupConnection(companyId, roomId);
        if (response.code === 'connected') setShowConfirmationDialog(true);
        else {
          console.log('Connecting to mediasoup');
          mediasoupSocket.connect();
        }
      } catch (error) {
        let errorCode = error?.data;
        if (!errors.hasOwnProperty(errorCode)) {
          errorCode = 'internal-server-error';
        }
        setError(errors[errorCode]);
      }
    }
    initSockets();

    return () => {
      if (!mediasoupSocket) return;

      userMediaEventListenerAbortController.current.abort();

      if (mediaStream.current) {
        mediaStream.current.getTracks().forEach(track => {
          console.log(`Stopping media stream track: ${track.id}`);
          track.stop();
        });
      } else {
        console.log('No media stream tracks to stop');
      }

      if (screenStream.current) {
        screenStream.current.getTracks().forEach(track => {
          console.log(`Stopping screen stream track: ${track.id}`);
          track.stop();
        });
      } else {
        console.log('No screen stream tracks to stop');
      }
    };
  }, [mediasoupSocket, roomId]);

  handleBotPausedRef.current = handleBotPaused;

  return {
    ...mediasoupSocketProps,
    botConnected,
    botState,
    botPaused,
    duration,
    joinRoom,
    joinTutorialRoom,
    messageHistory,
    loading,
    mute,
    toggleMute,
    endInterview,
    handleEndTutorial,
    changeBotStatus,
    sendChatMessage,
    showConfirmationDialog,
    connectHere,
    conversationMode,
    toggleConversationMode,
    changeBotState,
    questionData,
    cheatingActive,
    toggleCheatingStatus,
    plantUMLDiagram,
  };
}
