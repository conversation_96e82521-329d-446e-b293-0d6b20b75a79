import {useEffect, useState, useRef} from 'react';
import useConversation from './useConversation';
import axios from 'redaxios';
import {BASE_URL} from '@/lib/utilities/globalConstants';
import {auth} from '../lib/firebase/firebase';
import {useToast} from '@/components/ui/use-toast';
import {Interview} from '@/provider/InterviewProvider';
import {Tutorial} from '@/provider/TutorialProvider';
import {tutorialSteps} from '@/values/tutorialSteps';
import errors from '@/values/errorMessages';
import prepareFiles from '@/lib/utilities/CodeRoom/prepareFiles';
import interviewStates from '@/values/interviewStates';
import {getFinalCompanyId, reportError} from '@/lib/utilities/api';
import {saveCodeRoomState} from '@/lib/utilities/codeRoomStatePersistence';
import {getTimeStamp} from '@/lib/utilities/CodeRoom/getTime';
import botStates from '@/values/botStates';
import { setCookie } from 'cookies-next';

export default function useCodeRoom(filesData) {
  const {toast} = useToast();
  const {setTutorialStep, tutorialStep} = Tutorial();
  const {role, roomId, routeToFeedback, interviewState, interviewEnded} =
    Interview();

  const [fetchingFileData, setFetchingFileData] = useState(false);
  const [publishingChanges, setPublishingChanges] = useState(false);
  const [skippingQuestion, setSkippingQuestion] = useState(false);
  const {
    setDuration,
    messenger,
    botState,
    questionData,
    sendInputData,
    getSocketEventResponse,
    mediasoupSocket,
    ...props
  } = useConversation(filesData);

  const intervalRef = useRef(null);
  const keyDataRef = useRef([]);
  const inputCountRef = useRef({
    start_time: getTimeStamp(),
    key_count: 0,
    mouse_movement_count: 0,
    bot_speaking: 0,
    question: null,
  });
  const botThinkingRef = useRef([]);
  const focusStateRef = useRef('focused');
  const startThinkingTime = useRef(null);

  // const disableCondition =
  //   publishingChanges || fetchingFileData || skippingQuestion;
  // || !props.isConnected; // need a flag after join room request to be true
  // || !props.introductionFinished;

  // const disableEditor = props.botPaused ? false : disableCondition;
  // const disableButtons = props.botPaused ? true : disableCondition;
  const disableEditor = false;
  const disableButtons = skippingQuestion || publishingChanges;

  const startInterval = () => {
    intervalRef.current = setInterval(() => {
      sendInputData({
        keyData: keyDataRef.current,
        inputCount: inputCountRef.current,
        botThinking: botThinkingRef.current,
      });
      botThinkingRef.current = [];
      keyDataRef.current = [];
      inputCountRef.current = {
        start_time: getTimeStamp(),
        key_count: 0,
        mouse_movement_count: 0,
        bot_speaking: 0,
        question: inputCountRef.current.question,
      };
    }, 5 * 1000);
  };

  useEffect(() => {
    console.log('botState', botState);
    if (botState != botStates.LISTENING) {
      inputCountRef.current.bot_speaking += 1;
      if (startThinkingTime.current !== null) {
        botThinkingRef.current.push(
          ['start_time', startThinkingTime.current],
          ['end_time', getTimeStamp()],
        );
        startThinkingTime.current = null;
      }
    }
    if (botState == botStates.THINKING) {
      startThinkingTime.current = getTimeStamp();
    }
  }, [botState]);

  useEffect(() => {
    if (!questionData) return;
    const questionNumber = questionData.questionNumber;

    if (inputCountRef.current.question != null) {
      if (startThinkingTime.current !== null) {
        botThinkingRef.current.push(
          ['start_time', startThinkingTime.current],
          ['end_time', getTimeStamp()],
        );
        startThinkingTime.current = null;
      }
      sendInputData({
        botThinking: botThinkingRef.current,
        keyData: keyDataRef.current,
        inputCount: inputCountRef.current,
      });
    }

    botThinkingRef.current = [];
    keyDataRef.current = [];
    inputCountRef.current = {
      start_time: getTimeStamp(),
      key_count: 0,
      mouse_movement_count: 0,
      bot_speaking: 0,
      question: questionNumber,
    };

    startInterval();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [questionData]);

  const tutorialAction = async () => {
    console.log('tutorial next step');
    if (tutorialSteps.END_INTERVIEW !== tutorialStep) {
      setTutorialStep(prev => {
        messenger.emit('tutorialAction', {currentStep: prev});
        return prev + 1;
      });
    }
  };

  function submitChanges(filesData) {
    return new Promise((resolve, reject) => {
      setPublishingChanges(true);

      const filesToBeSent = prepareFiles(filesData.current, true);
      console.log('submitChanges', filesToBeSent);

      if (!filesToBeSent.length) {
        setPublishingChanges(false);
        toast({
          variant: 'destructive',
          description: 'Could not publish because no changes were made.',
        });
        resolve();
        return;
      }

      messenger.emit(
        'publishAnswer',
        {
          changes: filesToBeSent,
        },
        ({success}) => {
          setPublishingChanges(false);
          if (!success) {
            toast({
              variant: 'destructive',
              description: 'Error submitting changes. Please try again.',
            });
            reject();
          } else {
            for (let file in filesData.current) {
              filesData.current[file].sentToServer = true;
              filesData.current[file].publishedToServer = true;
            }
            saveCodeRoomState(filesData.current);
            toast({
              variant: 'success',
              description: 'Changes published successfully.',
            });
            resolve();
          }
        },
      );
    });
  }

  async function getFileData(path, allowSvg = false) {
    const bearerToken = await auth.currentUser.getIdToken();

    setFetchingFileData(true);

    try {
      const urlParts = window.location.pathname.split('/');
      const companyId = urlParts.length === 3 ? urlParts[1] : null;

      const response = await axios.post(
        `${BASE_URL}get-file-content`,
        {
          filePath: path.substring(1),
          roomId: props.roomId,
          allowSvg: allowSvg,
        },
        {
          headers: {
            Authorization: 'Bearer ' + bearerToken,
            companyid: getFinalCompanyId(companyId),
          },
        },
      );
      return response.data.content;
    } catch (error) {
      let errorCode = error?.data;
      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = 'internal-server-error';
      }

      reportError('getFileData', error, false);

      console.error('Error fetching file data', error);
      toast({
        variant: 'destructive',
        description: errors[errorCode].message,
      });
      return null;
    } finally {
      setFetchingFileData(false);
    }
  }

  function skipQuestion(filesData) {
    setSkippingQuestion(true);
    const filesToBeSent = prepareFiles(filesData.current, true);
    messenger.emit(
      'skipQuestion',
      {changes: filesToBeSent, roomId: roomId},
      ({success}) => {
        if (!success) {
          toast({
            variant: 'destructive',
            description: 'Error skipping question. Please try again.',
          });
        } else {
          for (let file in filesData.current) {
            filesData.current[file].sentToServer = true;
            filesData.current[file].publishedToServer = true;
          }
        }
        saveCodeRoomState(filesData.current);
        setSkippingQuestion(false);
      },
    );
  }

  function getSyntax(language, query) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        toast({
          variant: 'destructive',
          description: 'Request timed out. Please try again.',
        });
        reject();
      }, 60000);

      messenger.emit('getSyntax', {language, query}, data => {
        clearTimeout(timeout);
        if (!data.success) {
          toast({
            variant: 'destructive',
            description: 'Error fetching syntax. Please try again.',
          });
          reject();
        } else {
          resolve(data.syntax);
        }
      });
    });
  }

  function giveTimeWarning() {
    messenger.emit('timeWarning');
  }

  function handleTimeUp() {
    console.error('Time up!!!');
    interviewEnded.current = true;
    if (interviewState === interviewStates.CODING_TUTORIAL) {
      setCookie('tutorial-finished', 'true', {
        maxAge: TUTORIAL_RESET_TIMEOUT_IN_SEC,
      });
      messenger.emit('tutorialTimeUp', {}, () => {
        localStorage.clear();
        window.location.reload();
      });
    } else {
      const filesToBeSent = prepareFiles(filesData.current, true);
      messenger.emit(
        'interviewTimeUp',
        {
          changes: filesToBeSent,
          force: false,
        },
        () => {
          routeToFeedback();
        },
      );
    }
  }

  async function handleFocusOut() {
    if (!props.botConnected || !props.mediasoupConnected) return;
    if (focusStateRef.current === 'blurred') return;
    focusStateRef.current = 'blurred';

    await getSocketEventResponse(mediasoupSocket, 'tabFocusChange', {
      roomId: roomId,
      type: 'focusOut',
      timestamp: new Date().getTime(),
    });

    toast({
      variant: 'destructive',
      title: 'Warning',
      description: 'Tab out of focus.',
    });
  }

  async function handleFocusIn() {
    if (!props.botConnected || !props.mediasoupConnected) return;
    if (focusStateRef.current === 'focused') return;

    focusStateRef.current = 'focused';

    await getSocketEventResponse(mediasoupSocket, 'tabFocusChange', {
      roomId: roomId,
      type: 'focusIn',
      timestamp: new Date().getTime(),
    });
  }

  useEffect(() => {
    if (role === 'interviewer') return;

    const handleVisibilityChange = async () => {
      if (document.visibilityState !== 'visible') {
        await handleFocusOut();
      } else {
        await handleFocusIn();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleFocusOut);
    window.addEventListener('focus', handleFocusIn);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleFocusOut);
      window.removeEventListener('focus', handleFocusIn);
    };
  }, [props.botConnected, props.mediasoupConnected]);

  return {
    disableEditor,
    disableButtons,
    botState,
    questionData,
    inputCountRef,
    keyDataRef,
    submitChanges,
    getFileData,
    skipQuestion,
    giveTimeWarning,
    handleTimeUp,
    getSyntax,
    tutorialAction,
    sendInputData,
    ...props,
  };
}
