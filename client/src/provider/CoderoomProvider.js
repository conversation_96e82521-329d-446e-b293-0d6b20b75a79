'use client';

import React, {createContext, useContext, useRef, useState} from 'react';

import {Interview} from './InterviewProvider';
import {
  addProperties,
  mergePathsIntoStructure,
  sortItems,
} from '@/lib/utilities/CodeRoom/directory';
import {handleOperations} from '@/lib/utilities/CodeRoom/alterFilesData';
import useCodeRoom from '@/hooks/useCodeRoom';
import {
  loadCodeRoomState,
  saveCodeRoomState,
} from '@/lib/utilities/codeRoomStatePersistence';
import interviewStates from '@/values/interviewStates';
import { tutorialData, tutorialStructure } from '@/values/tutorialFiles';

const CodeRoomContext = createContext();

// import * as FILES from '@/values/files';

const CodeRoomProvider = ({children}) => {
  const { interviewDetails, interviewState } = Interview();
  const localCodeRoomState = loadCodeRoomState();
  const isTutorial = interviewStates.CODING_TUTORIAL === interviewState;
  const hasInterviewDetails = Boolean(interviewDetails);
  const defaultStructure = { name: '', items: [] };

  const structure = isTutorial
    ? tutorialStructure
    : hasInterviewDetails
      ? (localCodeRoomState
        ? mergePathsIntoStructure(
          interviewDetails.structure,
          Object.keys(localCodeRoomState).filter(
            path => localCodeRoomState[path].change !== 'delete',
          ),
        )
        : interviewDetails.structure)
      : defaultStructure;

  const initialFilesData = isTutorial ? tutorialData : (localCodeRoomState || {});
  const filesData = useRef(initialFilesData);
  const [selectedFile, selectFile] = useState();
  const [tabs, setTabs] = useState([]);
  const [files, setFiles] = useState(
    sortItems(addProperties(structure, {0: {collapsed: true}, 1: {}})),
  );
  const {...props} = useCodeRoom(filesData);

  const handleNewTabOpen = path => {
    setTabs(prevTabs => Array.from(new Set([...prevTabs, path])));
  };

  const handleTabsAfterRename = (path, changedName) => {
    const newPath = path.split('/').slice(0, -1).join('/') + '/' + changedName;
    const idx = tabs.indexOf(path);
    if (idx === -1) return handleNewTabOpen(newPath);
    const newTabs = [...tabs];
    newTabs[idx] = newPath;
    setTabs(newTabs);
  };

  const handleCloseTab = path => {
    const idx = tabs.indexOf(path);
    const nextIdx = idx === tabs.length - 1 ? idx - 1 : idx;

    const newTabs = [...tabs].filter(tab => tab !== path);
    setTabs(newTabs);

    if (selectedFile === path) {
      selectFile(newTabs[nextIdx] || null);
    }
  };

  function toggleCollapsed(path) {
    const newState = JSON.parse(JSON.stringify(files));
    const pathParts = path.split('/').filter(Boolean);

    let currentItem = newState;

    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];

      if (i === 0 && currentItem.name === part) {
        continue;
      }

      const nextItem = currentItem.items.find(item => item.name === part);
      if (!nextItem) {
        throw new Error(`Path does not exist: ${path}`);
      }
      currentItem = nextItem;
    }

    if (currentItem.type !== 0) {
      throw new Error(`Item at path is not of type 0: ${path}`);
    }
    currentItem.collapsed = !currentItem.collapsed;

    setFiles(newState);
  }

  function deleteItem(path) {
    const newState = {...files};

    const pathParts = path.split('/').filter(Boolean);

    let currentItem = newState;
    let parentItem = null;

    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];

      if (i === 0 && currentItem.name === part) {
        continue;
      }

      parentItem = currentItem;
      currentItem =
        currentItem.items && currentItem.items.find(item => item.name === part);

      if (!currentItem) {
        throw new Error(`Path does not exist: ${path}`);
      }
    }

    if (parentItem) {
      parentItem.items = parentItem.items.filter(item => item !== currentItem);
    } else {
      newState.items = newState.items.filter(item => item !== currentItem);
    }

    if (tabs.includes(path)) {
      handleCloseTab(path);
    }

    handleOperations(filesData, 0, path, '', currentItem.type);
    setFiles(newState);
  }

  function addItem(path, type) {
    const newState = {...files};
    const pathParts = path.split('/').filter(Boolean);
    let currentItem = newState;

    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];

      if (i === 0 && currentItem.name === part) {
        continue;
      }
      currentItem =
        currentItem.items && currentItem.items.find(item => item.name === part);

      if (!currentItem) {
        throw new Error(`Path does not exist: ${path}`);
      }
    }

    const baseName = type == 1 ? 'New File' : 'New Folder';
    let name = baseName;
    let counter = 1;

    while (currentItem.items.some(item => item.name === name)) {
      name = `${baseName}(${counter++})`;
    }

    const newItem = {
      name: name,
      type: type,
      inFocus: true,
      ...(type === 0 ? {items: []} : {}),
    };

    currentItem.collapsed = false;
    currentItem.items.unshift(newItem);
    handleOperations(filesData, 1, path + '/' + name, '', type);
    setFiles(newState);
  }

  const handleRename = async (
    path,
    changedName,
    originalName,
    setImmediate = false,
  ) => {
    const newState = JSON.parse(JSON.stringify(files));

    let parentFolder = null;
    let currentItem = newState;
    const pathParts = path.split('/').filter(Boolean);

    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];

      if (i === 0 && currentItem.name === part) {
        continue;
      }

      parentFolder = currentItem;
      currentItem =
        currentItem.items && currentItem.items.find(item => item.name === part);

      if (!currentItem) {
        throw new Error(`Path does not exist: ${path}`);
      }
    }

    if (
      parentFolder &&
      parentFolder.items.some(item => item.name === changedName) &&
      originalName !== changedName
    ) {
      return {success: false};
    }

    if (
      !Object.keys(filesData.current).includes(path) &&
      currentItem.type === 1
    ) {
      const content = await props.getFileData(path);
      if (content === null) {
        return {success: false};
      }
      filesData.current[path] = {
        content: content,
        change: null,
        type: 1,
        sentToServer: false,
        publishedToServer: false,
      };
      saveCodeRoomState(filesData.current);
    }

    currentItem.name = changedName;
    parentFolder = sortItems(parentFolder);
    const newPath = path.split('/').slice(0, -1).join('/') + '/' + changedName;
    handleOperations(filesData, 3, newPath, '', currentItem.type, path);
    if (currentItem.type === 1) handleTabsAfterRename(path, changedName);
    // selectFile(newPath);
    setImmediate && setFiles(newState);

    return {success: true, newFiles: newState};
  };

  const handleFileSelect = async (
    path,
    updatedFiles,
    afterRename = false,
    fetchDataFromServer = true,
  ) => {
    //also check if filesData has the path as key but the key has no content key
    if (
      (!Object.keys(filesData.current).includes(path) ||
        filesData.current[path].content == null) &&
      fetchDataFromServer
    ) {
      const content = await props.getFileData(path);
      if (content === null) {
        return;
      }
      filesData.current[path] = {
        content: content,
        change: null,
        type: 1,
        sentToServer: false,
        publishedToServer: false,
      };
      saveCodeRoomState(filesData.current);
    }

    //expand all the parent folders
    const pathParts = path.split('/').filter(Boolean);
    const newState = updatedFiles || {...files};
    let currentItem = newState;
    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      if (currentItem.name === part) {
        if (currentItem.type === 0) {
          currentItem.collapsed = false;
        }
        if (i < pathParts.length - 1) {
          let index = currentItem.items.findIndex(
            item => item.name === pathParts[i + 1],
          );
          if (index !== -1) {
            currentItem = currentItem.items[index];
          } else {
            throw new Error(`Path does not exist: ${path}`);
          }
        }
      }
    }
    setFiles(newState);
    selectFile(path);
    if (!afterRename) handleNewTabOpen(path);

    return newState;
  };

  const getSvgContent = async (path) => {
    if (
      (!Object.keys(filesData.current).includes(path))
    ) {
      const content = await props.getFileData(path, true);
      return content;
    }
    return `<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" width="24" height="24"><path fill="#d8d8ff" d="M16.52 12.91a2.845 2.845 0 0 0-3.93 0l-1.41 1.41a.967.967 0 0 0-.29.7.99.99 0 0 0 .29.71l6.23 6.23a2.894 2.894 0 0 0 1.84-.99c.483-.542.75-1.243.75-1.97v-2.62l-3.48-3.47z"/><path fill="#6563ff" d="m11.89 13.61-2.48-2.49a2.855 2.855 0 0 0-3.93 0L2 14.6V19a3.009 3.009 0 0 0 3 3h12c.138.002.275-.011.41-.04a2.894 2.894 0 0 0 1.84-.99l-7.36-7.36z"/><path fill="#b2b1ff" d="M19.828 8.862a4.082 4.082 0 0 1-5.656 0A4.003 4.003 0 0 1 13 6.02 3.971 3.971 0 0 1 13.559 4H5a3.009 3.009 0 0 0-3 3v7.6l3.48-3.48a2.855 2.855 0 0 1 3.93 0l2.48 2.49.7-.7a2.845 2.845 0 0 1 3.93 0L20 16.38V8.65c-.06.07-.106.146-.172.212z"/><path fill="#d8d8ff" d="M17 2a4.01 4.01 0 0 0-4 4.02 4.003 4.003 0 0 0 1.172 2.842 4.082 4.082 0 0 0 5.656 0 4.032 4.032 0 0 0 0-5.684A3.963 3.963 0 0 0 17 2z"/><path fill="#6563ff" d="M17 11a5 5 0 1 1 3.535-1.465A4.966 4.966 0 0 1 17 11zm0-8a3 3 0 0 0-2.121 5.121 3.072 3.072 0 0 0 4.242 0A3 3 0 0 0 17 3z"/><path fill="#6563ff" d="M14.172 9.828a1 1 0 0 1-.707-1.707l5.656-5.656a1 1 0 0 1 1.414 1.414L14.88 9.535a.997.997 0 0 1-.707.293z"/></svg>`;
  }

  const checkIfFolderHasItem = (path, name) => {
    const pathParts = path.split('/').filter(Boolean);
    let currentItem = {...files};
    let parentItem = null;

    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i + 1];
      parentItem = currentItem;
      currentItem = parentItem?.items.find(item => item.name === part);
    }
    return parentItem.items.some(item => item.name === name);
  };

  const markUnfocused = (path, updatedFiles) => {
    const newState = updatedFiles || JSON.parse(JSON.stringify(files));
    const pathParts = path.split('/').filter(Boolean);
    let currentItem = newState;
    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      if (currentItem.name === part) {
        if (i < pathParts.length - 1) {
          let index = currentItem.items.findIndex(
            item => item.name === pathParts[i + 1],
          );
          if (index !== -1) {
            currentItem = currentItem.items[index];
          } else {
            throw new Error(`Path does not exist: ${path}`);
          }
        }
      }
    }
    currentItem.inFocus = false;
    setFiles(newState);
    return newState;
  };

  return (
    <CodeRoomContext.Provider
      value={{
        files,
        selectedFile,
        filesData,
        tabs,
        handleOperations,
        handleFileSelect,
        handleRename,
        handleCloseTab,
        markUnfocused,
        checkIfFolderHasItem,
        toggleCollapsed,
        deleteItem,
        addItem,
        getSvgContent,
        ...props,
      }}>
      {children}
    </CodeRoomContext.Provider>
  );
};

const CodeRoom = () => {
  const context = useContext(CodeRoomContext);
  return context;
};

export {CodeRoomContext, CodeRoomProvider, CodeRoom};
