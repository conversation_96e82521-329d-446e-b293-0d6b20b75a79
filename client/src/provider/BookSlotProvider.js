'use client';
import React, {createContext, useContext, useState, useEffect} from 'react';
import {UserAuth} from '@/provider/AuthProvider';
import {
  checkUserAssignmentValid,
  getNumSlotsPerDay,
  checkAlreadyBooked,
  cancelSlot,
  reportError,
} from '@/lib/utilities/api';
import errors from '@/values/errorMessages';
import {trackEvent} from '@/lib/firebase/firebaseWrapper';
import {useToast} from '@/components/ui/use-toast';
import {formatDateInfo} from '@/lib/utilities/dateFormat';

const BookSlotContext = createContext();

const BookSlotProvider = ({children, params}) => {
  const {submissionId, companyId} = params;
  const {user} = UserAuth();
  const {toast} = useToast();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [calendarData, setCalendarData] = useState({});
  const [selectedDate, setSelectedDate] = useState();
  const [slotBooked, setSlotBooked] = useState(false);
  const [slotDetails, setSlotDetails] = useState(null);
  const [reschedulingSlot, setReschedulingSlot] = useState(false);
  const [cancelOpen, setCancelOpen] = useState(false);
  const [interviewTaken, setInterviewTaken] = useState(false);

  const initializeCaldendarData = async () => {
    try {
      setLoading(true);

      const slotsData = await getNumSlotsPerDay(submissionId, companyId);
      setCalendarData(slotsData);

      const slotDetails = slotsData.slotDetails
        ? {
            ...formatDateInfo(
              slotsData.slotDetails.startTime,
              slotsData.slotDetails.endTime,
            ),
            interviewLink: slotsData.slotDetails.interviewLink,
          }
        : null;

      setInterviewTaken(slotsData.interviewTaken);
      if (slotsData.alreadyBooked) {
        setSlotDetails(slotDetails);
        setSlotBooked(true);
      }
    } catch (err) {
      trackEvent('GetNumSlotsPerDay', {
        err: {
          message: err.message || 'No error message available',
          stack: err.stack || 'No stack trace available',
        },
      });
      console.log(err);

      let errorCode = err?.data?.errorCode;

      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = 'internal-server-error';
      }
      const errorDetails = errors[errorCode];

      if (typeof errorDetails.message === 'function') {
        const message = errorDetails.message({
          ...err?.data,
        });
        setError(message);
      } else {
        setError(errorDetails.message);
      }

      reportError('GetNumSlotsPerDay', err, false);
    } finally {
      setLoading(false);
    }
  };

  const updateSlotCountByDate = (date, count) => {
    setCalendarData(prevState => ({
      ...prevState,
      slotsPerDay: {
        ...prevState.slotsPerDay,
        [date]: count,
      },
      disabledDates:
        count === 0
          ? [...prevState.disabledDates, date]
          : prevState.disabledDates,
    }));
    if (count === 0) {
      setSelectedDate(null);
    }
  };

  async function handleCancel() {
    try {
      setLoading(true);
      const cancelationResult = await cancelSlot(submissionId, companyId);
      setSlotBooked(false);
      setReschedulingSlot(false);
      setSlotDetails(null);
      updateSlotCountByDate(
        selectedDate,
        calendarData.slotsPerDay[selectedDate] + 1,
      );
      toast({
        variant: 'success',
        description: 'Slot canceled successfully.',
      });
    } catch (err) {
      let errorCode = err?.data?.errorCode;

      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = 'internal-server-error';
      }
      const errorDetails = errors[errorCode];

      if (typeof errorDetails.message === 'function') {
        const message = errorDetails.message({
          ...err?.data,
        });
        toast({
          variant: 'destructive',
          description: message,
        });
      } else {
        toast({
          variant: 'destructive',
          description: errorDetails.message,
        });
      }

      trackEvent('CancelSlot', {
        err: {
          message: err.message || 'No error message available',
          stack: err.stack || 'No stack trace available',
        },
      });
      reportError('CancelSlot', err, false);
    } finally {
      await initializeCaldendarData();
      setCancelOpen(false);
      setLoading(false);
    }
  }

  function onOpenCancelDialog() {
    if (loading && cancelOpen) {
      return;
    }

    setCancelOpen(cancelOpen => !cancelOpen);
  }

  useEffect(() => {
    if (!user) return;
    initializeCaldendarData();
  }, [user]);

  return (
    <BookSlotContext.Provider
      value={{
        slotBooked,
        setSlotBooked,
        loading,
        error,
        calendarData,
        selectedDate,
        setSelectedDate,
        submissionId,
        companyId,
        updateSlotCountByDate,
        slotDetails,
        setSlotDetails,
        reschedulingSlot,
        setReschedulingSlot,
        cancelOpen,
        setCancelOpen,
        handleCancel,
        onOpenCancelDialog,
        interviewTaken,
        initializeCaldendarData,
      }}>
      {children}
    </BookSlotContext.Provider>
  );
};

const BookSlot = () => {
  const context = useContext(BookSlotContext);
  return context;
};

export {BookSlotContext, BookSlotProvider, BookSlot};
