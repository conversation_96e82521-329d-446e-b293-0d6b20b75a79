'use client';

import React, {createContext, useContext, useEffect, useState} from 'react';
import {Interview} from './InterviewProvider';
import interviewStates from '@/values/interviewStates';
import { tutorialSteps } from '@/values/tutorialSteps';

const TutorialContext = createContext();

const TutorialProvider = ({children}) => {
  const { interviewState, blinkOnPublish } = Interview();

  const [tutorialStep, setTutorialStep] = useState(1);
  const [infoBoxStyle, setInfoBoxStyle] = useState({display: 'none'});
  const [overlayStyle, setOverlayStyle] = useState({display: 'none'});
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    if (interviewState === interviewStates.CODING_TUTORIAL) {
      showInfoBox(tutorialStep);
    } else {
      if (blinkOnPublish) {
        setTutorialStep(tutorialSteps.PUBLISH);
        showInfoBox(tutorialSteps.PUBLISH);
      } else {
        setInfoBoxStyle({ display: 'none' });
        setOverlayStyle({ display: 'none' });
      }
    }
  }, [tutorialStep, interviewState, mounted, blinkOnPublish]);

  const showInfoBox = step => {
    if (typeof window !== 'undefined') {
      const infoBox = document.getElementById('info-box');
      const allOverlays = document.querySelectorAll('[data-overlay]');
      allOverlays.forEach(elem => {
        elem.style.zIndex = 'initial';
      });

      if (infoBox) {
        findStepElementWithRetry(step, 0);
      }
    }
  };

  const findStepElementWithRetry = (step, retryCount, maxRetries = 10) => {
    const stepElement = document.querySelector(`[data-step="${step}"]`);
    const overlayElement = document.querySelector(`[data-overlay="${step}"]`);

    if (stepElement) {
      const stepRect = stepElement.getBoundingClientRect();
      const scrollTop = window.scrollY || document.documentElement.scrollTop;
      const scrollLeft = window.scrollX || document.documentElement.scrollLeft;

      setInfoBoxStyle({
        display: 'flex',
        width: `${stepRect.width}px`,
        height: `${stepRect.height}px`,
        left: `${stepRect.left + scrollLeft}px`,
        top: `${stepRect.top + scrollTop}px`,
        transition: 'left 0.5s ease, top 0.5s ease',
      });

      if (overlayElement) {
        overlayElement.style.zIndex = 50;
        setOverlayStyle({
          display: 'block',
        });
      } else {
        setOverlayStyle({ display: 'none' });
      }

      const infoBox = document.getElementById('info-box');
      if (infoBox) {
        infoBox.onclick = (event) => {
          if (step === tutorialStep) {
            document.dispatchEvent(new CustomEvent('tutorial-click', {
              detail: { event, element: stepElement }
            }));
          }
          stepElement.click();
        };
      }
    } else if (retryCount < maxRetries) {
      setTimeout(() => {
        findStepElementWithRetry(step, retryCount + 1, maxRetries);
      }, 100);
    } else {
      setInfoBoxStyle({ display: 'none' });
      setOverlayStyle({ display: 'none' });
      console.log(`Failed to find element with data-step="${step}" after ${maxRetries} retries`);
    }
  };

  return (
    <TutorialContext.Provider
      value={{
        tutorialStep,
        setTutorialStep,
        showInfoBox,
        infoBoxStyle,
        overlayStyle,
        setMounted,
      }}>
      {children}
    </TutorialContext.Provider>
  );
};

const Tutorial = () => {
  const context = useContext(TutorialContext);
  return context;
};

export {TutorialContext, TutorialProvider, Tutorial};
