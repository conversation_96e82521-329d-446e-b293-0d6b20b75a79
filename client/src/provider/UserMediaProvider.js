'use client';
import { trackEvent } from '@/lib/firebase/firebaseWrapper';
import { reportError } from '@/lib/utilities/api';
import React, { createContext, useContext, useState, useRef } from 'react';
const UserMediaContext = createContext();
const DEFAULT_CHUNK_SIZE = 2048;

const UserMediaProvider = ({ children }) => {
  const mediaStream = useRef(null);
  const screenStream = useRef(null);
  const [audioTrack, setAudioTrack] = useState(null);
  const [videoTrack, setVideoTrack] = useState(null);
  const [screenTrack, setScreenTrack] = useState(null);
  const [displayDetails, setDisplayDetails] = useState(null);
  const [timeSlice, setTimeSlice] = useState(0);
  const [micSettings, setMicSettings] = useState({});
  const [devices, setDevices] = useState({
    audioinput: [],
    videoinput: [],
  });
  const [triggerDevice, setDevicePermissionRevoked] = useState(null);

  const userMediaEventListenerAbortController = useRef(new AbortController());
  const userMediaEventListenerAbortSignal =
    userMediaEventListenerAbortController.current.signal;

  const resetRevokedDevice = device => {
    setDevicePermissionRevoked(prev => {
      if (prev === device) {
        return null;
      }
      return prev;
    });
  };

  const onPermissionRevoked = device => {
    setDevicePermissionRevoked(device);
  };

  const getMedia = async (kind, deviceId) => {
    try {
      let stream;

      switch (kind) {
        case 'screen': {
          stream = await navigator.mediaDevices.getDisplayMedia({
            video: true,
          });
          screenStream.current = stream;

          const track = stream.getVideoTracks()[0];
          const settings = track.getSettings();

          if (settings.displaySurface === 'browser') {
            track.stop();
            screenStream.current = null;
            throw new Error('Sharing of individual browser tabs is not allowed. Please select "Entire Screen".');
          }

          if (settings.displaySurface !== 'monitor') {
            track.stop();
            screenStream.current = null;
            throw new Error('Only entire screen sharing is allowed. Please select "Entire Screen".');
          }

          setScreenTrack(track);
          resetRevokedDevice('Screen');

          track.addEventListener(
            'ended',
            () => {
              console.log('Screen sharing stopped');
              setScreenTrack(null);
              screenStream.current = null;
              onPermissionRevoked('Screen');
            },
            { signal: userMediaEventListenerAbortSignal },
          );
          return stream;
        }
        case 'audioinput':
        case 'videoinput': {
          const constraints = {
            audio:
              kind === 'audioinput'
                ? {
                  deviceId: deviceId ? { exact: deviceId } : undefined,
                  echoCancellation: true,
                }
                : false,
            video:
              kind === 'videoinput'
                ? {
                  deviceId: deviceId ? { exact: deviceId } : undefined,
                }
                : false,
          };

          stream = await navigator.mediaDevices.getUserMedia(constraints);

          if (!mediaStream.current) {
            mediaStream.current = stream;
          } else {
            const tracks = mediaStream.current.getTracks();
            tracks.forEach(track => {
              if (kind === 'audioinput' && track.kind === 'audio') {
                mediaStream.current.removeTrack(track);
                track.stop();
              } else if (kind === 'videoinput' && track.kind === 'video') {
                mediaStream.current.removeTrack(track);
                track.stop();
              }
            });

            stream.getTracks().forEach(track => {
              mediaStream.current.addTrack(track);
            });
          }

          stream.getTracks().forEach(track => {
            if (track.kind === 'audio' && kind === 'audioinput') {
              const micSettings = track.getSettings();
              setAudioTrack(track);
              setMicSettings(micSettings);
              setTimeSlice(
                Math.round(
                  (1000 * DEFAULT_CHUNK_SIZE) / micSettings.sampleRate,
                ),
              );
              resetRevokedDevice('Microphone');

              track.addEventListener(
                'ended',
                () => {
                  console.log('Audio track ended');
                  setAudioTrack(null);
                  onPermissionRevoked('Microphone');
                },
                { signal: userMediaEventListenerAbortSignal },
              );
            } else if (track.kind === 'video' && kind === 'videoinput') {
              setVideoTrack(track);
              resetRevokedDevice('Camera');
              track.addEventListener(
                'ended',
                () => {
                  console.log('Video track ended');
                  setVideoTrack(null);
                  onPermissionRevoked('Camera');
                },
                { signal: userMediaEventListenerAbortSignal },
              );
            }
          });
          return stream;
        }
        case 'display': {
          stream = await window.getScreenDetails();
          console.log('window access', stream);
          setDisplayDetails(stream);
          return stream;
        }
      }
    } catch (err) {
      let errorDescription;
      trackEvent(`getMediaStream(${{ kind, deviceId }})`, {
        err: {
          message: err.message || 'No error message available',
          stack: err.stack || 'No stack trace available',
        },
      });

      if (err instanceof DOMException) {
        errorDescription = getErrorDescription(err.name, kind, err);
      } else if (err.message) {
        errorDescription = err.message; 
      } else {
        errorDescription = 'An unexpected error occurred';
      }

      throw new Error(errorDescription);
    }
  };

  const getErrorDescription = (errorName, deviceType, err) => {
    switch (errorName) {
      case 'NotAllowedError':
        return `${deviceType} access denied`;
      case 'NotFoundError':
        return 'No input devices found';
      case 'NotReadableError':
        return `${deviceType} is already in use`;
      case 'OverconstrainedError':
        return 'Constraints cannot be satisfied by available hardware';
      default:
        reportError(`UserMediaProvider_${deviceType}`, err, false);
        return 'An unknown error occurred';
    }
  };

  return (
    <UserMediaContext.Provider
      value={{
        triggerDevice,
        timeSlice,
        mediaStream,
        getMedia,
        micSettings,
        audioTrack,
        videoTrack,
        screenStream,
        screenTrack,
        displayDetails,
        setAudioTrack,
        setScreenTrack,
        setVideoTrack,
        setDisplayDetails,
        devices,
        setDevices,
        userMediaEventListenerAbortController,
      }}>
      {children}
    </UserMediaContext.Provider>
  );
};

const UserMedia = () => {
  const context = useContext(UserMediaContext);
  return context;
};

export { UserMediaContext, UserMediaProvider, UserMedia };
