'use client';

import React, { createContext, useEffect, useState, useContext } from 'react';
import {
  signInWithEmailAndPassword,
  signOut,
  deleteUser,
  reauthenticateWithCredential,
  EmailAuthProvider,
  onAuthStateChanged,
  signInWithCustomToken,
} from 'firebase/auth';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { trackEvent } from '@/lib/firebase/firebaseWrapper';
import { constants } from '@/lib/utilities/Auth';
import mixpanel from 'mixpanel-browser';
import { auth } from '@/lib/firebase/firebase';
import { usePathname, useRouter, useParams } from 'next/navigation';
import Loader from '@/components/Loader';
import { validateDomain } from '@/lib/utilities/urlUtilities';
import { reportError } from '@/lib/utilities/api';

const { errorCodes } = constants;

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [initializing, setInitializing] = useState(true);
  const [authChecked, setAuthChecked] = useState(true);
  const [signingOut, setSigningOut] = useState(false);

  const router = useRouter();
  const pathname = usePathname();

  const signout = () => {
    return new Promise(async (resolve, reject) => {
      try {
        setSigningOut(true);
        await signOut(auth);
        resolve();
      } catch (error) {
        trackEvent('signout', {
          err: {
            message: error.message || 'No error message available',
            stack: error.stack || 'No stack trace available',
          },
        });
        reportError("signout", error, false);
        reject(error);
      }
    });
  }

  const handleAuthStateChanged = async user => {
    if (user) {
      const idTokenResult = await user.getIdTokenResult();
      const { dob, phone, gender } = idTokenResult.claims;
      user.claims = {
        dob,
        phone,
        gender,
      };
      mixpanel.identify();
      mixpanel.people.set({
        email: user.email,
      });
    }
    setUser(user);
    setInitializing(false);

    const redirectPath = localStorage.getItem('redirectPath');
    const redirect = localStorage.getItem('redirect');
    if (user && redirectPath) {
      if (redirect) {
        router.push(redirect === '/auth' ? '/' : redirect);
        localStorage.removeItem('redirect');
      }
      else {
        if (!redirectPath) router.push("/");
        router.push(redirectPath === '/auth' ? '/' : redirectPath);
      }
      localStorage.removeItem('redirectPath');
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, handleAuthStateChanged);

    return () => unsubscribe();
  }, [user]);

  useEffect(() => {
    const handleMessage = async event => {
      const { token } = event.data;
      const url = event.origin;
      if (validateDomain(url) && token) {
        setAuthChecked(false);
        try {
          await signInWithCustomToken(auth, token);
        } catch (error) {
          trackEvent('SignInWithCustomToken', {
            err: {
              message: error.message || 'No error message available',
              stack: error.stack || 'No stack trace available',
            },
          });
          reportError("SignInWithCustomToken", error, false);
          console.error('Error authenticating user:', error);
        } finally {
          setSigningOut(false);
          setAuthChecked(true);
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  useEffect(() => {
    if (user || !authChecked || initializing) return;
    localStorage.setItem('redirectPath', pathname);
    router.push(`${signingOut ? '/auth?signout=true' : '/auth'}`);
  }, [authChecked, initializing, user, signingOut]);

  useEffect(() => {
    if (window.location.pathname === '/signout/user/1') {
      console.log("Logged out");
      signout();
      router.push('/auth?signout=true');
    }
  }, [router, signout]);

  return (
    <AuthContext.Provider
      value={{
        user,
        setUser,
        initializing,
        authChecked,
        signingOut,
        setSigningOut,
        setAuthChecked,
        signout,
        login: (email, password) => {
          return new Promise(async (resolve, reject) => {
            try {
              trackEvent('login', { email });
              const user = await signInWithEmailAndPassword(
                auth,
                email,
                password,
              );
              // const token = await auth.currentUser.getIdToken(); //==XXX remove these XXX==
              // console.log('token:', token);
              resolve(user);
            } catch (error) {
              console.log('Error loging in:', error);
              trackEvent('login', {
                err: {
                  message: error.message || 'No error message available',
                  stack: error.stack || 'No stack trace available',
                },
              });
              reportError("login", error, false);
              reject(error);
            }
          });
        },
        signup: (displayName, gender, phone, dob, email, password) => {
          console.log('inside signup');
          return new Promise(async (resolve, reject) => {
            try {
              trackEvent('signup', { email });
              const functions = getFunctions();
              const cloudSignup = httpsCallable(functions, 'cloudSignup');
              cloudSignup({
                email: email,
                password: password,
                displayName: displayName,
                customClaims: {
                  gender: gender,
                  dob: dob,
                  phone: phone,
                },
              })
                .then(async res => {
                  if (res.data.success) {
                    trackEvent('login', { email });
                    await signInWithEmailAndPassword(auth, email, password);
                    resolve(user);
                  } else {
                    console.log('Error signing up: ', res.data.error);
                    reject(res.data.error);
                  }
                })
                .catch(error => {
                  trackEvent('cloudSignup', {
                    err: {
                      message: error.message || 'No error message available',
                      stack: error.stack || 'No stack trace available',
                    },
                  });
                  reportError("cloudSignup", error, false);
                  reject(error);
                });
            } catch (error) {
              trackEvent('signup', {
                err: {
                  message: error.message || 'No error message available',
                  stack: error.stack || 'No stack trace available',
                },
              });
              reportError("signup", error, false);
              reject(error);
            }
          });
        },
        delete_account: () => {
          return new Promise(async (resolve, reject) => {
            try {
              const res = deleteUser(user);
              resolve(res);
            } catch (error) {
              console.log('Error deleting user: ', error);
              trackEvent('delete_account', {
                err: {
                  message: error.message || 'No error message available',
                  stack: error.stack || 'No stack trace available',
                },
              });
              reportError("delete_account", error, false);
              reject(error);
            }
          });
        },
        reauthenticate: (email, password) => {
          return new Promise(async (resolve, reject) => {
            try {
              const credential = EmailAuthProvider.credential(email, password);
              reauthenticateWithCredential(user, credential)
                .then(res => resolve(res))
                .catch(error => {
                  console.log('Error reauthenticating user: ', error);
                  trackEvent('reauthenticateWithCredential', {
                    err: {
                      message: error.message || 'No error message available',
                      stack: error.stack || 'No stack trace available',
                    },
                  });
                  reject(error);
                });
            } catch (error) {
              console.log('Error reauthenticating user: ', error);
              trackEvent('reauthenticate', {
                err: {
                  message: error.message || 'No error message available',
                  stack: error.stack || 'No stack trace available',
                },
              });
              reportError("reauthenticate", error, false);
              reject(error);
            }
          });
        },
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export const UserAuth = () => {
  return useContext(AuthContext);
};
