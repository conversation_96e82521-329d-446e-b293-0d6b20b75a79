'use client';
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import errors from '@/values/errorMessages';

import interviewStates from '@/values/interviewStates';
import {checkRoom, reportError} from '@/lib/utilities/api';
import {trackEvent} from '@/lib/firebase/firebaseWrapper';
import {UserMedia} from './UserMediaProvider';
import {hasCookie} from 'cookies-next';
import conversationModes from '@/values/conversationModes';
import {BASE_URL} from '../lib/utilities/globalConstants';
import {UserAuth} from '@/provider/AuthProvider';
import {useParams} from 'next/navigation';
import {fetchMetaInfo} from '@/lib/utilities/metaInfoUtils';

const InterviewContext = createContext();

const InterviewProvider = ({children}) => {
  const params = useParams();
  const {user} = UserAuth();
  const {audioTrack, screenTrack, videoTrack, displayDetails} = UserMedia();
  const [interviewState, setInterviewState] = useState(1);
  const [interviewDetails, setInterviewDetails] = useState();
  const [role, setRole] = useState();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [roomId, setRoomId] = useState(null);
  const [tutorialFinished, setTutorialFinished] = useState(false);
  const [conversationMode, setConversationMode] = useState(
    conversationModes.voice,
  );
  const [blinkOnPublish, setBlinkOnPublish] = useState(false);
  const [recognizedText, setRecognizedText] = useState('');

  const metaInfo = useRef(null);
  const intervieweeSpeaking = useRef(false);
  const interviewEnded = useRef(false);
  const networkStatusCallbackRef = useRef(null);
  const intervalCamTestCallbackRef = useRef(null);

  const sendMetaInfoToBackend = async data => {
    try {
      const response = await fetch(`${BASE_URL}metaInfo`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify({
          companyId: params.roomParams[0],
          roomId: params.roomParams[1],
          ...data,
        }),
      });
    } catch (error) {
      console.error('Error sending meta info to backend:', error);
    }
  };

  useEffect(() => {
    const shouldSendMetaInfo =
      role === 'interviewee' &&
      audioTrack &&
      screenTrack &&
      videoTrack &&
      displayDetails;

    if (user?.accessToken && shouldSendMetaInfo) {
      fetchMetaInfo(sendMetaInfoToBackend, metaInfo);
    }

    const handleOnlineStatusChange = () => {
      if (user?.accessToken && shouldSendMetaInfo) {
        fetchMetaInfo(sendMetaInfoToBackend, metaInfo);
      }
    };

    window.addEventListener('online', handleOnlineStatusChange);

    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
    };
  }, [role, user, audioTrack, screenTrack, videoTrack, displayDetails]);

  const companyIdRef = useRef(null);

  const checkIfBrowserSupported = () => {
    return navigator.userAgentData?.brands?.some(
      b => b.brand === 'Google Chrome',
    );
  };

  const checkIfTutorialFinished = () => {
    hasCookie('tutorial-finished')
      ? setTutorialFinished(true)
      : setTutorialFinished(false);
  };

  const getInterviewDetails = async (roomId, companyId) => {
    setLoading(true);

    if (roomId === undefined || roomId === null || roomId.length === 0) {
      setError(errors['invalid-room-id']);
      setLoading(false);
      return;
    }

    try {
      const response = await checkRoom(roomId, companyId);
      setInterviewDetails(response.data);
      setRoomId(roomId);
      setRole(response.role);
    } catch (error) {
      trackEvent('getInterviewDetails', {
        err: {
          message: error.message || 'No error message available',
          stack: error.stack || 'No stack trace available',
        },
      });
      let errorCode = error?.data?.errorCode;

      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = 'internal-server-error';
      }
      const errorDetails = errors[errorCode];

      if (typeof errorDetails.message === 'function') {
        const message = errorDetails.message({
          ...error?.data,
        });
        setError({
          ...errorDetails,
          message,
        });
      } else {
        setError(errorDetails);
      }
      reportError('getInterviewDetails', error, false);
    }
  };

  const routeToFeedback = () => {
    setInterviewState(interviewStates.FEEDBACK);
    setInterviewDetails(null);
  };

  const routeToInstruction = () => {
    setInterviewState(interviewStates.INSTRUCTION_PAGE);
  };

  useEffect(() => {
    const isBrowserSupported = checkIfBrowserSupported();
    if (!isBrowserSupported) {
      setError(errors['browser-not-supported']);
      setLoading(false);
      return;
    }
    checkIfTutorialFinished();
    if (interviewDetails && role && !error) setLoading(false);
    else setLoading(true);
  }, [interviewDetails, role, error, interviewState]);

  return (
    <InterviewContext.Provider
      value={{
        roomId,
        interviewState,
        setInterviewState,
        interviewDetails,
        setInterviewDetails,
        getInterviewDetails,
        loading,
        routeToFeedback,
        routeToInstruction,
        error,
        setError,
        role,
        tutorialFinished,
        conversationMode,
        setConversationMode,
        blinkOnPublish,
        setBlinkOnPublish,
        companyIdRef,
        setRecognizedText,
        recognizedText,
        intervieweeSpeaking,
        interviewEnded,
        networkStatusCallbackRef,
        intervalCamTestCallbackRef,
      }}>
      {children}
    </InterviewContext.Provider>
  );
};

const Interview = () => {
  const context = useContext(InterviewContext);
  return context;
};

export {InterviewContext, InterviewProvider, Interview};
