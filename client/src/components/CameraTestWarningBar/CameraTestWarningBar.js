'use client';
import { Interview } from '@/provider/InterviewProvider';
import { useEffect, useState } from 'react';
import WarningBar from '../ui/WarningBar';

const CameraTestWarningBar = () => {
  const [warning, setWarning] = useState(null);
  const { intervalCamTestCallbackRef } = Interview();

  useEffect(() => {
    const handleCamTestResult = data => {
      console.log("Camera test data:", data);
      if (!data) {
        setWarning(null);
        return;
      }
      const { metrics, thresholds, status } = data;
      if (status === true) {
        setWarning(null);
        return;
      }
      const faceDetected = metrics.face_detection_rate > thresholds.avg_face_detection_rate;
      const faceBrightEnough = metrics.face_brightness > thresholds.brightness_low;
      const faceNotTooBright = metrics.face_brightness < thresholds.brightness_high;
      const leftEyeVisible = metrics.left_eye_brightness > thresholds.eye_brightness_low;
      const rightEyeVisible = metrics.right_eye_brightness > thresholds.eye_brightness_low;

      if (!faceDetected) {
        setWarning("Face not clearly detected. Please center your face in the frame.");
      } else if (!faceBrightEnough) {
        setWarning("Face appears too dark. Please adjust your lighting for better visibility.");
      } else if (!leftEyeVisible && !rightEyeVisible) {
        setWarning("Eyes not visible. Please ensure proper lighting on your face.");
      } else if (!leftEyeVisible) {
        setWarning("Left eye not clearly visible. Please adjust your position or lighting.");
      } else if (!rightEyeVisible) {
        setWarning("Right eye not clearly visible. Please adjust your position or lighting.");
      } else if (!faceNotTooBright) {
        setWarning("Face appears too bright. Please reduce direct light on your face.");
      } else {
        setWarning(null);
      }
    }

    intervalCamTestCallbackRef.current = handleCamTestResult;

    return () => {
      intervalCamTestCallbackRef.current = null;
    };
  }, []);

  if (!warning) return null;

  return (
    <WarningBar warningType={"face"} warning={warning} />
  );
};

export default CameraTestWarningBar;
