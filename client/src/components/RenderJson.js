import React from 'react';

const RenderJson = ({ json, defaultValue }) => {
  return <pre>{JSON.stringify(json, null, 4)}</pre>;
  if (typeof json === 'array') return (
    <summary>
      {json.map((item, index) => (
        <p key={index}>{item}</p>
      ))}
      <details>
        {json.map((item, index) => (
          <div key={index}>
            {typeof item === 'object' && !Array.isArray(item) ? (
              <RenderJson json={item} />
            ) : Array.isArray(item) ? (
              item.map((i, j) => (
                <p key={j}>{i}</p>
              ))
            ) : (
              <p>{item}</p>
            )}
          </div>
        ))}
      </details>
    </summary>
  );
  if (typeof json === 'string') return <p>{json}</p>;
  if (typeof json === 'number') return <p>{json}</p>;
  return (
    <summary>
      {defaultValue ? (
        <p>
          {defaultValue}: {json[defaultValue]}
        </p>
      ) : (
        <p>{json.stringify()}</p>
      )}
      <details>
        {Object.keys(json).map((key, index) => (
          <div key={index}>
            {typeof json[key] === 'object' && !Array.isArray(json[key]) ? (
              <RenderJson json={json[key]} defaultValue={key} />
            ) : Array.isArray(json[key]) ? (
              json[key].map((item, i) => (
                <p key={i}>
                  {key}: {item}
                </p>
              ))
            ) : (
              <p>
                {key}: {json[key]}
              </p>
            )}
          </div>
        ))}
      </details>
    </summary>
  );
};

export default RenderJson;
