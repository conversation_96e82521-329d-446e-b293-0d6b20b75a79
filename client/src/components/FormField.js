import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import {Label} from '@/components/ui/label';
import {RadioGroup, RadioGroupItem} from '@/components/ui/radio-group';
import {Input} from '@/components/ui/input';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover';
import {Button} from '@/components/ui/button';
import {format} from 'date-fns';
import {cn} from '@/lib/utils';
import {Calendar} from '@/components/ui/calendar';
import {Checkbox} from '@/components/ui/checkbox';
import {Calendar as CalendarIcon} from 'lucide-react';

export const TextField = ({
  control,
  name,
  label,
  placeholder,
  type = 'text',
  onValueChange,
  ...otherProps
}) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({field}) => (
        <FormItem>
          <FormLabel className="text-md">{label}</FormLabel>
          <FormControl>
            <Input
              type={type}
              placeholder={placeholder}
              onChange={(value) => {
                field.onChange(value);
                onValueChange && onValueChange(value);
              }}
              {...field}
              {...otherProps}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export const CheckboxField = ({control, name, label, onCheckedChange}) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({field}) => (
        <FormItem className="flex gap-2">
          <FormControl>
            <Checkbox
              className="mt-3"
              onCheckedChange={checked => {
                // field.onChange(checked);
                onCheckedChange ? onCheckedChange(checked) : field.onChange(checked);
              }}
              checked={field.value}
            />
          </FormControl>
          <FormLabel className="text-md">{label}</FormLabel>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export const RadioFormField = ({control, name, label, options}) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({field}) => (
        <FormItem>
          <FormLabel className="text-md">{label}</FormLabel>
          <RadioGroup
            onValueChange={field.onChange}
            className="flex justify-between">
            {options.map((option, index) => (
              <FormItem
                className="flex items-center space-x-3 space-y-0"
                key={index}>
                <FormControl>
                  <RadioGroupItem value={option} />
                </FormControl>
                <FormLabel className="font-normal">{option}</FormLabel>
              </FormItem>
            ))}
          </RadioGroup>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export const DatePicker = ({control, name, label, from, to}) => {
  return (
    <>
      <FormField
        control={control}
        name={name}
        render={({field}) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !field.value && 'text-muted-foreground',
                  )}>
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {field.value ? (
                    format(field.value, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={field.onChange}
                  fromDate={from}
                  toDate={to}
                  showOutsideDays={true}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};
