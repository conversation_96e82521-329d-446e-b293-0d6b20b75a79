'use client';

import {useEffect, useRef} from 'react';
import VideoPlayer from '../VideoPlayer';

const SharedScreens = ({consumers}) => {
  return (
    <div className='w-full h-full flex items-center justify-between'>
      {Object.keys(consumers).length ? (
        Object.keys(consumers)
          .map(peerId => {
            if (!consumers[peerId].screen) return null;
            return (
              <div key={consumers[peerId].screen.id}>
                <VideoPlayer
                  playsInline
                  autoPlay
                  videoTrack={consumers[peerId].screen.track}
                />
              </div>
            );
          })
          .filter(consumer => consumer !== null)
      ) : (
        <p>No one is sharing their screen currently.</p>
      )}
    </div>
  );
};

export default SharedScreens;
