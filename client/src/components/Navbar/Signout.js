'use client';

import Chip from '../Chip';
import { usePathname, useRouter } from 'next/navigation';

const Signout = () => {
  const pathname = usePathname();

  const handleLogout = () => {
    localStorage.setItem("redirect", pathname);
    window.location.href = '/signout/user/1';
  };

  return (
    <Chip
      className="bg-destructive text-white"
      title="Logout"
      src="/logout.png"
      alt={'Logout'}
      onClick={handleLogout}
    >
      Logout
    </Chip>
  );
};

export default Signout;
