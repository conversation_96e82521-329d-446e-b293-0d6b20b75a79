'use client';
import {Interview} from '@/provider/InterviewProvider';
import {useEffect, useState} from 'react';
import WarningBar from '../ui/WarningBar';

const NetworkStatus = () => {
  const [status, setStatus] = useState(null);
  const {networkStatusCallbackRef} = Interview();

  useEffect(() => {
    const handleNetworkStatus = data => {
      if (data?.status === 'Bad') {
        setStatus('Bad');
      } else {
        setStatus(null);
      }
    };

    networkStatusCallbackRef.current = handleNetworkStatus;

    return () => {
      networkStatusCallbackRef.current = null;
    };
  }, []);

  if (status !== 'Bad') return null;

  return (
    <WarningBar warningType={"network"} />
  );
};

export default NetworkStatus;
