import {useEffect, useState} from 'react';
import {parse, getHours, format} from 'date-fns';
import {useToast} from '@/components/ui/use-toast';
import {BookSlot} from '@/provider/BookSlotProvider';
import {getAvailableSlots, reportError} from '@/lib/utilities/api';
import Slot from './Slot';
import {trackEvent} from '@/lib/firebase/firebaseWrapper';

const SlotTimings = () => {
  const {toast} = useToast();
  const {selectedDate, updateSlotCountByDate, submissionId, companyId} =
    BookSlot();
  const [slots, setSlots] = useState({});
  const [loading, setLoading] = useState(true);

  function pushSlots(slots) {
    const morning = [];
    const afternoon = [];
    const evening = [];
    slots.forEach(slot => {
      const fromTime = parse(slot.from, 'HH:mm:ss', new Date());
      const hour = getHours(fromTime);

      if (hour >= 0 && hour < 12) {
        morning.push(slot);
      } else if (hour >= 12 && hour < 15) {
        afternoon.push(slot);
      } else if (hour >= 15 && hour < 24) {
        evening.push(slot);
      }
    });

    setSlots({timings: {morning, afternoon, evening}});
  }

  async function getSlots() {
    try {
      const date = format(selectedDate, 'yyyy-MM-dd');

      const {slots} = await getAvailableSlots(date, submissionId, companyId);
      updateSlotCountByDate(selectedDate, slots.length);
      pushSlots(slots);
    } catch (err) {
      console.log(err);
      toast({
        variant: 'destructive',
        description: 'Failed to get available slots. Please try again.',
      });
      trackEvent('GetSlots', {
        err: {
          message: err.message || 'No error message available',
          stack: err.stack || 'No stack trace available',
        },
      });
      reportError('GetSlots', err, false);
    }
  }

  useEffect(() => {
    setLoading(true);
    getSlots().then(() => setLoading(false));
  }, [selectedDate]);

  const TimeSlotGroup = ({title, slots}) => {
    return (
      <div className="flex flex-col gap-4">
        <h3>{title}</h3>
        {slots.length
          ? slots.map(slot => (
              <Slot
                key={slot.from}
                slotData={slot}
                refreshSlotData={getSlots}
              />
            ))
          : 'No slots available'}
      </div>
    );
  };

  return (
    <div className="flex w-full flex-row gap-4">
      {loading ? (
        'Loading...'
      ) : (
        <>
          <TimeSlotGroup title="Morning" slots={slots.timings.morning} />
          <TimeSlotGroup title="Afternoon" slots={slots.timings.afternoon} />
          <TimeSlotGroup title="Evening" slots={slots.timings.evening} />
        </>
      )}
    </div>
  );
};

export default SlotTimings;
