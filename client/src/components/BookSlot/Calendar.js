import {BookSlot} from '@/provider/BookSlotProvider';
import {Calendar} from '@/components/ui/calendar';

function getBackgroundColorForDay(numOfSlotsAvailable, maxSlots) {
  const percentage = numOfSlotsAvailable / maxSlots;

  if (percentage === 0) return 'bg-red-600';
  if (percentage <= 0.25) return 'bg-red-400';
  if (percentage <= 0.5) return 'bg-yellow-500';
  if (percentage <= 0.75) return 'bg-green-500';

  return 'bg-green-500';
}

const CustomDayContent = props => {
  const {date, activeModifiers, calendarData} = props;
  const numSlots = calendarData.slotsPerDay.hasOwnProperty(date)
    ? calendarData.slotsPerDay[date]
    : 0;
  const maxSlots = calendarData.maxSlotsPerDay;

  if (!numSlots)
    return <div style={{fontSize: '1.2rem'}}>{date.getDate()}</div>;

  const backgroundColor = getBackgroundColorForDay(numSlots, maxSlots);

  return (
    <div
      style={{position: 'relative', overflow: 'visible', fontSize: '1.2rem'}}>
      {date.getDate()}
      {numSlots > 0 && !activeModifiers?.disabled && (
        <div
          className={
            'absolute right-[-12px] top-[-12px] flex h-4 w-4 items-center justify-center rounded-full text-center text-sm text-white ' +
            backgroundColor
          }>
          {numSlots}
        </div>
      )}
    </div>
  );
};

export default function ({className}) {
  const {selectedDate, setSelectedDate, calendarData} = BookSlot();
  return (
    <Calendar
      className={className}
      styles={{
        head_cell: {
          fontSize: '1rem',
          margin: '0.2rem',
        },
        table: {},
        day: {
          margin: '0.2rem',
        },
      }}
      components={{
        DayContent: props => CustomDayContent({...props, calendarData}),
      }}
      mode="single"
      selected={selectedDate}
      onSelect={setSelectedDate}
      fromDate={calendarData.from}
      toDate={calendarData.to}
      showOutsideDays={true}
      ISOWeek={true}
      footer={!selectedDate ? 'Select a date to view available slots' : null}
      disabled={calendarData.disabledDates.map(date => new Date(date))}
    />
  );
}
