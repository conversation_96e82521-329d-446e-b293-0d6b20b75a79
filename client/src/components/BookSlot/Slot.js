import {useState} from 'react';
import {format} from 'date-fns';

import Chip from '@/components/Chip';
import LoadingSpinner from '@/components/LoadingSpinner';
import {Button} from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {useToast} from '@/components/ui/use-toast';

import {BookSlot} from '@/provider/BookSlotProvider';
import {bookSlot, reportError, rescheduleSlot} from '@/lib/utilities/api';
import {trackEvent} from '@/lib/firebase/firebaseWrapper';
import {formatDateInfo} from '@/lib/utilities/dateFormat';
import errors from '@/values/errorMessages';

export default function Slot({slotData, refreshSlotData}) {
  const {toast} = useToast();
  const [open, setOpen] = useState(false);
  const [rescheduleOpen, setRescheduleOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const {
    selectedDate,
    submissionId,
    slotBooked,
    setSlotBooked,
    companyId,
    setSlotDetails,
    reschedulingSlot,
    setReschedulingSlot,
    initializeCaldendarData,
  } = BookSlot();

  const {from: startTime, to: endTime} = slotData;

  function onOpenDialog() {
    if (loading && open) {
      return;
    }

    setOpen(open => !open);
  }

  function onOpenRescheduleDialog() {
    if (loading && rescheduleOpen) {
      return;
    }

    setRescheduleOpen(rescheduleOpen => !rescheduleOpen);
  }

  async function handleBooking() {
    try {
      setLoading(true);

      const data = {
        date: format(selectedDate, 'yyyy-MM-dd'),
        roomStartTime: startTime,
        roomEndTime: endTime,
        submissionId: submissionId,
      };

      const result = await bookSlot(data, companyId);
      trackEvent('BookSlotData', data);

      const slotDetails = result.slotDetails
        ? {
            ...formatDateInfo(
              result.slotDetails.startTime,
              result.slotDetails.endTime,
            ),
            interviewLink: result.slotDetails.interviewLink,
          }
        : null;

      setSlotDetails(slotDetails);
      setSlotBooked(true);
    } catch (err) {
      let errorCode = err?.data?.errorCode;

      await refreshSlotData();

      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = 'internal-server-error';
      }
      const errorDetails = errors[errorCode];

      if (typeof errorDetails.message === 'function') {
        const message = errorDetails.message({
          ...err?.data,
        });
        toast({
          variant: 'destructive',
          description: message,
        });
      } else {
        toast({
          variant: 'destructive',
          description: errorDetails.message,
        });
      }

      trackEvent('BookSlot', {
        err: {
          message: err.message || 'No error message available',
          stack: err.stack || 'No stack trace available',
        },
      });
      trackEvent('BookSlotDataOnError', err.data);
      reportError('BookSlot', err, false);
    } finally {
      await initializeCaldendarData();
      setOpen(false);
      setLoading(false);
    }
  }

  async function handleReschedule() {
    try {
      setLoading(true);

      const data = {
        date: format(selectedDate, 'yyyy-MM-dd'),
        roomStartTime: startTime,
        roomEndTime: endTime,
        submissionId: submissionId,
      };

      const result = await rescheduleSlot(data, companyId);
      trackEvent('RescheduleSlotData', data);

      const slotDetails = result.slotDetails
        ? {
            ...formatDateInfo(
              result.slotDetails.startTime,
              result.slotDetails.endTime,
            ),
            interviewLink: result.slotDetails.interviewLink,
          }
        : null;

      setSlotDetails(slotDetails);
      setReschedulingSlot(false);
      setSlotBooked(true);
    } catch (err) {
      let errorCode = err?.data?.errorCode;

      await refreshSlotData();

      if (!errors.hasOwnProperty(errorCode)) {
        errorCode = 'internal-server-error';
      }
      const errorDetails = errors[errorCode];

      if (typeof errorDetails.message === 'function') {
        const message = errorDetails.message({
          ...err?.data,
        });
        toast({
          variant: 'destructive',
          description: message,
        });
      } else {
        toast({
          variant: 'destructive',
          description: errorDetails.message,
        });
      }
      trackEvent('RescheduleSlot', {
        err: {
          message: err.message || 'No error message available',
          stack: err.stack || 'No stack trace available',
        },
      });
      trackEvent('RescheduleSlotDataOnError', err.data);
      reportError('RescheduleSlot', err, false);
    } finally {
      await initializeCaldendarData();
      setRescheduleOpen(false);
      setLoading(false);
    }
  }

  if (!slotBooked)
    return (
      <Dialog open={open} onOpenChange={onOpenDialog}>
        <DialogTrigger asChild={true}>
          <Chip>{startTime + ' - ' + endTime}</Chip>
        </DialogTrigger>
        <DialogContent
          className="sm:max-w-[425px]"
          onInteractOutside={e => {
            e.preventDefault();
          }}>
          <DialogHeader>
            <DialogTitle>Confirm Slot</DialogTitle>
          </DialogHeader>
          <DialogDescription>
            Are you sure you want to book the slot from {startTime} to {endTime}{' '}
            on {format(selectedDate, 'dd MMM yyyy')}?
          </DialogDescription>

          <DialogFooter>
            <Button type="submit" onClick={handleBooking}>
              {loading ? <LoadingSpinner /> : 'Confirm'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );

  if (reschedulingSlot)
    return (
      <Dialog open={rescheduleOpen} onOpenChange={onOpenRescheduleDialog}>
        <DialogTrigger asChild={true}>
          <Chip>{startTime + ' - ' + endTime}</Chip>
        </DialogTrigger>
        <DialogContent
          className="sm:max-w-[425px]"
          onInteractOutside={e => {
            e.preventDefault();
          }}>
          <DialogHeader>
            <DialogTitle>Reschedule Slot</DialogTitle>
          </DialogHeader>
          <DialogDescription>
            Are you sure you want to reschedule the slot from {startTime} to{' '}
            {endTime} on {format(selectedDate, 'dd MMM yyyy')}?
          </DialogDescription>

          <DialogFooter>
            <Button type="submit" onClick={handleReschedule}>
              {loading ? <LoadingSpinner /> : 'Confirm'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
}
