'use client';
import {useContext} from 'react';
import {useRouter} from 'next/navigation';
import {getCookie} from 'cookies-next';
import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import {z} from 'zod';

import {TextField, RadioFormField, DatePicker} from '@/components/FormField';
import {Button} from '@/components/ui/button';
import {Form} from '@/components/ui/form';
import {useToast} from '@/components/ui/use-toast';

import {AuthContext} from '@/provider/AuthProvider';

import {constants} from '@/lib/utilities/Auth';
const {errorCodes, regex} = constants;

const signupSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be atleast 2 characters')
    .max(50, `Name can't be more than 50 characters`),
  gender: z
    .string()
    .min(2, 'Select a gender')
    .max(50, `Gender can't be more than 50 characters`),
  phoneNumber: z
    .string()
    .min(10, 'Phone number must be atleast 10 characters')
    .max(10, 'Phone number must be atmost 10 characters')
    .transform(value => {
      return value.replace(regex.bannedSymbols.phoneNumber, '');
    })
    .refine(value => value.match(regex.validation.phoneNumber), {
      message: errorCodes.custom.invalidPhoneNumber,
    }),
  dob: z.date({
    required_error: 'A date of birth is required.',
  }),
  email: z.string().email(),
  password: z
    .string()
    .min(6, 'Password must be atleast 6 characters')
    .max(50, `Password can't be more than 50 characters`),
});

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6, 'Invalid Password'),
});

const AuthForm = ({formType}) => {
  const router = useRouter();
  const {toast} = useToast();
  const {signup, login} = useContext(AuthContext);
  const form = useForm({
    resolver: zodResolver(
      formType.type === 'signup' ? signupSchema : loginSchema,
    ),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      phoneNumber: '',
      gender: '',
    },
  });
  const redirectTo = getCookie('redirectTo');

  const onSubmit = formData => {
    if (formType.type === 'signup') {
      signup(
        formData.name,
        formData.gender,
        formData.phoneNumber,
        formData.dob,
        formData.email,
        formData.password,
      )
        .then(res => router.push(redirectTo ? `/${redirectTo}` : '/'))
        .catch(error => {
          console.log(error);
          toast({
            variant: 'destructive',
            description:
              errorCodes.firebase[error.code] || error.errorInfo.message,
          });
        });
    } else if (formType.type === 'login') {
      login(formData.email, formData.password)
        .then(res => router.push(redirectTo ? `/${redirectTo}` : '/'))
        .catch(error =>
          toast({
            variant: 'destructive',
            description:
              errorCodes.firebase[error.code] || error.errorInfo.message,
          }),
        );
    }
  };

  const eighteenYearsAgo = new Date();
  eighteenYearsAgo.setFullYear(eighteenYearsAgo.getFullYear() - 18);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-8">
        {formType.type === 'signup' && (
          <>
            <TextField
              control={form.control}
              name="name"
              label="Full Name"
              placeholder="Enter your Full Name"
            />
            <RadioFormField
              control={form.control}
              name="gender"
              label="Gender"
              options={['Male', 'Female', 'Other']}
            />
            <TextField
              control={form.control}
              type="number"
              name="phoneNumber"
              label="Phone Number"
              placeholder="Enter your Phone Number"
              className="font-semibold"
            />
            <DatePicker
              name="dob"
              control={form.control}
              label="Date of Birth"
              from={new Date(1970, new Date().getMonth())}
              to={eighteenYearsAgo}
            />
          </>
        )}
        <TextField
          control={form.control}
          name="email"
          label="Email"
          placeholder="Enter your Email"
          type="email"
        />
        <TextField
          control={form.control}
          name="password"
          label="Password"
          placeholder="Enter your Password"
          type="password"
        />
        <Button type="submit" className="w-full">
          Submit
        </Button>
      </form>
    </Form>
  );
};

export default AuthForm;
