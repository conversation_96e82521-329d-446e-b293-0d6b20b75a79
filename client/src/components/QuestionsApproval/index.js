'use client';
import {
  getQuestionsApprovalDetails,
  approveQuestions,
} from '@/lib/utilities/api';
import {UserAuth} from '@/provider/AuthProvider';
import {useState, useEffect} from 'react';

import {Button} from '@/components/ui/button';
import {useToast} from '@/components/ui/use-toast';
import Link from 'next/link';

const QuestionsApproval = ({params}) => {
  const {user} = UserAuth();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  const {toast} = useToast();

  const fetchData = async params => {
    try {
      setLoading(true);
      console.log('fetching data', params);

      const data = await getQuestionsApprovalDetails(params);

      setData(data);
    } catch (e) {
      console.error(e);
      setError(`${e.data.message}: ${e.data.error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleOnClick = async ({approve}) => {
    try {
      setSubmitting(true);
      await approveQuestions(approve, params);
      toast({
        variant: 'success',
        title: `Questions ${approve ? 'approved' : 'rejected'} successfully`,
      });
    } catch (e) {
      console.error(e);
      toast({
        variant: 'destructive',
        title: 'Questions Approval Failed',
      });
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    if (!user) return;
    fetchData(params);
  }, [user, params]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  const ListSection = ({title, items}) => (
    <div>
      <h6 className="text-md font-bold text-primary">{title}</h6>
      {items.length > 0 ? (
        <ul className="list-inside list-disc space-y-1">
          {items.map((item, index) => (
            <li key={index} className="text-white">
              {JSON.stringify(item)}
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-red-400">No {title.toLowerCase()}</p>
      )}
    </div>
  );

  return (
    <div className="flex h-full w-full flex-col">
      <h3 className="text-4xl font-bold text-primary">Questions approval</h3>
      <Link href={data.repoLink} className="text-blue-400">
        {' '}
        View Repo
      </Link>
      <div className="my-2 grid grid-cols-1 gap-6 md:grid-cols-3">
        <ListSection title="Languages" items={data.languages} />
        <ListSection title="Skills" items={data.skills} />
        <ListSection title="Custom Skills" items={data.customSkills} />
      </div>

      <ListSection title="Questions" items={data.questions} />

      <div className="mt-2 flex h-full w-full flex-col gap-2 md:flex-row">
        <Button
          disabled={submitting}
          onClick={() => handleOnClick({approve: true})}
          className="rounded bg-green-500 px-4 py-2 font-bold text-white hover:bg-green-700">
          Approve
        </Button>
        <Button
          disabled={submitting}
          onClick={() => handleOnClick({approve: false})}
          className="rounded bg-red-500 px-4 py-2 font-bold text-white hover:bg-red-700">
          Reject
        </Button>
      </div>
    </div>
  );
};

export default QuestionsApproval;
