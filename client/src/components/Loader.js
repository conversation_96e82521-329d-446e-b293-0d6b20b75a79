import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import FaqsComponent from '@/components/interview/FaqsComponent';

export default function Loader({ message }) {
  const [showDialog, setShowDialog] = useState(false);
  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setShowButton(true), 15000); 
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center">
      {!showDialog && (
        <>
          <motion.div
            className="h-20 w-20 rounded-2xl bg-red-500"
            animate={{ rotate: [0, 0, 180, 360, 180, 0] }}
            transition={{ repeat: Infinity, duration: 3 }}
          />
          {message && <p className="mt-4 text-lg text-white">{message}</p>}
        </>
      )}

      {showDialog && <FaqsComponent />}

      {showButton && !showDialog && (
        <div className="absolute bottom-4 flex flex-col items-center">
          <p className="text-white mb-2">Having trouble?</p>
          <Button variant="outline" onClick={() => setShowDialog(!showDialog)}>
            {showDialog ? "Hide Troubleshooting FAQs" : "View Troubleshooting FAQs"}
          </Button>
        </div>
      )}
    </div>
  );
}
