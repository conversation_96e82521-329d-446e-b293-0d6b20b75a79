import { useEffect, useRef } from 'react';
import { Interview } from '@/provider/InterviewProvider';
import interviewStates from '@/values/interviewStates';
import { sendImages } from '@/lib/utilities/api';

const VideoPlayer = ({
  videoTrack,
  className,
}) => {
  const { roomId, interviewState, role, intervalCamTestCallbackRef } = Interview();

  const videoRef = useRef();
  const canvasRef = useRef(null);
  const frameBufferRef = useRef([]);
  const isCapturingRef = useRef(false);
  const pendingClicksRef = useRef([]);
  const intervalRef = useRef(null);

  const framesToSendAtInterval = 2;

  const drawVideoToCanvas = () => {
    if (!videoRef.current || !canvasRef.current) return false;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
    return true;
  };

  const createCanvasBlob = async () => {
    if (!canvasRef.current) return null;
    return new Promise(resolve =>
      canvasRef.current.toBlob(resolve, 'image/jpeg', 0.8)
    );
  };

  const captureCurrentFrame = async () => {
    if (!drawVideoToCanvas()) return null;
    return await createCanvasBlob();
  };

  const captureInterviewFrames = async () => {
    if (!videoRef.current || !canvasRef.current) return null;

    const blobs = [];

    for (let i = 0; i < framesToSendAtInterval; i++) {
      const blob = await captureCurrentFrame();
      if (blob) blobs.push(blob);

      if (i < framesToSendAtInterval - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return blobs;
  };

  const startFrameBuffering = () => {
    isCapturingRef.current = true;
    const captureFrame = async () => {
      if (!isCapturingRef.current || !videoRef.current || !canvasRef.current) return;

      const blob = await captureCurrentFrame();
      if (blob) {
        frameBufferRef.current.push(blob);
        if (frameBufferRef.current.length > 15) {
          frameBufferRef.current.shift();
        }
      }

      processPendingClicks();
      requestAnimationFrame(captureFrame);
    };

    captureFrame();
  };

  const processPendingClicks = async () => {
    if (pendingClicksRef.current.length > 0) {
      const clickData = pendingClicksRef.current[0];

      if (clickData.frames.length < 30) {
        const blob = await captureCurrentFrame();
        if (blob) {
          clickData.frames.push(blob);

          if (clickData.frames.length === 30) {
            try {
              await sendImages(clickData, roomId, 'click');
            } catch (error) {
              console.error('Error processing click data:', error);
            }
            pendingClicksRef.current.shift();
          }
        }
      }
    }
  };

  const captureClickData = (event, stepElement) => {
    if (pendingClicksRef.current.length > 0) return;

    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    const centerX = screenWidth / 2;
    const centerY = screenHeight / 2;
    const relativeX = event.clientX - centerX;
    const relativeY = centerY - event.clientY;

    const clickData = {
      frames: [...frameBufferRef.current],
      clickPosition: {
        x: relativeX,
        y: relativeY
      },
      screenDimensions: {
        width: screenWidth,
        height: screenHeight
      },
      stepId: stepElement.dataset.step,
      timestamp: Date.now()
    };

    pendingClicksRef.current.push(clickData);
  };

  useEffect(() => {
    if (videoTrack && videoRef.current) {
      const stream = new MediaStream();
      stream.addTrack(videoTrack);
      videoRef.current.srcObject = stream;
      videoRef.current?.play().catch(err => console.error('Error playing stream:', err));

      if (role === 'interviewee' && canvasRef.current) {
        const canvas = canvasRef.current;
        canvas.width = videoRef.current.videoWidth || 640;
        canvas.height = videoRef.current.videoHeight || 480;

        startFrameBuffering();
      }
    }

    return () => {
      isCapturingRef.current = false;
    };
  }, [videoTrack]);

  useEffect(() => {
    if (role === 'interviewer' || interviewState !== interviewStates.CODING_INTERVIEW) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    intervalRef.current = setInterval(async () => {
      if (videoRef.current && canvasRef.current) {
        try {
          const frames = await captureInterviewFrames();
          if (frames && frames.length > 0) {
            const result = await sendImages(frames, roomId, 'camera');
            if (intervalCamTestCallbackRef.current) {
              intervalCamTestCallbackRef.current(result);
            }
          }
        } catch (error) {
          console.error('Error sending periodic camera frames:', error);
        }
      }
    }, 10000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [interviewState, videoTrack]);

  useEffect(() => {
    if (role === 'interviewer') return;
    const handleTutorialClick = (e) => {
      if (interviewState === interviewStates.CODING_TUTORIAL) {
        const { event, element } = e.detail;
        captureClickData(event, element);
      }
    };

    document.addEventListener('tutorial-click', handleTutorialClick);

    return () => {
      document.removeEventListener('tutorial-click', handleTutorialClick);
    };
  }, [interviewState]);

  return (
    <>
      <video
        autoPlay
        playsInline
        muted
        ref={videoRef}
        className={'size-full object-cover ' + className}
      />
      {role === 'interviewee' && (
        <canvas ref={canvasRef} className="hidden" />
      )}
    </>
  );
};

export default VideoPlayer;