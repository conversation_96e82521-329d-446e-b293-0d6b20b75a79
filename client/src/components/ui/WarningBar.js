import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const WarningBar = ({ warningType, warning = "" }) => {
  switch (warningType) {
    case 'network':
      return <NetworkWarning />
    case 'face':
      return <FaceWarning warning={warning} />
    default:
      return null;
  }
}

export default WarningBar;

const warningVariants = {
  initial: { 
    opacity: 0, 
    y: -20 
  },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { type: "spring", stiffness: 300, damping: 30 }
  },
  exit: { 
    opacity: 0,
    y: -20,
    transition: { duration: 0.3 }
  },
};

const NetworkWarning = () => {
  return (
    <AnimatePresence>
      <motion.div 
        className="w-full border-b border-yellow-400 bg-yellow-200 py-2 text-center text-xs text-yellow-800 shadow"
        initial="initial"
        animate={["animate"]}
        exit="exit"
        variants={warningVariants}
      >
        <strong>⚠️ Poor Network Connection:</strong> Your video/audio quality may
        be affected.
      </motion.div>
    </AnimatePresence>
  )
}

const FaceWarning = ({ warning }) => {
  return (
    <AnimatePresence>
      <motion.div 
        className="w-full border-b border-yellow-400 bg-yellow-200 py-2 text-center text-xs text-yellow-800 shadow"
        initial="initial"
        animate={["animate"]}
        exit="exit"
        variants={warningVariants}
      >
        <strong>⚠️ Warning:</strong> {warning}
      </motion.div>
    </AnimatePresence>
  )
}
