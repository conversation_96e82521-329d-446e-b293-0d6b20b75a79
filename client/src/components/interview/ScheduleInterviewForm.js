'use client';
import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import {z} from 'zod';

import {TextField, DatePicker, CheckboxField} from '@/components/FormField';
import {Button} from '@/components/ui/button';
import {Form} from '@/components/ui/form';
import {useToast} from '@/components/ui/use-toast';

import {constants} from '@/lib/utilities/Auth';
import {scheduleInterview} from '@/lib/utilities/api';
import Render<PERSON>son from '../RenderJson';
const {errorCodes} = constants;

const questionSchema = z
  .object({
    concepts: z.array(z.string()),
    example: z.string(),
    fileName: z.string(),
    question: z.string(),
    unimplementedFeature: z.string(),
  })
  .optional();

const scheduleInterviewSchema = z.object({
  startDate: z
    .date({
      required_error: 'A start date is required.',
    })
    .min(
      new Date(`${new Date().toISOString().split('T')[0]}T00:00:00`),
      "Date cannot be less than today's date",
    ),
  startTime: z.string().default('09:00'),
  duration: z
    .preprocess(val => {
      if (typeof val === 'string' || typeof val === 'number') {
        return parseInt(val);
      }
      return val;
    }, z.number())
    .default(60),
  isDemo: z.boolean().default(false),
  demoLength: z
    .preprocess(val => {
      if (typeof val === 'string' || typeof val === 'number') {
        return parseInt(val);
      }
      return val;
    }, z.number())
    .default(15),
  recommendedQuestions: z.array(questionSchema),
});

const ScheduleInterviewForm = ({
  submissionId,
  firebaseLink,
  questions,
  userEmail,
  setRoomId,
}) => {
  const {toast} = useToast();
  const form = useForm({
    resolver: zodResolver(scheduleInterviewSchema),
    defaultValues: {
      startDate: new Date(),
      startTime: '09:00',
      duration: 60,
      isDemo: false,
      demoLength: 15,
      recommendedQuestions: [],
    },
  });

  const onSubmit = formData => {
    console.log(formData);
    const {startDate, startTime, duration} = formData;
    const startDateTime = new Date(startDate);
    const startTimeSplit = startTime.split(':');
    startDateTime.setHours(parseInt(startTimeSplit[0]) + 5);
    startDateTime.setMinutes(parseInt(startTimeSplit[1]) + 30);
    startDateTime.setSeconds(0);

    const endTime = new Date(startDateTime);
    endTime.setMinutes(endTime.getMinutes() + duration);

    scheduleInterview({
      startTime: startDateTime.toISOString().split('T').join(' ').split('.')[0],
      endTime: endTime.toISOString().split('T').join(' ').split('.')[0],
      isDemo: formData.isDemo,
      demoLength: formData.demoLength,
      userEmail,
      languageId: 2,
      submissionId,
      firebaseLink,
      recommendedQuestions: formData.recommendedQuestions.filter(
        question => !!question,
      ),
    })
      .then(res => {
        toast({
          variant: 'success',
          description:
            'Interview Scheduled Successfully with room ID: ' + res.roomId,
        });
        setRoomId(res.roomId);
        form.reset();
      })
      .catch(error => {
        console.log(error);
        toast({
          variant: 'destructive',
          description:
            errorCodes.firebase[error.code] ||
            error.errorInfo.message ||
            error.message,
        });
      });
  };

  const handleRecommendedQuestions = (index, checked, value) => {
    let recommendedQuestions = form.watch('recommendedQuestions');
    if (checked) {
      recommendedQuestions[index] = value;
    } else {
      delete recommendedQuestions[index];
    }
    form.setValue('recommendedQuestions', recommendedQuestions);
  };

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full space-y-8">
          <DatePicker
            name="startDate"
            control={form.control}
            label="Start Date"
            from={new Date().setDate(new Date().getDate())}
            to={new Date().setFullYear(new Date().getFullYear() + 1)}
          />
          <TextField
            control={form.control}
            type="time"
            name="startTime"
            label="Start Time"
            placeholder="12:00"
            className="font-semibold"
          />
          <TextField
            control={form.control}
            type="number"
            name="duration"
            label="Duration in minutes"
            placeholder="60"
            className="font-semibold"
          />
          <CheckboxField
            control={form.control}
            name="isDemo"
            label="Is this a demo room?"
          />
          {form.watch('isDemo') && (
            <TextField
              control={form.control}
              type="number"
              name="demoLength"
              label="Demo Length in minutes"
              placeholder="15"
              className="font-semibold"
            />
          )}
          {questions?.length > 0 && (
            <div className="flex flex-col gap-4">
              <h2 className="text-2xl font-semibold">Recommended Questions</h2>
              {questions.map((question, index) => {
                return (
                  <CheckboxField
                    control={form.control}
                    key={index}
                    name={`recommendedQuestions[${index}]`}
                    label={<RenderJson json={question} />}
                    onCheckedChange={checked =>
                      handleRecommendedQuestions(index, checked, question)
                    }
                  />
                );
              })}
            </div>
          )}
          <Button type="submit" className="w-full">
            Submit
          </Button>
        </form>
      </Form>
    </>
  );
};

export default ScheduleInterviewForm;
