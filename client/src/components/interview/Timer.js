import { CodeRoom } from '@/provider/CoderoomProvider';
import React from 'react'
import InterviewTimer from '../InterviewTimer';

const Timer = () => {
  const { giveTimeWarning, handleTimeUp, duration } = CodeRoom();

  const triggers = [
    {
      time: 5 * 60 * 1000,
      callback: giveTimeWarning,
    },
  ];

  return (
    <div className="inline-flex h-[49px] flex-shrink-0 items-center justify-between px-2 py-1">
      <InterviewTimer
        duration={duration}
        onTimeup={handleTimeUp}
        triggers={triggers}
      />
      <div className="flex items-center gap-2">
        <div className="recorder-blink h-[14px] w-[14px] rounded-full bg-red-500"></div>
        <span className="font-semibold text-red-500">Recording</span>
      </div>
    </div>
  )
}

export default Timer