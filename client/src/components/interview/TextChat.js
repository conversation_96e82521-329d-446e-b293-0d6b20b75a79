'use client';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {useRef, useEffect} from 'react';
import {Button} from '../ui/button';
import {Input} from '../ui/input';
import AudioPlayer from '../AudioPlayer';
import JoineeCard from './JoineeCard';
import {UserMedia} from '@/provider/UserMediaProvider';
import ChatCard from './ChatCard';
import {botPeerId, gazeBotPeerId} from '@/values/peerIds';

const TextChat = () => {
  const { messageHistory, sendChatMessage, peers, consumers, inputCountRef } = CodeRoom();
  const {videoTrack} = UserMedia();

  const inputRef = useRef(null);
  const messageContainerRef = useRef(null);

  const InterviewerAudio = () => {
    return Object.keys(peers)
      .filter(
        peerId =>
          peers[peerId].role === 'interviewer' &&
          peerId !== botPeerId &&
          peerId !== gazeBotPeerId,
      )
      .map((peerId, index) =>
        AudioPlayer({
          audioTrack: consumers[peerId]?.mic?.track,
          local: false,
        }),
      );
  };

  useEffect(() => {
    if (messageContainerRef.current) {
      messageContainerRef.current.scrollTop =
        messageContainerRef.current.scrollHeight;
    }
  }, [messageHistory]);

  useEffect(() => {
    const handleKeyDown = event => {
      if (event.key === 'Enter') {
        const message = inputRef.current.value;
        if (message) {
          sendChatMessage(message);
          event.preventDefault();
          inputRef.current.value = '';
        }
      }
    };

    inputRef.current.addEventListener('keydown', handleKeyDown);

    return () => {
      inputRef.current?.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <>
      <div className="relative w-full">
        <JoineeCard
          name={'You'}
          mediaStreamTrack={{audio: '', video: videoTrack}}
          local={true}
          fullWidth={true}
        />
      </div>
      <div className="relative flex flex-1 flex-col overflow-hidden bg-neutral-950">
        <ChatCard />
        <div className="mb-11"></div>
        <InterviewerAudio />

        <div className="absolute bottom-0 left-0 right-0 mx-1 flex items-center gap-2 bg-neutral-950 pb-2">
          <Input
            className=" w-full focus:outline-none"
            placeholder="Type your message..."
            ref={inputRef}
            onChange={(e) => {
              e.preventDefault();
              inputCountRef.current.key_count += 1;
            }}
          />
        </div>
      </div>
    </>
  );
};

export default TextChat;
