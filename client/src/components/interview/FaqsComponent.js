import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import FAQs from '@/values/faqs';

export default function FaqsComponent() {
  return (
    <div className="w-[90%] max-w-xl p-6 mt-6 bg-black text-white rounded-2xl shadow-2xl border border-gray-600 max-h-[80vh] overflow-y-auto">
      <h2 className="text-2xl font-bold mb-4">Troubleshooting FAQs</h2>
      <Accordion type="single" collapsible>
        {Object.entries(FAQs).map(([section, faqs]) => (
          <AccordionItem key={section} value={section}>
            <AccordionTrigger className="text-lg font-bold capitalize">{section} FAQs</AccordionTrigger>
            <AccordionContent>
              {faqs.map((faq, index) => (
                <div key={index} className="mb-4">
                  <p className="font-bold">{faq.question}</p>
                  <p>{faq.answer}</p>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
      <p className="mt-4">
        If the issue persists, feel free to reach out to us at{' '}
        <span className="text-blue-400"><EMAIL></span>.
      </p>
    </div>
  );
}
