import { CodeRoom } from '@/provider/CoderoomProvider';
import { getColorForName } from '@/lib/utilities/getColorForName';
import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

const ChatCard = () => {
  const { messageHistory } = CodeRoom();

  const messageContainerRef = useRef(null);

  useEffect(() => {
    if (messageContainerRef.current) {
      messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
    }
  }, [messageHistory]);
  return (
    <div
      ref={messageContainerRef}
      className="flex h-full w-full flex-1 flex-col space-y-2 overflow-auto px-2 py-2"
    >
      {messageHistory.map((messageObj, index) => (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          key={index}
          className="flex items-start space-x-2"
        >
          <div
            className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-md shadow-sm"
            style={{ backgroundColor: getColorForName(messageObj.peerName) }}
          >
            <p className="text-sm font-bold text-neutral-900">
              {messageObj.peerName.charAt(0).toUpperCase()}
            </p>
          </div>
          <div className="flex flex-1 flex-col">
            <div className="flex items-center">
              <span className="text-xs font-semibold text-neutral-400">
                {messageObj.peerName}
              </span>
            </div>
            <p className="rounded-md bg-neutral-800/50 p-2 text-xs text-neutral-300">
              {messageObj.message}
            </p>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export default ChatCard