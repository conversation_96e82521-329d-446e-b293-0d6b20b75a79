'use client';
import {useEffect, useRef} from 'react';
import {isMobile} from 'react-device-detect';
import {Button} from '@/components/ui/button';

const PushToTalk = ({onPressIn, onPressOut, disabled}) => {
  const pressed = useRef(false);
  const disabledRef = useRef(disabled);

  useEffect(() => {
    disabledRef.current = disabled;
  }, [disabled]);

  function handlePushIn() {
    onPressIn();
    pressed.current = true;
  }

  function handlePushOut() {
    onPressOut();
    pressed.current = false;
  }

  function handleKeyPress(event) {
    if (disabledRef.current) return;

    if (event.key === 'k') {
      if (event.type === 'keydown' && !pressed.current) {
        handlePushIn();
      } else if (event.type === 'keyup') {
        handlePushOut();
      }
    }
  }

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    document.addEventListener('keyup', handleKeyPress);

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
      document.removeEventListener('keyup', handleKeyPress);
    };
  }, []);

  return (
    <Button
      className={`rounded-md p-4 text-white ${pressed.current ? 'bg-yellow-400' : 'bg-primary'} `}
      disabled={disabled}
      onTouchStart={handlePushIn}
      onMouseDown={handlePushIn}
      onTouchEnd={handlePushOut}
      onMouseUp={handlePushOut}>
      {isMobile
        ? pressed.current
          ? 'Recording'
          : 'Hold to talk'
        : pressed.current
          ? 'Recording'
          : 'Hold "K" to talk'}
    </Button>
  );
};

export default PushToTalk;
