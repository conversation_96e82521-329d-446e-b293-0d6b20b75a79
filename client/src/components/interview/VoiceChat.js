import {UserMedia} from '@/provider/UserMediaProvider';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {Interview} from '@/provider/InterviewProvider';
import JoineeCard from '../interview/JoineeCard';
import {motion} from 'framer-motion';
import TranscriptionChat from './TranscriptionChat';
import {botPeerId, gazeBotPeerId} from '@/values/peerIds';

const VoiceChat = () => {
  const {audioTrack, videoTrack} = UserMedia();
  const {peers, consumers, botPaused} = CodeRoom();
  const {role} = Interview();

  const isInterviewer = role === 'interviewer';

  const videoToShow = isInterviewer
    ? Object.entries(peers).find(([peerId]) => consumers[peerId]?.camera?.track)
    : {
        isLocal: true,
        name: 'You',
        mediaStreamTrack: {
          audio: audioTrack,
          video: videoTrack,
        },
      };

  const baseList = Object.keys(peers)
    .filter(peerId => peerId !== botPeerId && peerId !== gazeBotPeerId)
    .map(peerId => ({
      name: peers[peerId].name,
      mediaStreamTrack: {
        audio: consumers[peerId]?.mic?.track,
        video: consumers[peerId]?.camera?.track,
      },
      local: false,
      opacity: 1,
      showListening: false,
    }));

  const evalAI = Object.entries(peers).find(([peerId]) => peerId === botPeerId);

  const joineeCount = baseList.length;

  return (
    <div className="flex h-full w-full flex-1 flex-col overflow-hidden">
      <motion.div
        initial={{opacity: 0}}
        animate={{opacity: 1}}
        className={`flex ${joineeCount > 0 ? 'h-72' : 'h-60'} sticky top-0 w-full overflow-hidden border-b border-neutral-800 bg-neutral-900`}>
        <div className="flex flex-1 flex-col">
          {videoToShow && (
            <motion.div className="relative w-full" layoutId="mainVideo">
              <JoineeCard
                name={videoToShow.isLocal ? 'You' : peers[videoToShow[0]].name}
                mediaStreamTrack={
                  videoToShow.isLocal
                    ? {audio: audioTrack, video: videoTrack}
                    : {
                        audio: consumers[videoToShow[0]]?.mic?.track,
                        video: consumers[videoToShow[0]]?.camera?.track,
                      }
                }
                local={videoToShow.isLocal}
                fullWidth={true}
              />
            </motion.div>
          )}
          {evalAI && (
            <motion.div
              initial={{y: 20, opacity: 0}}
              animate={{y: 0, opacity: 1}}
              className="mt-auto flex h-full w-full border-t border-neutral-800 px-3">
              <div className="flex w-full items-center">
                <JoineeCard
                  name="Eval"
                  mediaStreamTrack={{
                    audio: consumers[evalAI[0]]?.mic?.track,
                  }}
                  opacity={botPaused ? 0.5 : 1}
                  showListening={!botPaused}
                />
              </div>
            </motion.div>
          )}
          {joineeCount > 0 && (
            <motion.div
              initial={{y: 20, opacity: 0}}
              animate={{y: 0, opacity: 1}}
              className="mt-auto flex h-full w-full border-t border-neutral-800 px-3">
              <div className="flex w-full items-center">
                <JoineeCard name="Interview Panel" baseList={baseList} />
              </div>
            </motion.div>
          )}
        </div>
      </motion.div>
      <TranscriptionChat />
    </div>
  );
};

export default VoiceChat;
