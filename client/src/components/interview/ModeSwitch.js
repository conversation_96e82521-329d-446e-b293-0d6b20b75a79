'use client';
import {Switch} from '@/components/ui/switch';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {Interview} from '@/provider/InterviewProvider';
import conversationModes from '@/values/conversationModes';
import {useState} from 'react';

const ModeSwitch = () => {
  const {conversationMode} = Interview();
  const {toggleConversationMode} = CodeRoom();
  const [disabled, setDisabled] = useState(false);
  const checked = conversationMode === conversationModes.voice;

  return (
    <div className="flex items-center gap-2">
      <p className={!checked ? 'text-white' : 'text-neutral-400'}>Text</p>
      <Switch
        disabled={disabled}
        onCheckedChange={val => {
          setDisabled(true);
          const timeout = setTimeout(() => {
            setDisabled(false);
          }, 10000);

          toggleConversationMode(val).finally(() => {
            if (timeout) {
              clearTimeout(timeout);
            }
            setDisabled(false);
          });
        }}
        checked={checked}
      />
      <p className={checked ? 'text-white' : 'text-neutral-400'}>Voice</p>
    </div>
  );
};

export default ModeSwitch;
