import React, {useEffect, useRef} from 'react';

const Countdown = ({
  height,
  width,
  backgroundColor,
  pieSliceColor,
  duration,
  onComplete,
}) => {
  const pieSliceRef = useRef(null);
  let α = 0;
  const π = Math.PI;
  const fps = 60;
  const totalFrames = (duration / 1000) * fps;
  const increment = 360 / totalFrames;

  console.log(duration, 'duration');

  useEffect(() => {
    let animationFrameId;
    const draw = () => {
      α += increment;
      α %= 360;
      const r = (α * π) / 180;
      const x = Math.sin(r) * 125;
      const y = Math.cos(r) * -125;
      const mid = α > 180 ? 1 : 0;
      const anim = `M 0 0 v -125 A 125 125 1 ${mid} 1 ${x} ${y} z`;

      pieSliceRef.current.setAttribute('d', anim);

      if (α < increment) {
        onComplete();
      } else {
        animationFrameId = requestAnimationFrame(draw);
      }
    };

    draw();

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [duration]);

  return (
    <svg width={width} height={height} viewBox="0 0 250 250">
      <circle cx="125" cy="125" r="125" fill={backgroundColor} />
      <path
        ref={pieSliceRef}
        transform="translate(125, 125)"
        fill={pieSliceColor}
      />
    </svg>
  );
};

export default Countdown;
