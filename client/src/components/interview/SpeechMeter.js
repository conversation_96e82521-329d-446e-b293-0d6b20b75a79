import React, {useEffect, useRef, useState} from 'react';

const SpeechMeter = ({audioTrack, onTop = false, size = 'normal'}) => {
  const canvasRef = useRef(null);
  const [analyser, setAnalyser] = useState(null);
  const animationFrameRef = useRef(null);

  const dimensions = {
    normal: {
      canvas: 40,
      barWidth: 7,
      barSpacing: 3.5,
      minBarHeight: 8,
    },
    small: {
      canvas: 20,
      barWidth: 3,
      barSpacing: 1.5,
      minBarHeight: 2.5,
    },
  };

  useEffect(() => {
    let context = null;
    let source = null;
    let analyserNode = null;

    if (audioTrack) {
      try {
        const stream = new MediaStream();
        stream.addTrack(audioTrack);

        context = new (window.AudioContext || window.webkitAudioContext)();
        source = context.createMediaStreamSource(stream);
        analyserNode = context.createAnalyser();
        source.connect(analyserNode);
        analyserNode.fftSize = 256;

        setAnalyser(analyserNode);
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        if (context) {
          context
            .close()
            .catch(err => console.error('Error closing audio context:', err));
        }
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      if (source) {
        source.disconnect();
      }

      if (context) {
        context
          .close()
          .catch(err => console.error('Error closing audio context:', err));
      }
    };
  }, [audioTrack]);

  useEffect(() => {
    if (analyser) {
      const canvas = canvasRef.current;
      const canvasContext = canvas.getContext('2d');
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      const config = dimensions[size];

      const drawRoundedRect = (x, y, width, height, radius) => {
        canvasContext.beginPath();
        canvasContext.moveTo(x + radius, y);
        canvasContext.arcTo(x + width, y, x + width, y + height, radius);
        canvasContext.arcTo(x + width, y + height, x, y + height, radius);
        canvasContext.arcTo(x, y + height, x, y, radius);
        canvasContext.arcTo(x, y, x + width, y, radius);
        canvasContext.closePath();
        canvasContext.fill();
      };

      const draw = () => {
        analyser.getByteFrequencyData(dataArray);

        canvasContext.fillStyle = '#333aea';
        canvasContext.fillRect(0, 0, canvas.width, canvas.height);

        const numBars = 3;
        const {barWidth, barSpacing, minBarHeight} = config;
        const rectWidth = numBars * barWidth + (numBars - 1) * barSpacing;
        const rectX = (canvas.width - rectWidth) / 2;
        const rectY = canvas.height / 2;
        const radius = barWidth / 2;

        let maxLevel = 0;

        for (let i = 0; i < numBars; i++) {
          const start = Math.floor((i * bufferLength) / numBars);
          const end = Math.floor(((i + 1) * bufferLength) / numBars);
          let sum = 0;
          for (let j = start; j < end; j++) {
            sum += dataArray[j];
          }
          let average = (sum / (end - start)) * 0.5;

          if (i === 1) {
            average *= 1.5;
          } else if (i === 2) {
            average *= 1.4;
          }

          const barHeight = Math.max(average / 2, minBarHeight);
          maxLevel = Math.max(maxLevel, barHeight);

          let x;
          if (i === 0) {
            x = rectX + Math.floor(numBars / 2) * (barWidth + barSpacing);
          } else if (i % 2 === 1) {
            x =
              rectX +
              Math.floor(numBars / 2) * (barWidth + barSpacing) -
              Math.ceil(i / 2) * (barWidth + barSpacing);
          } else {
            x =
              rectX +
              Math.floor(numBars / 2) * (barWidth + barSpacing) +
              (i / 2) * (barWidth + barSpacing);
          }

          const y = rectY - barHeight / 2;

          canvasContext.fillStyle = 'white';
          drawRoundedRect(x, y, barWidth, barHeight, radius);
        }

        animationFrameRef.current = requestAnimationFrame(draw);
      };

      draw();
    }
  }, [analyser, size]);

  const canvasSize = dimensions[size].canvas;

  return (
    <div
      className="flex gap-2"
      style={{
        ...(onTop && {
          position: 'absolute',
          top: '2%',
          right: '2%',
        }),
      }}>
      <canvas
        ref={canvasRef}
        height={canvasSize}
        width={canvasSize}
        style={{
          borderRadius: '50%',
        }}
      />
    </div>
  );
};

export default SpeechMeter;
