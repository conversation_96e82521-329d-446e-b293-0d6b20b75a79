'use client';
import {useRef} from 'react';
import SpeechMeter from './SpeechMeter';
import VideoPlayer from '../VideoPlayer';
import AudioPlayer from '../AudioPlayer';
import {getColorForName} from '@/lib/utilities/getColorForName';
import {CodeRoom} from '@/provider/CoderoomProvider';
import botStates from '@/values/botStates';
import {Interview} from '@/provider/InterviewProvider';
import {Mic, MicOff} from 'lucide-react';

const JoineeCard = ({
  name,
  mediaStreamTrack = null,
  local = false,
  opacity = 1,
  fullWidth = false,
  showListening = false,
  baseList = [],
}) => {
  const statusColors = {
    [botStates.IDLE]: 'text-neutral-400',
    [botStates.THINKING]: 'text-yellow-400',
    [botStates.LISTENING]: 'text-green-400',
  };

  const {botState, toggleMute, mute} = CodeRoom();
  const {roomId, role} = Interview();
  const badgeColor = useRef(getColorForName(name)).current;
  const isBot = name === 'Eval';
  const isPanel = name === 'Interview Panel';

  const NameBadge = () => (
    <div className="relative">
      <div
        className={`flex h-9 w-9 items-center justify-center rounded-sm transition-all duration-300`}
        style={{backgroundColor: badgeColor}}>
        <span className="text-xl font-medium text-black">{name.charAt(0)}</span>
        <div className="absolute -right-2 -top-1.5">
          <SpeechMeter audioTrack={mediaStreamTrack?.audio} size={'small'} />
        </div>
      </div>
    </div>
  );

  if (isPanel) {
    return (
      <div className={`flex flex-1 items-center justify-between`}>
        <div className="flex items-center gap-3">
          <NameBadge />
          <span className="text-base font-medium">{name}</span>
        </div>
        <div className="flex items-center gap-3">
          {baseList.map((joinee, index) => (
            <span key={index}>
              <AudioPlayer
                audioTrack={joinee.mediaStreamTrack?.audio}
                local={joinee.local}
              />
            </span>
          ))}
        </div>
      </div>
    );
  }

  if (fullWidth) {
    return (
      <div className={`flex w-full`}>
        <div className={`flex flex-1 items-center`}>
          <div className={`flex flex-1 items-center justify-center`}>
            {mediaStreamTrack?.video ? (
              <div className={`relative h-[185px] w-full overflow-hidden`}>
                <VideoPlayer
                  videoTrack={mediaStreamTrack.video}
                  className="-scale-x-100"
                />
                <div className="absolute right-2 top-2">
                  <SpeechMeter
                    audioTrack={mediaStreamTrack?.audio}
                    size={'small'}
                  />
                </div>
                <div className="absolute left-1 top-1 rounded-sm bg-black/40 px-2 py-0.5 backdrop-blur-sm">
                  <span className="text-xs font-medium text-white">{name}</span>
                </div>
                {roomId === '9ez2-lx49-oavz' && role !== 'interviewer' && (
                  <div className="absolute bottom-2 left-2">
                    <button
                      onClick={toggleMute}
                      className="rounded-full bg-neutral-800 p-2 transition-colors hover:bg-neutral-700">
                      {mute ? (
                        <MicOff className="h-4 w-4 text-red-500" />
                      ) : (
                        <Mic className="h-4 w-4 text-white" />
                      )}
                    </button>
                  </div>
                )}
              </div>
            ) : null}
            <AudioPlayer audioTrack={mediaStreamTrack?.audio} local={local} />
          </div>
        </div>
      </div>
    );
  }

  if (isBot) {
    return (
      <div
        className={`
        flex flex-1 items-center justify-between
        transition-all duration-300
        opacity-${Math.round((opacity * 100) / 5) * 5}
      `}>
        <div className="flex items-center gap-3">
          <NameBadge showHoverName={false} />
          <span className="text-base font-medium">{name}</span>
        </div>
        <div className="flex items-center gap-3">
          <AudioPlayer audioTrack={mediaStreamTrack?.audio} local={local} />
          {showListening ? (
            botState && botState !== botStates.IDLE ? (
              <div
                className={`
              flex items-center gap-2 rounded-full bg-neutral-800 
              px-3 py-1 
              ${statusColors[botState]}
            `}>
                <div className="h-2 w-2 animate-pulse rounded-full bg-current" />
                <p className="text-xs font-medium">{botState}</p>
              </div>
            ) : null
          ) : (
            <div className="flex items-center gap-1.5 rounded-full bg-red-500/10 px-2 py-0.5">
              <span className="text-xs font-medium text-red-500">Paused</span>
            </div>
          )}
        </div>
      </div>
    );
  }
};

export default JoineeCard;
