import { CodeRoom } from '@/provider/CoderoomProvider';
import { Interview } from '@/provider/InterviewProvider';
import { motion, AnimatePresence } from 'framer-motion';
import ChatCard from './ChatCard';

const TranscriptionChat = () => {
  const { botPaused } = CodeRoom();
  const { recognizedText } = Interview();

  return (
    <div className="relative flex flex-1 flex-col overflow-hidden bg-neutral-950">
      <ChatCard />
      <div className='absolute top-0 w-full'>
        <AnimatePresence>
          {!botPaused && recognizedText && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
              className="mx-1 rounded-md bg-blue-600/30 p-2 backdrop-blur-sm"
            >
              <div className="flex items-center space-x-1">
                <div className="h-1.5 w-1.5 animate-pulse rounded-full bg-blue-400" />
                <p className="text-xs font-medium text-blue-100">
                  Transcribing...
                </p>
              </div>
              <p className="mt-1 text-xs text-neutral-50">
                {recognizedText}
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default TranscriptionChat;