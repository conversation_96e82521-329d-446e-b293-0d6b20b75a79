import {Progress} from '../ui/progress';
import {Button} from '../ui/button';
import {toast} from '../ui/use-toast';

const TestMic = ({ volume, micAccess, setMicAccess, setShowOverlay }) => {
  // this file is not being used in the project currently

  return (
    <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-black bg-opacity-50">
      <div className="flex flex-col gap-4 rounded-lg bg-white p-8">
        <h2 className="text-xl font-bold">Test your Microphone</h2>
        <p className="text-muted-foreground">
          Allow microphone access and make sure that it is working fine.
        </p>
        <Progress value={volume} />
        <p className="text-muted-foreground">Try speaking a few words</p>
        <div className="flex gap-4">
          <Button
            variant={'outline'}
            className="mt-8 w-fit rounded-full px-8"
            onClick={() => {
              setMicAccess(false);
              setShowOverlay(false);
            }}>
            Cancle
          </Button>
          <Button
            className="mt-8 w-fit rounded-full px-8"
            onClick={() => {
              if (micAccess) {
                setShowOverlay(false);
              } else {
                toast({
                  variant: 'destructive',
                  title: 'No Microphone Access!',
                  description: 'First allow microphone access.',
                });
              }
            }}>
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TestMic;
