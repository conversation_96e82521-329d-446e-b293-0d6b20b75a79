import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { UserMedia } from "@/provider/UserMediaProvider"
import { useEffect, useState } from 'react'
import { RefreshCcw, Mic, Video, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Command, CommandGroup, CommandItem } from "@/components/ui/command"

const DeviceModal = ({ open, onClose, type, onDeviceSelect, children }) => {
  const { devices, setDevices } = UserMedia()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const fetchDevices = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const deviceList = await navigator.mediaDevices.enumerateDevices()
      const list = deviceList.filter(device => device.kind === type && device.deviceId)
      if (list.length === 0) {
        setError('Failed to fetch devices. Please check permissions.')
      }
      setDevices({ ...devices, [type]: list })
    } catch (err) {
      setError('Failed to fetch devices. Please check permissions.')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (open) {
      fetchDevices()
    }
  }, [open, type])

  const renderDeviceList = () => {
    if (!devices || !devices[type] || devices[type].length === 0) {
      return (
        <CommandItem value="no-devices" disabled>
          No devices available
        </CommandItem>
      )
    }

    return devices[type].map(device => (
      <CommandItem
        key={device.deviceId}
        onSelect={() => onDeviceSelect(device.deviceId)}
        className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <div className="flex items-center gap-2">
          {type === 'audioinput' ?
            <Mic className="w-4 h-4 text-purple-600" /> :
            <Video className="w-4 h-4 text-purple-600" />
          }
          <span>{device.label || `Device ${devices[type].indexOf(device) + 1}`}</span>
        </div>
      </CommandItem>
    ))
  }

  return (
    <Popover open={open} onOpenChange={onClose}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" side="top">
        <div className="p-4 pb-0">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium">
              Select {type === 'audioinput' ? 'Microphone' : 'Camera'}
            </h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchDevices}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCcw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          {error && (
            <div className="flex items-center gap-2 text-red-500 text-sm mb-2">
              <AlertCircle className="w-4 h-4" />
              {error}
            </div>
          )}
        </div>

        <Command className="max-h-[300px] overflow-auto">
          <CommandGroup>
            {renderDeviceList()}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

export default DeviceModal