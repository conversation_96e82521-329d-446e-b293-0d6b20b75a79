'use client';
import {useRef, useEffect} from 'react';

const AudioPlayer = ({audioTrack, local}) => {
  const ref = useRef();

  useEffect(() => {
    if (audioTrack && ref.current) {
      const stream = new MediaStream();
      stream.addTrack(audioTrack);
      ref.current.srcObject = stream;
      ref.current?.play();
    }
  }, [audioTrack]);

  return (
    <audio ref={ref} className="hidden" autoPlay playsInline muted={local} />
  );
};

export default AudioPlayer;
