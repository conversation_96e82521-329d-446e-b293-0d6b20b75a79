import {
  Cross2Icon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@radix-ui/react-icons';
import {CodeRoom} from '@/provider/CoderoomProvider';
import Image from 'next/image';
import {useRef, useState, useEffect} from 'react';
import getFileIcon from '@/lib/utilities/CodeRoom/getFileIcon';

const Tabs = () => {
  const {tabs, selectedFile, handleFileSelect, handleCloseTab} = CodeRoom();
  const childRef = useRef(null);
  const containerRef = useRef(null);
  const [showLeftScroll, setShowLeftScroll] = useState(false);
  const [showRightScroll, setShowRightScroll] = useState(false);

  const checkScroll = () => {
    const container = childRef.current;
    if (container) {
      setShowLeftScroll(container.scrollLeft > 0);
      setShowRightScroll(
        container.scrollLeft <
          container.scrollWidth - container.clientWidth - 1,
      );
    }
  };

  useEffect(() => {
    const container = childRef.current;
    if (container) {
      container.addEventListener('scroll', checkScroll);
      checkScroll();
    }
    return () => container?.removeEventListener('scroll', checkScroll);
  }, [tabs]);

  useEffect(() => {
    checkScroll();
  }, [tabs]);

  const scroll = direction => {
    const container = childRef.current;
    if (container) {
      const scrollAmount = container.clientWidth * 0.5;
      const targetScroll =
        container.scrollLeft +
        (direction === 'left' ? -scrollAmount : scrollAmount);

      container.scrollTo({
        left: targetScroll,
        behavior: 'smooth',
      });
    }
  };

  const handleWheel = e => {
    if (childRef.current) {
      e.preventDefault();

      const scrollMultiplier = 0.5;
      const smoothScrollAmount = e.deltaY * scrollMultiplier;

      childRef.current.scrollTo({
        left: childRef.current.scrollLeft + smoothScrollAmount,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    if (!containerRef.current || !childRef.current) return;

    const updateSize = () => {
      childRef.current.style.width = `${containerRef.current.clientWidth}px`;
    };

    const observer = new ResizeObserver(updateSize);
    observer.observe(containerRef.current);

    return () => observer.disconnect();
  }, []);

  return (
    <div
      className="relative flex w-full items-center bg-black"
      ref={containerRef}>
      {showLeftScroll && (
        <button
          onClick={() => scroll('left')}
          className="absolute left-0 h-full bg-black p-1 text-white">
          <ChevronLeftIcon height={16} width={16} />
        </button>
      )}

      <div
        ref={childRef}
        onWheel={handleWheel}
        className="scrollbar-hide flex items-center overflow-x-auto scroll-smooth"
        style={{
          msOverflowStyle: 'none',
          scrollbarWidth: 'none',
          WebkitOverflowScrolling: 'touch',
        }}>
        {tabs.map(tab => (
          <div
            key={tab}
            className={`group flex flex-none items-center gap-1 whitespace-nowrap border-r border-[#252525] px-2 py-1 font-serif text-sm font-normal text-white ${
              selectedFile === tab ? 'bg-[#252525]' : 'bg-transparent'
            }`}
            onClick={e => {
              e.stopPropagation();
              handleFileSelect(tab);
            }}>
            <Image
              src={getFileIcon(tab?.split('.').pop())}
              height={13}
              width={13}
              alt="file-icon"
            />
            {tab.split('/').pop()}
            <div
              className={`rounded-[1px] p-[0.8px] ${
                selectedFile === tab
                  ? 'opacity-100'
                  : 'opacity-0 group-hover:opacity-100'
              } hover:bg-red-500`}
              onClick={e => {
                e.stopPropagation();
                handleCloseTab(tab);
              }}>
              <Cross2Icon height={11} width={11} />
            </div>
          </div>
        ))}
      </div>

      {showRightScroll && (
        <button
          onClick={() => scroll('right')}
          className="absolute right-0 h-full flex-none bg-black p-1 text-white">
          <ChevronRightIcon height={16} width={16} />
        </button>
      )}
    </div>
  );
};

export default Tabs;
