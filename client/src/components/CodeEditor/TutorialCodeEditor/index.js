import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import TutorialFileExplorer from './TutorialFileExplorer';
import Tabs from '../Tabs';
import FileEditor from '../Editors/Monaco';
import TutorialQuestion from './TutorialQuestion';

function TutorialCodeEditor() {
  return (
    <div
      className="flex h-full w-full flex-col font-serif">
      <TutorialQuestion />
      <ResizablePanelGroup
        direction="horizontal"
        className="h-full w-full border-t font-serif">
        <ResizablePanel
          defaultSize={20}
          minSize={20}>
          <TutorialFileExplorer />
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={80} minSize={30}>
          <div className="relative flex h-full w-full flex-col bg-black">
            <div className="flex-none">
              <Tabs />
            </div>
            <div className="min-h-0 flex-1">
              <FileEditor />
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}

export default TutorialCodeEditor;
