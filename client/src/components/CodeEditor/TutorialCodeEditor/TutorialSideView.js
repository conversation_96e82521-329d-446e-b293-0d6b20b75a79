import {Button} from '@/components/ui/button';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {UserMedia} from '@/provider/UserMediaProvider';
import {motion} from 'framer-motion';
import {tutorialSteps} from '@/values/tutorialSteps';
import JoineeCard from '@/components/interview/JoineeCard';
import {Tutorial} from '@/provider/TutorialProvider';
import {useEffect} from 'react';
import {botPeerId} from '@/values/peerIds';

const TutorialSideView = () => {
  const {
    peers,
    consumers,
    botPaused,
    tutorialAction,
  } = CodeRoom();

  const {audioTrack, videoTrack} = UserMedia();
  const {tutorialStep, setMounted} = Tutorial();

  useEffect(() => {
    setMounted(true);
  }, []);

  const evalAI = Object.entries(peers).find(([peerId]) => peerId === botPeerId);

  return (
    <div className="flex h-full w-full flex-col justify-between border-l bg-black">
      <div className="flex h-full w-full flex-1 flex-col overflow-hidden">
        <motion.div
          initial={{opacity: 0}}
          animate={{opacity: 1}}
          className={`sticky top-0 flex h-60 w-full overflow-hidden border-b border-neutral-800 bg-neutral-900`}>
          <div className="flex flex-1 flex-col">
            <motion.div className="relative w-full" layoutId="mainVideo">
              <JoineeCard
                name={'You'}
                mediaStreamTrack={{audio: audioTrack, video: videoTrack}}
                local={true}
                fullWidth={true}
              />
            </motion.div>
            {evalAI && (
              <motion.div
                initial={{y: 20, opacity: 0}}
                animate={{y: 0, opacity: 1}}
                className="mt-auto flex h-full w-full border-t border-neutral-800 px-3">
                <div className="flex w-full items-center">
                  <JoineeCard
                    name="Eval"
                    mediaStreamTrack={{
                      audio: consumers[evalAI[0]]?.mic?.track,
                    }}
                    opacity={botPaused ? 0.5 : 1}
                    showListening={!botPaused}
                  />
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      </div>
      <div className="flex flex-wrap items-center justify-center gap-1 border-t border-neutral-800 px-2 py-3">
        <Button
          data-step={tutorialSteps.SKIP_QUESTION}
          data-overlay={tutorialSteps.SKIP_QUESTION}
          disabled={tutorialStep !== tutorialSteps.SKIP_QUESTION}
          variant="gradientPrimary"
          onClick={tutorialStep === tutorialSteps.SKIP_QUESTION && tutorialAction}>
          Skip Question
        </Button>
        <Button
          data-step={tutorialSteps.PUBLISH}
          disabled={tutorialStep !== tutorialSteps.PUBLISH}
          variant="gradientPrimary"
          onClick={tutorialStep === tutorialSteps.PUBLISH && tutorialAction}>
          Publish
        </Button>
      </div>
    </div>
  );
};

export default TutorialSideView;
