import {CodeRoom} from '@/provider/CoderoomProvider';
import {ScrollArea} from '@/components/ui/scroll-area';
import TutorialDirectory from './TutorialDirectory';
import TutorialContextMenuWrapper from './TutorialContextMenuWrapper';
import TutorialSyntaxSearch from './TutorialSyntaxSearch';

function TutorialFileExplorer() {
  const {files, addItem} = CodeRoom();

  return (
    <TutorialContextMenuWrapper
      className={'h-full w-full'}
      menuItems={{
        'New File...': {
          variant: 'default',
          onSelect: () => addItem(files.name, 1),
        },
        'New Folder...': {
          variant: 'default',
          onSelect: () => addItem(files.name, 0),
        },
      }}>
      <ScrollArea className="h-full w-full overflow-x-scroll bg-black px-2 py-4">
        <TutorialSyntaxSearch />
        <TutorialDirectory path={''} files={files} header={true} />
      </ScrollArea>
    </TutorialContextMenuWrapper>
  );
}

export default TutorialFileExplorer;
