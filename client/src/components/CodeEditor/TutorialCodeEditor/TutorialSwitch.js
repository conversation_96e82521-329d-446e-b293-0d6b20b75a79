import {tutorialSteps} from '@/values/tutorialSteps';
import {Tutorial} from '@/provider/TutorialProvider';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {Switch} from '@/components/ui/switch';

const TutorialSwitch = () => {
  const {tutorialAction} = CodeRoom();
  const {tutorialStep} = Tutorial();
  return (
    <div
      className="flex items-center gap-2"
      onClick={tutorialStep === tutorialSteps.SWITCH && tutorialAction}
      disabled={tutorialStep !== tutorialSteps.SWITCH}
      data-step={tutorialSteps.SWITCH}
      data-overlay={tutorialSteps.SWITCH}>
      <p className={'text-neutral-400'}>Text</p>
      <Switch checked={true} />
      <p className={'text-white'}>Voice</p>
    </div>
  );
};

export default TutorialSwitch;
