import {useEffect, useRef} from 'react';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {ChevronDownIcon} from '@radix-ui/react-icons';
import InputField from '../InputField';
import TutorialDirectory from './TutorialDirectory';
import TutorialContextMenuWrapper from './TutorialContextMenuWrapper';
import {Tutorial} from '@/provider/TutorialProvider';
import {tutorialSteps} from '@/values/tutorialSteps';
import Image from 'next/image';

const TutorialFolder = ({path, files, header, className}) => {
  const inputRef = useRef();
  const {toggleCollapsed, deleteItem, addItem, disableEditor} = CodeRoom();
  const {tutorialStep, setTutorialStep} = Tutorial();

  const PATH = path + '/' + files.name;

  const handleRename = () => {
    inputRef.current.focus();
  };

  useEffect(() => {
    if (files.inFocus) setTimeout(handleRename, 10);
  }, [files.inFocus]);

  return (
    <div className={' ' + className}>
      <TutorialContextMenuWrapper
        className={
          'mt-1 h-[20px] px-2 text-xs hover:bg-neutral-800 hover:text-white '
        }
        menuItems={tutorialStep === tutorialSteps.ROOT_FOLDER ? {} : {
          'New File...': {
            variant: 'default',
            onSelect: () => {
              addItem(PATH, 1);
            },
          },
          'New Folder...': {
            variant: 'default',
            onSelect: () => addItem(PATH, 0),
          },
          ...(header
            ? {}
            : {
                Delete: {
                  variant: 'destructive',
                  onSelect: () => deleteItem(PATH),
                },
              }),
        }}
        disabled={disableEditor}
        onClick={() => {
          if (!disableEditor) toggleCollapsed(PATH);
        }}>
        <div
          data-overlay={header && tutorialSteps.ROOT_FOLDER}
          data-step={header && tutorialSteps.ROOT_FOLDER}
          onClick={() => header && tutorialStep === tutorialSteps.ROOT_FOLDER && setTutorialStep(prev => prev + 1)}
          className={`flex items-center gap-[3px] ${disableEditor ? 'text-neutral-400' : ''}`}>
          <ChevronDownIcon
            className={`flex-shrink-0 transform transition-transform duration-200 ${
              !files.collapsed ? 'rotate-0' : '-rotate-90'
            }`}
            height={13}
            width={13}
          />
          <Image
            src={
              files.collapsed ? '/icons/folder.svg' : '/icons/folder_open.svg'
            }
            height={13}
            width={13}
            alt="folder icon"
          />
          <h2 className={`text-nowrap text-xs ${header ? 'uppercase' : ''} `}>
            <InputField
              fileName={files.name}
              type={0}
              path={PATH}
              ref={inputRef}
            />
          </h2>
        </div>
      </TutorialContextMenuWrapper>

      {!files.collapsed &&
        files.items.map(item => (
          <TutorialDirectory
            path={PATH}
            files={item}
            key={PATH + '/' + item.name}
            className={header ? '' : 'ml-4'}
          />
        ))}
    </div>
  );
};

export default TutorialFolder;
