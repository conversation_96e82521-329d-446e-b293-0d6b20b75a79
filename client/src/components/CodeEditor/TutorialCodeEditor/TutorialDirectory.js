import TutorialFile from './TutorialFile';
import TutorialFolder from './TutorialFolder';

const TutorialDirectory = ({path, files, header = false, className}) => {
  if (files.type === 0) {
    return (
      <TutorialFolder
        path={path}
        files={files}
        header={header}
        className={className}
      />
    );
  }
  return (
    <TutorialFile
      path={path}
      fileName={files.name}
      className={className}
      inFocus={files?.inFocus || false}
    />
  );
};

export default TutorialDirectory;
