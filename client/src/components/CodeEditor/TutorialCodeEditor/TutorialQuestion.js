import {CodeRoom} from '@/provider/CoderoomProvider';
import {motion, useAnimation} from 'framer-motion';
import {useEffect, useRef, useState} from 'react';
import {ChevronDownIcon} from '@radix-ui/react-icons';
import {tutorialSteps} from '@/values/tutorialSteps';
import {Tutorial} from '@/provider/TutorialProvider';

const TutorialQuestion = () => {
  const {questionData, tutorialAction} = CodeRoom();
  const {tutorialStep} = Tutorial();
  const controls = useAnimation();
  const [expanded, setExpanded] = useState(true);
  const [collapsedHeight, setCollapsedHeight] = useState('auto');
  const textRef = useRef(null);

  useEffect(() => {
    if (textRef.current) {
      const computedStyle = window.getComputedStyle(textRef.current);
      const singleLineHeight = computedStyle.lineHeight;
      setCollapsedHeight(singleLineHeight);
    }
  }, []);

  useEffect(() => {
    if (textRef.current) {
      const fullHeight = textRef.current.scrollHeight + 'px';
      if (expanded) {
        controls.start({
          opacity: 1,
          height: fullHeight,
          transition: {duration: 0.5},
        });
      } else {
        controls.start({
          opacity: 1,
          height: collapsedHeight,
          transition: {duration: 0.5},
        });
      }
    }
  }, [expanded, questionData, collapsedHeight, controls]);

  const handleClick = () => {
    setExpanded(!expanded);
    if (tutorialStep === tutorialSteps.QUESTION) {
      tutorialAction();
    }
  };

  return (
    <div
      data-step={tutorialSteps.QUESTION}
      data-overlay={tutorialSteps.QUESTION}
      className="flex w-full items-center justify-between bg-black p-2 border-t"
      onClick={handleClick}
      style={{cursor: 'pointer'}}>
      <motion.p
        ref={textRef}
        initial={{opacity: 0, height: collapsedHeight}}
        animate={controls}
        className="overflow-clip text-sm"
      >
        {questionData.question}
      </motion.p>

      <motion.div
        className="p-1 text-neutral-400"
        animate={{rotate: expanded ? -180 : 0}}
        transition={{duration: 0.2}}>
        <ChevronDownIcon height={25} width={25} />
      </motion.div>
    </div>
  );
};

export default TutorialQuestion;
