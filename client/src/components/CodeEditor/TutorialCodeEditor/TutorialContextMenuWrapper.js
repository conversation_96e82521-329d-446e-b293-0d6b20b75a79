import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';
import {Interview} from '@/provider/InterviewProvider';

const TutorialContextMenuWrapper = ({
  children,
  onClick,
  menuItems,
  disabled = true,
  className,
}) => {
  const {role} = Interview();
  return (
    <ContextMenu>
      <ContextMenuTrigger
        className={`flex items-center ${className}`}
        disabled={disabled || role === 'interviewer'}
        onClick={onClick}>
        {children}
      </ContextMenuTrigger>
      {menuItems && <ContextMenuContent className="w-52">
        {Object.keys(menuItems).map((item, index) => (
          <ContextMenuItem
            key={index}
            inset
            onSelect={menuItems[item].onSelect}
            className={`text-neutral-500  ${menuItems[item].variant == 'destructive' ? 'focus:bg-destructive focus:text-destructive-foreground' : ''}`}>
            {item}
          </ContextMenuItem>
        ))}
      </ContextMenuContent>}
    </ContextMenu>
  );
};

export default TutorialContextMenuWrapper;
