import {CodeIcon} from '@radix-ui/react-icons';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {tutorialSteps} from '@/values/tutorialSteps';
import {Tutorial} from '@/provider/TutorialProvider';

const TutorialSyntaxSearch = () => {
  const {tutorialAction} = CodeRoom();
  const {tutorialStep} = Tutorial();

  return (
    <div
      className="flex items-center gap-2 text-xs text-neutral-400"
      onClick={tutorialStep === tutorialSteps.SYNTAX_SEARCH && tutorialAction}
      disabled={tutorialStep !== tutorialSteps.SYNTAX_SEARCH}
      data-step={tutorialSteps.SYNTAX_SEARCH}
      data-overlay={tutorialSteps.SYNTAX_SEARCH}>
      <div className="rounded-sm bg-[#ffd13d] p-1 text-black">
        <CodeIcon className="h-4 w-4" />
      </div>
      Search Syntax
    </div>
  );
};

export default TutorialSyntaxSearch;
