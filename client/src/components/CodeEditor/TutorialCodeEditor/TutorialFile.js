import {useEffect, useRef, useMemo} from 'react';
import {CodeRoom} from '@/provider/CoderoomProvider';
import InputField from '../InputField';
import {tutorialSteps} from '@/values/tutorialSteps';
import TutorialContextMenuWrapper from './TutorialContextMenuWrapper';
import {Tutorial} from '@/provider/TutorialProvider';
import {Interview} from '@/provider/InterviewProvider';
import interviewStates from '@/values/interviewStates';
import Image from 'next/image';
import getFileIcon from '@/lib/utilities/CodeRoom/getFileIcon';

const TutorialFile = ({path, fileName, className, inFocus}) => {
  const { tutorialStep, showInfoBox } = Tutorial();
  const {interviewState} = Interview();
  const inputRef = useRef();
  const { selectedFile, handleFileSelect, deleteItem, disableEditor, tutorialAction } =
    CodeRoom();

  useEffect(() => {
    if (interviewState === interviewStates.CODING_TUTORIAL) {
      showInfoBox(tutorialStep);
    }
  }, [tutorialStep, interviewState]);

  const PATH = path + '/' + fileName;

  const handleRename = () => {
    inputRef.current.focus();
  };

  useEffect(() => {
    if (inFocus) setTimeout(handleRename, 10);
  }, [inFocus]);

  const isFileSelected = selectedFile === PATH;

  const fileIconPath = useMemo(() => {
    const fileType = fileName?.split('.').pop();
    return getFileIcon(fileType);
  }, [fileName]);

  return (
    <TutorialContextMenuWrapper
      className={
        'mt-1 h-[20px] px-2 text-xs hover:bg-neutral-800 hover:text-white ' +
        className +
        (isFileSelected ? ' bg-neutral-800 text-white' : ' text-neutral-400')
      }
      menuItems={{
        Rename: {
          variant: 'default',
          onSelect: () => handleRename(),
        },

        Delete: {
          variant: 'destructive',
          onSelect: () => deleteItem(PATH),
        },
      }}
      disabled={disableEditor}
      onClick={() => {
        if (!disableEditor) handleFileSelect(PATH);
      }}>
      <div
        data-overlay={(fileName.toLowerCase() === 'intro.txt') && tutorialSteps.TUTORIAL_FILE}
        data-step={
          (fileName.toLowerCase() === 'intro.txt') &&
          tutorialSteps.TUTORIAL_FILE
        }
        onClick={() => {
          if ((fileName.toLowerCase() === 'intro.txt') && (tutorialStep === tutorialSteps.TUTORIAL_FILE)) {
            tutorialAction();
          }
        }}
        className={`flex items-center gap-[3px] ${disableEditor ? 'text-neutral-400' : ''}`}>
        <Image src={fileIconPath} height={13} width={13} alt={'file icon'} />
        <InputField fileName={fileName} type={1} path={PATH} ref={inputRef} />
      </div>
    </TutorialContextMenuWrapper>
  );
};

export default TutorialFile;
