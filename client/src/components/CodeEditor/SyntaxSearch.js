'use client';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Input } from '@/components/ui/input';
import { CodeRoom } from '@/provider/CoderoomProvider';
import { Interview } from '@/provider/InterviewProvider';
import { MagnifyingGlassIcon, CopyIcon } from '@radix-ui/react-icons';
import LoadingSpinner from '@/components/LoadingSpinner';
import { motion } from 'framer-motion';
import { QUERY_WORD_LIMIT } from '@/values/syntaxSearch';
import { CodeIcon } from '@radix-ui/react-icons';
import { useToast } from '@/components/ui/use-toast';

const SyntaxSearch = () => {
  const {getSyntax, disableButtons, inputCountRef} = CodeRoom();
  const {interviewDetails} = Interview();
  const {toast} = useToast();
  const [open, setOpen] = useState(false);
  const [language, setLanguage] = useState('');
  const [query, setQuery] = useState('');
  const [syntax, setSyntax] = useState('');
  const [loading, setLoading] = useState(false);
  const [queryWordCount, setQueryWordCount] = useState(0);

  function onOpenDialog() {
    if (loading && open) {
      return;
    }
    setOpen(open => !open);
  }

  function handleCopy() {
    if (!syntax) {
      toast({
        variant: 'warning',
        title: 'Warning',
        description: 'No syntax available to copy.',
      });
      return;
    }

    const encodedSyntax = `__internal_copy__${btoa(syntax)}`; 

    navigator.clipboard
      .writeText(encodedSyntax)
      .then(() => {
        toast({
          variant: 'success',
          title: 'Copied!',
          description: 'Syntax copied successfully.',
        });
      })
      .catch(err => {
        console.error('Copy failed:', err);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to copy syntax.',
        });
      });
  }


  useEffect(() => {
    if (!open) {
      setQuery('');
      return;
    }

    let processing = false;
    const handleEnter = e => {
      if (
        !processing &&
        language.length &&
        queryWordCount >= 1 &&
        e.key === 'Enter'
      ) {
        processing = true;
        handleClick().finally(() => (processing = false));
      }
    };

    window.addEventListener('keydown', handleEnter);
    return () => window.removeEventListener('keydown', handleEnter);
  }, [open, language, queryWordCount]);

  const handleInputChange = e => {
    const input = e.target.value;
    const words = input.trim().split(/\s+/);
    inputCountRef.current.key_count += 1;

    if (words.length <= QUERY_WORD_LIMIT) {
      setQuery(input);
      setQueryWordCount(words.length);
    }
  };

  const handleClick = async () => {
    try {
      setLoading(true);
      const res = await getSyntax(language, query);
      setSyntax(res);
    } catch (err) {
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='sticky top-0 bg-black z-10'>
      <Popover open={open} onOpenChange={onOpenDialog}>
        <PopoverTrigger disabled={disableButtons}>
          <div className="flex items-center gap-2 text-xs text-neutral-400">
            <div className="rounded-sm bg-[#ffd13d] p-1 text-black">
              <CodeIcon className="h-4 w-4" />
            </div>
            Search Syntax
          </div>
        </PopoverTrigger>
        <PopoverContent className="ml-10 overflow-hidden  border-[#252525]  bg-black text-sm">
          <p className="mb-4 w-full text-left">
            Search for any syntax or code snippet you want to know about.
          </p>
          <motion.div layout className="grid gap-4">
            <div className="flex w-full flex-col gap-5">
              <div className="flex w-full flex-col">
                <div className="flex items-center justify-between">
                  <p>Lanugauge</p>
                </div>
                <Select onValueChange={setLanguage} value={language}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a language" />
                  </SelectTrigger>
                  <SelectContent>
                    {interviewDetails.programmingLanguages.map(language => (
                      <SelectItem key={language} value={language}>
                        {language}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex w-full flex-col">
                <div className="flex items-center justify-between">
                  <p>Query</p>
                  <p className="text-xs text-neutral-400">
                    Words: {queryWordCount}/{QUERY_WORD_LIMIT}
                  </p>
                </div>
                <Input
                  value={query}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Write your syntax query here..."
                  className="w-full"
                />
              </div>
              <Button
                className="w-full"
                disabled={
                  queryWordCount === 0 ||
                  disableButtons ||
                  loading ||
                  language.length === 0
                }
                onClick={handleClick}>
                {loading ? (
                  <LoadingSpinner />
                ) : (
                  <>
                    <MagnifyingGlassIcon className="mr-2 h-4 w-4" />
                    Search
                  </>
                )}
              </Button>
            </div>
            {syntax.length ? (
              <div className="relative ">
                <div className=" max-h-[20dvh] overflow-x-scroll overflow-y-scroll">
                  <button onClick={handleCopy} className="absolute right-2 top-1">
                    <CopyIcon className="h-4 w-4 text-neutral-400 hover:text-white" />
                  </button>
                  <pre className="max-w-[10ch] pt-8">
                    <code className="enable-select whitespace-pre break-words text-left text-sm text-neutral-400">
                      {syntax}
                    </code>
                  </pre>
                </div>
              </div>
            ) : null}
          </motion.div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default SyntaxSearch;
