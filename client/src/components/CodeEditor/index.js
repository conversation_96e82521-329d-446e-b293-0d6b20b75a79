import FileExplorer from './FileExplorer';
import Tabs from './Tabs';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import Question from './Question';
import { CodeRoom } from '@/provider/CoderoomProvider';
import { useEditor } from '@/hooks/useEditor';
import { disableShortcuts } from '@/lib/utilities/disableShortcuts';
import { useEffect } from 'react';
import { Interview } from '@/provider/InterviewProvider';


function CodeEditor() {
  const { interviewDetails } = Interview();

  useEffect(() => {
    if (interviewDetails?.isDemo) {
      return;
    }
    window.addEventListener('keydown', disableShortcuts);
    return () => {
      window.removeEventListener('keydown', disableShortcuts);
    };
  }, [interviewDetails?.isDemo]);

  const { selectedFile } = CodeRoom();
  const { RenderedEditor, editorDropdown } = useEditor();

  return (
    <div className="flex h-full w-full flex-col font-serif">
      <Question />
      <ResizablePanelGroup
        direction="horizontal"
        className="h-full w-full border-t font-serif">
        <ResizablePanel defaultSize={20} minSize={20}>
          <FileExplorer />
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={80} minSize={30}>
          <div className="relative flex h-full w-full flex-col bg-black">
            <div className="flex-none">
              <Tabs />
              {selectedFile?.length ? (
                <p className="w-full bg-[#252525] px-2 py-1 text-xs text-neutral-400">
                  {selectedFile.replaceAll('/', ' > ')}
                </p>
              ) : null}
            </div>

            <div className="min-h-0 flex-1">
              {RenderedEditor && <RenderedEditor />}
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
      {/* <div className="flex items-center bg-black p-0.5 text-xs">
        <p className="ml-auto mr-1">Having trouble? Switch editor</p>{' '}
        {editorDropdown}
      </div> */}
    </div>
  );
}

export default CodeEditor;
