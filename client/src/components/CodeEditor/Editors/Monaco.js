'use client';
import dynamic from 'next/dynamic';

import {useRef, useEffect, useState} from 'react';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {Interview} from '@/provider/InterviewProvider';
import {languageAliases} from '@/values/languageAliases';
import {getTimeStamp} from '@/lib/utilities/CodeRoom/getTime';
import {debounce} from '@/lib/utilities/debounce';
import {useToast} from '@/components/ui/use-toast';
import {getCleanTextOnPaste} from '@/lib/utilities/handlePaste';
import interviewStates from '@/values/interviewStates';

const Editor = () => {
  const {
    selectedFile,
    filesData,
    handleOperations,
    disableEditor,
    questionData,
    keyDataRef,
    inputCountRef,
  } = CodeRoom();
  const {toast} = useToast();
  const { interviewDetails, interviewState } = Interview();

  const editorRef = useRef(null);
  const monacoRef = useRef(null);
  const editorInstanceRef = useRef(null);
  const [isEditorReady, setIsEditorReady] = useState(false);

  useEffect(() => {
    if (!questionData) return;

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      handleMouseMove.cancel();
    };
  }, [questionData]);

  const handleMouseMove = debounce(() => {
    inputCountRef.current = {
      ...inputCountRef.current,
      mouse_movement_count: inputCountRef.current.mouse_movement_count + 1,
    };
  }, 100);

  const handleScroll = debounce(() => {
    inputCountRef.current = {
      ...inputCountRef.current,
      mouse_movement_count: inputCountRef.current.mouse_movement_count + 1,
    };
  }, 100);

  const handleMouseDown = event => {
    if (!editorInstanceRef.current) {
      return;
    }
    const {lineNumber, column} = editorInstanceRef.current.getPosition();
    keyDataRef.current.push([
      getTimeStamp(),
      'mouse_click',
      lineNumber,
      column,
    ]);
    inputCountRef.current.mouse_movement_count += 1;
  };

  const handleKeyDown = event => {
    if (!editorInstanceRef.current) {
      return;
    }

    const {lineNumber, column} = editorInstanceRef.current.getPosition();
    const {keyCode, ctrlKey, metaKey} = event;

    if (
      !interviewDetails.isDemo &&
      (keyCode === 33 || keyCode === 52) &&
      (metaKey || ctrlKey)
    ) {
      event.preventDefault();
    }

    const timestamp = getTimeStamp();

    switch (keyCode) {
      case 1:
        if (ctrlKey || metaKey)
          keyDataRef.current.push([
            timestamp,
            'ctrl_backspace',
            lineNumber,
            column,
          ]);
        else
          keyDataRef.current.push([timestamp, 'backspace', lineNumber, column]);
        break;
      case 3:
        keyDataRef.current.push([timestamp, 'enter', lineNumber, column]);
        break;
      case 15:
        keyDataRef.current.push([timestamp, 'left_arrow', lineNumber, column]);
        break;
      case 16:
        keyDataRef.current.push([timestamp, 'up_arrow', lineNumber, column]);
        break;
      case 17:
        keyDataRef.current.push([timestamp, 'right_arrow', lineNumber, column]);
        break;
      case 18:
        keyDataRef.current.push([timestamp, 'down_arrow', lineNumber, column]);
        break;
      case 20:
        if (ctrlKey || metaKey)
          keyDataRef.current.push([
            timestamp,
            'ctrl_delete',
            lineNumber,
            column,
          ]);
        else keyDataRef.current.push([timestamp, 'delete', lineNumber, column]);
        break;
      case 31:
        if (ctrlKey || metaKey)
          keyDataRef.current.push([timestamp, 'ctrl_a', lineNumber, column]);
        break;
      case 54:
        if (ctrlKey || metaKey)
          keyDataRef.current.push([timestamp, 'ctrl_x', lineNumber, column]);
        break;
      case 55:
        if (ctrlKey || metaKey)
          keyDataRef.current.push([timestamp, 'ctrl_y', lineNumber, column]);
        break;
      case 56:
        if (ctrlKey || metaKey)
          keyDataRef.current.push([timestamp, 'ctrl_z', lineNumber, column]);
        break;
      default:
        inputCountRef.current.key_count += 1;
        break;
    }
  };

  useEffect(() => {
    const initMonaco = async () => {
      const monaco = await import('monaco-editor/esm/vs/editor/editor.api');
      monacoRef.current = monaco;

      editorInstanceRef.current = monaco.editor.create(editorRef.current, {
        value: filesData.current[selectedFile]?.content || '',
        language:
          languageAliases[selectedFile?.split('.').pop()] || 'plaintext',
        minimap: {enabled: false},
        fontSize: 14,
        lineNumbers: 'on',
        scrollBeyondLastLine: true,
        automaticLayout: true,
        contextmenu: false,
        bracketPairColorization: {
          enabled: true,
          independentColorPoolPerBracketType: true,
        },
        stickyScroll: {
          enabled: false,
        },
        scrollbar: {
          vertical: 'visible',
          horizontal: 'visible',
          verticalScrollbarSize: 7,
          horizontalScrollbarSize: 7,
          useShadows: false,
        },
      });
      editorInstanceRef.current.addCommand(
        monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyX,
        () => {
          const selection = editorInstanceRef.current.getSelection();
          const model = editorInstanceRef.current.getModel();
          if (selection && model) {
            const selectedText = model.getValueInRange(selection);
            if (selectedText) {
              const encodedText = btoa(selectedText);
              const internalText = `__internal_copy__${encodedText}`;
              navigator.clipboard
                .writeText(internalText)
                .catch(err => console.error('Cut error:', err));

              editorInstanceRef.current.executeEdits('', [
                {
                  range: selection,
                  text: '',
                },
              ]);

              console.log('Cut operation executed');
            }
          }
        },
      );

      editorInstanceRef.current.addCommand(
        monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyC,
        () => {
          const selection = editorInstanceRef.current.getSelection();
          const model = editorInstanceRef.current.getModel();
          if (selection && model) {
            const selectedText = model.getValueInRange(selection);
            if (selectedText) {
              const encodedText = btoa(selectedText);
              const internalText = `__internal_copy__${encodedText}`;
              navigator.clipboard
                .writeText(internalText)
                .catch(err => console.error('Copy error:', err));

              console.log('Copy operation executed');
            }
          }
        },
      );

      editorInstanceRef.current.addCommand(
        monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyV,
        () => {
          navigator.clipboard
            .readText()
            .then(clipText => {
              if (
                interviewDetails.isDemo ||
                clipText.startsWith('__internal_copy__')
              ) {
                const cleanText = getCleanTextOnPaste(clipText);

                const selection = editorInstanceRef.current.getSelection();
                if (selection) {
                  editorInstanceRef.current.executeEdits('paste-operation', [
                    {
                      range: selection,
                      text: cleanText,
                    },
                  ]);
                  console.log('Paste operation executed');
                }
              } else {
                toast({
                  variant: 'destructive',
                  title: 'Warning',
                  description: 'Pasting external content is not allowed.',
                });
              }
            })
            .catch(err => console.error('Paste error:', err));
        },
      );

      monaco.editor.defineTheme('evalai-theme', {
        base: 'vs-dark',
        inherit: true,
        rules: [],
        colors: {
          'editor.background': '#252525',
        },
      });

      monaco.editor.setTheme('evalai-theme');

      editorInstanceRef.current.onKeyDown(handleKeyDown);
      editorInstanceRef.current.onMouseDown(handleMouseDown);
      editorInstanceRef.current.onDidScrollChange(handleScroll);

      setIsEditorReady(true);

      return () => {
        if (editorInstanceRef.current) {
          handleScroll.cancel();
          editorInstanceRef.current.dispose();
          editorInstanceRef.current = null;
          setIsEditorReady(false);
        }
      };
    };

    initMonaco();
  }, []);

  useEffect(() => {
    if (isEditorReady && editorInstanceRef.current && selectedFile) {
      const content = filesData.current[selectedFile]?.content || '';
      editorInstanceRef.current.setValue(content);

      // Store original conflict markers
      let originalConflictLines = [];

      // Regex patterns for conflict markers
      const startMarkerRegex = /<<<<<<\s+question-\w+/;
      const endMarkerRegex = />>>>>>\s+question-\w+/;

      // Get message controller to show inline messages
      const messageController = editorInstanceRef.current.getContribution(
        'editor.contrib.messageController',
      );

      // Function to find and track conflict markers
      const updateConflictMarkers = () => {
        const model = editorInstanceRef.current?.getModel();
        if (!model) return [];

        const markers = [];
        const lineCount = model.getLineCount();

        for (let lineNumber = 1; lineNumber <= lineCount; lineNumber++) {
          const lineContent = model.getLineContent(lineNumber);
          if (
            startMarkerRegex.test(lineContent) ||
            endMarkerRegex.test(lineContent)
          ) {
            markers.push({
              lineNumber,
              content: lineContent,
              isStartMarker: startMarkerRegex.test(lineContent),
              isEndMarker: endMarkerRegex.test(lineContent),
            });
          }
        }

        originalConflictLines = markers;
        return markers;
      };

      // Initial tracking
      updateConflictMarkers();

      const model = editorInstanceRef.current.getModel();
      const language =
        languageAliases[selectedFile?.split('.').pop()] || 'plaintext';
      if (model && monacoRef.current) {
        monacoRef.current.editor.setModelLanguage(model, language);
      }

      let isUndoing = false;

      // Show read-only message at current cursor position
      const showReadOnlyMessage = message => {
        if (messageController && editorInstanceRef.current) {
          const position = editorInstanceRef.current.getPosition();
          if (position) {
            messageController.showMessage(message, position);
          }
        }
      };

      // Register event handler for read-only edit attempts
      const readOnlyDisposable =
        editorInstanceRef.current.onDidAttemptReadOnlyEdit(() => {
          showReadOnlyMessage('This line is read-only.');
        });

      const disposable = editorInstanceRef.current.onDidChangeModelContent(
        event => {
          // Skip if we're in the middle of an undo operation
          if (isUndoing) return;

          const currentModel = editorInstanceRef.current.getModel();
          if (!currentModel) return;

          // Get current state of conflict markers
          const currentMarkers = [];
          const lineCount = currentModel.getLineCount();

          for (let lineNumber = 1; lineNumber <= lineCount; lineNumber++) {
            const lineContent = currentModel.getLineContent(lineNumber);
            if (
              startMarkerRegex.test(lineContent) ||
              endMarkerRegex.test(lineContent)
            ) {
              currentMarkers.push({
                lineNumber,
                content: lineContent,
                isStartMarker: startMarkerRegex.test(lineContent),
                isEndMarker: endMarkerRegex.test(lineContent),
              });
            }
          }

          // Store current position for message
          const position = editorInstanceRef.current.getPosition();

          // Check if number of conflict markers changed
          if (originalConflictLines.length !== currentMarkers.length) {
            isUndoing = true;
            editorInstanceRef.current.trigger('keyboard', 'undo', null);
            isUndoing = false;

            // Show inline message instead of toast
            if (position) {
              showReadOnlyMessage('This line is read-only.');
            }

            // Refresh markers after undo
            updateConflictMarkers();
            return;
          }

          // Check if any marker content was modified (exact content comparison)
          let markerContentModified = false;

          for (let i = 0; i < originalConflictLines.length; i++) {
            const original = originalConflictLines[i];
            const current = currentMarkers[i];

            // Compare entire line content - must be exactly the same
            if (original.content !== current.content) {
              markerContentModified = true;
              break;
            }
          }

          if (markerContentModified) {
            isUndoing = true;
            editorInstanceRef.current.trigger('keyboard', 'undo', null);
            isUndoing = false;

            // Show inline message instead of toast
            if (position) {
              showReadOnlyMessage('Modifying conflict markers is not allowed.');
            }

            // Refresh markers after undo
            updateConflictMarkers();
            return;
          }

          // Update marker tracking with new positions
          updateConflictMarkers();

          // Process normal edits
          handleOperations(
            filesData,
            2,
            selectedFile,
            editorInstanceRef.current.getValue(),
          );
        },
      );

      return () => {
        disposable.dispose();
        readOnlyDisposable.dispose();
      };
    }
  }, [selectedFile, isEditorReady]);

  useEffect(() => {
    if (editorInstanceRef.current) {
      editorInstanceRef.current.updateOptions({
        readOnly: disableEditor,
      });
    }
  }, [disableEditor]);

  return (
    <>
      <div ref={editorRef} style={{width: '100%', height: '100%'}} />
      {!selectedFile && (
        <div
          className={`absolute inset-0 z-50 flex items-center justify-center bg-black ${
            disableEditor ? 'text-neutral-400' : ''
          }`}>
          <p>${interviewState === interviewStates.DSA_CODING_INTERVIEW ? `Loading File` : `Select a File to Edit.`}</p>
        </div>
      )}
    </>
  );
};

export default dynamic(() => Promise.resolve(Editor), {ssr: false});
