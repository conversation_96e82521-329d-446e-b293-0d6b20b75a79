import { CodeRoom } from '@/provider/CoderoomProvider';
import { Interview } from '@/provider/InterviewProvider';
import { useEffect, useRef } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { getCleanTextOnPaste } from '@/lib/utilities/handlePaste';

const TextArea = () => {
  const { toast } = useToast();
  const { selectedFile, filesData, handleOperations, disableEditor } = CodeRoom();
  const { interviewDetails } = Interview();
  const textAreaRef = useRef(null);

  const handleTextAreaChange = (e) => {
    const newContent = e.target.value;
    handleOperations(filesData, 2, selectedFile, newContent);
  };

  const handleCopy = (e) => {
    const selectedText = window.getSelection().toString();
    if (selectedText) {
      const encodedText = btoa(selectedText);
      const internalText = `__internal_copy__${encodedText}`;
      navigator.clipboard.writeText(internalText).catch((err) =>
        console.error('Copy error:', err)
      );
    }
    e.preventDefault();
  };

  const handleCut = (e) => {
    const selectedText = window.getSelection().toString();
    if (selectedText) {
      const encodedText = btoa(selectedText);
      const internalText = `__internal_copy__${encodedText}`;
      navigator.clipboard.writeText(internalText).catch((err) =>
        console.error('Cut error:', err)
      );

      const currentText = textAreaRef.current.value;
      const newText =
        currentText.slice(0, textAreaRef.current.selectionStart) +
        currentText.slice(textAreaRef.current.selectionEnd);

      textAreaRef.current.value = newText;
      handleTextAreaChange({ target: { value: newText } });
    }
    e.preventDefault();
  };

  const handlePaste = (e) => {
    e.preventDefault();
    navigator.clipboard.readText().then((clipText) => {
      if (interviewDetails.isDemo || clipText.startsWith('__internal_copy__')) {
        const cleanText = getCleanTextOnPaste(clipText);

        const currentText = textAreaRef.current.value;
        const cursorPos = textAreaRef.current.selectionStart;

        const newText = currentText.slice(0, cursorPos) + cleanText + currentText.slice(cursorPos);
        textAreaRef.current.value = newText;
        handleTextAreaChange({ target: { value: newText } });
      } else {
        toast({
          variant: 'destructive',
          title: 'Warning',
          description: 'Pasting external content is not allowed.',
        });
      }
    }).catch((err) => console.error('Paste error:', err));
  };

  useEffect(() => {
    if (selectedFile) {
      textAreaRef.current.value =
        filesData.current[selectedFile]?.content || '';
    }
  }, [selectedFile]);

  return selectedFile ? (
    <textarea
      ref={textAreaRef}
      disabled={disableEditor}
      className="custom-textarea h-full w-full bg-gray-900 p-2 text-white outline-none focus:outline-none"
      onChange={handleTextAreaChange}
      onCopy={handleCopy}
      onCut={handleCut}
      onPaste={handlePaste}
      autoFocus
    />
  ) : (
    <div className={`flex h-full w-full items-center justify-center ${disableEditor ? 'text-neutral-400' : ''}`}>
      <p>Select a File to Edit.</p>
    </div>
  );
};

export default TextArea;
