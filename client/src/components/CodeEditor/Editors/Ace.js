import React, { useEffect, useRef, useState } from 'react';
import AceEditor from 'react-ace';
import 'ace-builds/src-noconflict/theme-monokai';
import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/mode-python';
import 'ace-builds/src-noconflict/mode-java';
import 'ace-builds/src-noconflict/ext-language_tools';
import { CodeRoom } from '@/provider/CoderoomProvider';
import { Interview } from '@/provider/InterviewProvider';
import { languageAliases } from '@/values/languageAliases';
import { getTimeStamp } from '@/lib/utilities/CodeRoom/getTime';
import { useToast } from '@/components/ui/use-toast';
import { getCleanTextOnPaste } from "@/lib/utilities/handlePaste";


function AceEditorComp() {
  const {
    selectedFile,
    filesData,
    handleOperations,
    disableEditor,
    sendInputData,
    questionData,
  } = CodeRoom();
  const { interviewDetails } = Interview();
  const { toast } = useToast();
  const [editorMounted, setEditorMounted] = useState(false);
  const editorRef = useRef(null);
  const keyDataRef = useRef([]);
  const keyCountRef = useRef({
    start_time: getTimeStamp(),
    count: 0,
    question: null,
  });
  const intervalRef = useRef(null);
  const undoManagers = useRef(new Map());
  const previousFileRef = useRef(null);

  const language = languageAliases[selectedFile?.split('.').pop()];

  const startInterval = () => {
    intervalRef.current = setInterval(() => {
      sendInputData({
        keyData: keyDataRef.current,
        keyCount: keyCountRef.current,
      });
      keyDataRef.current = [];
      keyCountRef.current = {
        start_time: getTimeStamp(),
        count: 0,
        question: keyCountRef.current.question,
      };
    }, 5 * 1000);
  };

  useEffect(() => {
    if (!questionData) return;
    const questionNumber = questionData.questionNumber;
    if (keyCountRef.current.question !== questionNumber) {
      sendInputData({
        keyData: keyDataRef.current,
        keyCount: keyCountRef.current,
      });

      keyDataRef.current = [];
      keyCountRef.current = {
        start_time: getTimeStamp(),
        count: 0,
        question: questionNumber,
      };

      sendInputData({
        keyData: keyDataRef.current,
        keyCount: keyCountRef.current,
      });
      clearInterval(intervalRef.current);
      startInterval();
    }
  }, [questionData, sendInputData]);

  useEffect(() => {
    if (!editorMounted || !editorRef.current) return;

    const editor = editorRef.current.editor;

    editor.on('change', e => {
      const { row, column } = editor.getCursorPosition();
      const lineNumber = row + 1;

      switch (e.action) {
        case 'insert':
          if (e.lines.length > 1) {
            keyDataRef.current.push([
              getTimeStamp(),
              'enter',
              lineNumber,
              column,
            ]);
          } else {
            keyCountRef.current.count += 1;
          }
          break;
        case 'remove':
          keyDataRef.current.push([
            getTimeStamp(),
            'remove',
            lineNumber,
            column,
          ]);
          break;
        default:
          break;
      }
    });

    const mouseDownListener = () => {
      const { row, column } = editor.getCursorPosition();
      keyDataRef.current.push([getTimeStamp(), 'mouse_click', row + 1, column]);
    };

    const keyupListener = e => {
      const { keyCode } = e;
      const { row, column } = editor.getCursorPosition();

      switch (keyCode) {
        case 37:
          keyDataRef.current.push([
            getTimeStamp(),
            'left_arrow',
            row + 1,
            column,
          ]);
          break;
        case 38:
          keyDataRef.current.push([
            getTimeStamp(),
            'up_arrow',
            row + 1,
            column,
          ]);
          break;
        case 39:
          keyDataRef.current.push([
            getTimeStamp(),
            'right_arrow',
            row + 1,
            column,
          ]);
          break;
        case 40:
          keyDataRef.current.push([
            getTimeStamp(),
            'down_arrow',
            row + 1,
            column,
          ]);
          break;
        default:
          keyCountRef.current.count += 1;
      }
    };

    const contextMenuListener = event => {
      event.preventDefault();
    };

    editor.commands.addCommand({
      name: 'disableSave',
      bindKey: { win: 'Ctrl-S', mac: 'Command-S' },
      exec: () => {
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'ctrl_s', row + 1, column]);
      },
    });

    editor.container.addEventListener('mousedown', mouseDownListener);
    editor.container.addEventListener('keyup', keyupListener);
    editor.container.addEventListener('contextmenu', contextMenuListener);

    editor.commands.addCommand({
      name: 'undo',
      bindKey: { win: 'Ctrl-Z', mac: 'Command-Z' },
      exec: () => {
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'ctrl_z', row + 1, column]);
        editor.undo();
      },
    });

    editor.commands.addCommand({
      name: 'redo',
      bindKey: { win: 'Ctrl-Y', mac: 'Command-Y' },
      exec: () => {
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'ctrl_y', row + 1, column]);
        editor.redo();
      },
    });

    editor.commands.addCommand({
      name: 'backspace',
      bindKey: { win: 'Backspace', mac: 'Backspace' },
      exec: () => {
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'backspace', row + 1, column]);
        const session = editor.session;

        if (column === 0 && row > 0) {
          // At the start of the line and not the first line
          const prevLine = session.getLine(row - 1);
          const currentLine = session.getLine(row);

          // Remove the current line first
          session.remove({
            start: { row, column: 0 },
            end: { row: row + 1, column: 0 },
          });

          // Then set the content of the previous line
          session.replace(
            {
              start: { row: row - 1, column: prevLine.length },
              end: { row: row - 1, column: prevLine.length },
            },
            currentLine,
          );

          // Move the cursor to the end of the merged line
          editor.moveCursorTo(row - 1, prevLine.length);
        } else if (column > 0) {
          // Default backspace behavior: remove one character
          session.remove({
            start: { row, column: column - 1 },
            end: { row, column },
          });
        }
      },
    });

    editor.commands.addCommand({
      name: 'ctrlBackspace',
      bindKey: { win: 'Ctrl-Backspace', mac: 'Option-Backspace' },
      exec: () => {
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([
          getTimeStamp(),
          'ctrl_backspace',
          row + 1,
          column,
        ]);
      },
    });

    editor.commands.addCommand({
      name: 'delete',
      bindKey: { win: 'Delete', mac: 'Delete' },
      exec: () => {
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'delete', row + 1, column]);
      },
    });

    editor.commands.addCommand({
      name: 'copy',
      bindKey: { win: 'Ctrl-C', mac: 'Command-C' },
      exec: () => {
        if (!editorRef.current || !editorRef.current.editor) {
          return false;
        }
        const editor = editorRef.current.editor;
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'ctrl_c', row + 1, column]);

        const selectedText = editor.getSelectedText();
        if (selectedText) {
          const encodedText = btoa(selectedText);
          const internalCopyText = `__internal_copy__${encodedText}`;
          navigator.clipboard.writeText(internalCopyText).catch((err) => console.error('Copy error:', err));
        }
      },
    });

    editor.commands.addCommand({
      name: 'cut',
      bindKey: { win: 'Ctrl-X', mac: 'Command-X' },
      exec: () => {
        if (!editorRef.current || !editorRef.current.editor) {
          return false;
        }
        const editor = editorRef.current.editor;
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'ctrl_x', row + 1, column]);

        const selectedText = editor.getSelectedText();
        if (selectedText) {
          const encodedText = btoa(selectedText);
          const internalText = `__internal_copy__${encodedText}`;
          navigator.clipboard.writeText(internalText).catch((err) => console.error('Cut error:', err));
          editor.session.remove(editor.getSelection().getRange());
        }
      },
    });

    editor.commands.addCommand({
      name: 'paste',
      bindKey: { win: 'Ctrl-V', mac: 'Command-V' },
      exec: () => {
        const editor = editorRef.current.editor;
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'ctrl_v', row + 1, column]);

        navigator.clipboard
          .readText()
          .then((clipText) => {
            if (interviewDetails.isDemo || clipText.startsWith('__internal_copy__')) {
              const cleanText = getCleanTextOnPaste(clipText);
              editor.session.insert(editor.getCursorPosition(), cleanText);
            } else {
              console.warn('Pasting external content is disabled.');
              toast({
                variant: 'destructive',
                title: 'Warning',
                description: 'Pasting external content is not allowed.',
              });
            }
          })
          .catch((err) => console.error('Paste error:', err));
      },
    });



    editor.commands.addCommand({
      name: 'selectAll',
      bindKey: { win: 'Ctrl-A', mac: 'Command-A' },
      exec: () => {
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([getTimeStamp(), 'ctrl_a', row + 1, column]);
        editor.selectAll();
      },
    });

    editor.commands.addCommand({
      name: 'ctrlDelete',
      bindKey: { win: 'Ctrl-Delete', mac: 'Command-Delete' },
      exec: () => {
        const { row, column } = editor.getCursorPosition();
        keyDataRef.current.push([
          getTimeStamp(),
          'ctrl_delete',
          row + 1,
          column,
        ]);
        editor.remove('right');
      },
    });

    return () => {
      editor.container.removeEventListener('mousedown', mouseDownListener);
      editor.container.removeEventListener('keyup', keyupListener);
      editor.commands.removeCommand('undo');
      editor.commands.removeCommand('redo');
      editor.commands.removeCommand('backspace');
      editor.commands.removeCommand('ctrlBackspace');
      editor.commands.removeCommand('delete');
      editor.commands.removeCommand('copy');
      editor.commands.removeCommand('cut');
      editor.commands.removeCommand('paste');
      editor.commands.removeCommand('selectAll');
      editor.commands.removeCommand('ctrlDelete');
      editor.container.removeEventListener('contextmenu', contextMenuListener);
      editor.commands.removeCommand('disableSave');
    };
  }, [editorMounted, interviewDetails.isDemo]);

  useEffect(() => {
    if (editorMounted && selectedFile) {
      const editor = editorRef.current?.editor;
      if (!editor) return;

      const session = editor.getSession();
      if (previousFileRef.current) {
        const currentSession = editor.getSession();
        const currentUndoManager = currentSession.getUndoManager();

        const undoStack = currentUndoManager.$undoStack;
        if (undoStack && undoStack.length > 0) {
          const lastEntry = undoStack.pop();
        }
        undoManagers.current[previousFileRef.current] = currentUndoManager;
      }

      if (undoManagers.current[selectedFile]) {
        session.setUndoManager(undoManagers.current[selectedFile]);
      } else {
        const newUndoManager = new ace.UndoManager();
        session.setUndoManager(newUndoManager);
        undoManagers.current[selectedFile] = newUndoManager;
      }
      previousFileRef.current = selectedFile;
    }
  }, [selectedFile, filesData, editorMounted]);

  return selectedFile ? (
    <div className="relative h-full w-full">
      <AceEditor
        ref={editorRef}
        mode={language}
        theme="monokai"
        width="100%"
        height="100%"
        style={{ backgroundColor: '#252525' }}
        value={filesData.current[selectedFile]?.content}
        onChange={val => handleOperations(filesData, 2, selectedFile, val)}
        onLoad={() => setEditorMounted(true)}
        readOnly={disableEditor}
        showPrintMargin={false}
        setOptions={{
          enableBasicAutocompletion: true,
          enableLiveAutocompletion: true,
          enableSnippets: true,
          showLineNumbers: true,
          tabSize: 2,
          fontSize: 14,
          fontFamily: 'monospace',
          useWorker: false,
          scrollPastEnd: true,
        }}
      />
    </div>
  ) : (
    <div
      className={`flex h-full w-full items-center justify-center ${disableEditor ? 'text-neutral-400' : ''
        }`}>
      <p>Select a File to Edit.</p>
    </div>
  );
}

export default AceEditorComp;
