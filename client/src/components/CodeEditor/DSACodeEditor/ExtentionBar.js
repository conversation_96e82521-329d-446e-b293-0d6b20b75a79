import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CodeRoom } from "@/provider/CoderoomProvider";
import { LANGUAGE_EXTENSIONS } from "@/values/LanguageExtentions";
import { FileText } from "lucide-react";

const ExtentionBar = ({ fileName, setFileName }) => {
  const { files, handleFileSelect, handleRename } = CodeRoom();

  const handleExtensionChange = async (newExtension) => {
    const currentPath = `/${files.name}/${fileName}`;
    const newFileName = `code.${newExtension}`;
    const newPath = `/${files.name}/${newFileName}`;

    try {
      const { success, newFiles: newFilesAfterRename } = await handleRename(
        currentPath,
        newFileName,
        fileName,
        true,
      );
      if (success) {
        await handleFileSelect(newPath, newFilesAfterRename, false, false);
        setFileName(newFileName);
      }
    } catch (error) {
      console.error("Error changing file extension:", error);
    }
  };

  return (
    <div className="flex items-center justify-between px-4 py-1.5 bg-neutral-900/95 border-b border-neutral-700/60 backdrop-blur-md">
      <div className="flex items-center space-x-3">
        <div className="p-1.5 bg-neutral-800/60 rounded-lg border border-neutral-700/50">
          <FileText size={14} className="text-neutral-400" />
        </div>
        <span className="text-sm font-medium text-neutral-200">
          {fileName}
        </span>
      </div>

      <Select value={fileName.split(".").pop()} onValueChange={handleExtensionChange}>
        <SelectTrigger className="w-[140px] h-8 bg-neutral-800/60 border border-neutral-600/50 text-neutral-200 text-xs focus:ring-blue-500/50 focus:border-blue-400/60 hover:bg-neutral-700/60 transition-all duration-200 backdrop-blur-sm">
          <SelectValue placeholder="Extension" />
        </SelectTrigger>
        <SelectContent className="bg-neutral-800/95 border border-neutral-600/50 backdrop-blur-md">
          {LANGUAGE_EXTENSIONS.map((ext) => (
            <SelectItem
              key={ext.value}
              value={ext.value}
              className="text-neutral-200 hover:bg-neutral-700/60 focus:bg-neutral-700/80 focus:text-white text-xs transition-colors duration-200"
            >
              {ext.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

export default ExtentionBar