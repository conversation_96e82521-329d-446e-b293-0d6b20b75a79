import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, ZoomIn, ZoomOut, RotateCcw, FileText, ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';

const SVGViewer = ({ svgData, isOpen, onClose, problemStatement }) => {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const [isQuestionExpanded, setIsQuestionExpanded] = useState(false);
  const svgContainerRef = useRef(null);
  const svgRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      setScale(1);
      setPosition({ x: 0, y: 0 });
      setIsQuestionExpanded(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (svgData && svgRef.current) {
      const svgElement = svgRef.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.width = '100%';
        svgElement.style.height = 'auto';
        svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');
        
        if (!svgElement.getAttribute('viewBox')) {
          const width = svgElement.getAttribute('width') || svgElement.clientWidth;
          const height = svgElement.getAttribute('height') || svgElement.clientHeight;
          svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
        }
      }
    }
  }, [svgData, isOpen]);

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev * 1.2, 5));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev / 1.2, 0.1));
  };

  const handleReset = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    const deltaX = e.clientX - lastMousePos.x;
    const deltaY = e.clientY - lastMousePos.y;
    
    setPosition(prev => ({
      x: prev.x + deltaX,
      y: prev.y + deltaY
    }));
    
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleWheel = (e) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setScale(prev => Math.min(Math.max(prev * delta, 0.1), 5));
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/65 backdrop-blur-sm"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        exit={{ scale: 0.9 }}
        className="relative w-[95vw] h-[95vh] bg-neutral-900/95 border border-neutral-700/80 rounded-xl backdrop-blur-md shadow-2xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="border-b border-neutral-700/60 bg-gradient-to-r from-purple-500/15 to-indigo-500/15">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-500/25 rounded-lg border border-purple-400/30 shadow-lg">
                <FileText size={16} className="text-purple-200" />
              </div>
              <h2 className="text-base font-bold text-white">Problem Visualization</h2>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleZoomOut}
                className="p-2 hover:bg-neutral-800/60 rounded-lg transition-all duration-200 text-neutral-300 hover:text-white border border-transparent hover:border-neutral-600/50"
                title="Zoom Out"
              >
                <ZoomOut size={16} />
              </button>
              <span className="text-xs text-neutral-400 min-w-[50px] text-center bg-neutral-800/60 border border-neutral-700/50 px-3 py-1.5 rounded-lg font-mono">
                {Math.round(scale * 100)}%
              </span>
              <button
                onClick={handleZoomIn}
                className="p-2 hover:bg-neutral-800/60 rounded-lg transition-all duration-200 text-neutral-300 hover:text-white border border-transparent hover:border-neutral-600/50"
                title="Zoom In"
              >
                <ZoomIn size={16} />
              </button>
              <button
                onClick={handleReset}
                className="p-2 hover:bg-neutral-800/60 rounded-lg transition-all duration-200 text-neutral-300 hover:text-white border border-transparent hover:border-neutral-600/50"
                title="Reset View"
              >
                <RotateCcw size={16} />
              </button>
              <div className="w-px h-5 bg-neutral-600/50 mx-1"></div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-red-500/20 rounded-lg transition-all duration-200 text-neutral-300 hover:text-red-300 border border-transparent hover:border-red-500/40"
                title="Close"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        </div>
        <div className="border-b border-neutral-700/60 bg-neutral-900/40">
          <button
            onClick={() => setIsQuestionExpanded(!isQuestionExpanded)}
            className="w-full flex items-center justify-between p-3 hover:bg-neutral-800/30 transition-all duration-200"
          >
            <div className="flex items-center space-x-3">
              <div className="p-1.5 bg-indigo-500/25 rounded-lg border border-indigo-400/30">
                <HelpCircle size={14} className="text-indigo-200" />
              </div>
              <span className="text-sm font-medium text-neutral-200">Click to View Question</span>
            </div>
            <div className="text-neutral-400 hover:text-neutral-200 transition-colors duration-200">
              {isQuestionExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </div>
          </button>
          <motion.div
            initial={false}
            animate={{
              height: isQuestionExpanded ? 'auto' : 0,
              opacity: isQuestionExpanded ? 1 : 0
            }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="px-4 pb-4">
              <div className="bg-neutral-800/40 rounded-lg p-4 border border-neutral-700/50 backdrop-blur-sm">
                <p className="text-neutral-200 leading-relaxed text-sm whitespace-pre-wrap">
                  {problemStatement || "Loading problem statement..."}
                </p>
              </div>
            </div>
          </motion.div>
        </div>
        <div className="flex flex-col h-[calc(100%-120px)]">
          <div
            ref={svgContainerRef}
            className="flex-1 overflow-hidden relative bg-neutral-900"
            style={{ 
              cursor: isDragging ? 'grabbing' : 'grab'
            }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onWheel={handleWheel}
          >
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{
                transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                transformOrigin: 'center center',
                transition: isDragging ? 'none' : 'transform 0.1s ease-out'
              }}
            >
              <div
                ref={svgRef}
                className="bg-white rounded-lg shadow-lg p-4"
                dangerouslySetInnerHTML={svgData}
              />
            </div>
          </div>
        </div>
        <div className="absolute bottom-3 left-3 bg-neutral-800/95 text-neutral-200 px-3 py-2 rounded-lg text-xs border border-neutral-700/50 backdrop-blur-sm">
          <div className="flex items-center gap-2">
            <span>🖱️ Drag • Scroll to zoom</span>
          </div>
        </div>
        <div className="absolute bottom-3 right-3 bg-neutral-800/95 text-neutral-200 px-3 py-2 rounded-lg text-xs border border-neutral-700/50 backdrop-blur-sm">
          <span className="font-mono">{Math.round(scale * 100)}%</span>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default SVGViewer;