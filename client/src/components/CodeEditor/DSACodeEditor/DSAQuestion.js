import { CodeRoom } from '@/provider/CoderoomProvider';
import { useEffect, useRef, useState } from 'react';
import { Maximize2, FileText, PlayCircle, Clock, AlertTriangle, Code2, Target } from 'lucide-react';
import { DSAFilePaths } from "@/values/DSAFilePaths";
import SVGViewer from './SVGViewer';

const DSAQuestion = () => {
  const { questionData, files, getSvgContent } = CodeRoom();
  const { question, questionNumber, totalQuestions, exampleInputOutput = "", expectedComplexity = "", constraints = "" } = questionData;

  const [svgData, setSvgData] = useState(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const svgContainerRef = useRef(null);

  useEffect(() => {
    const fetchInitialData = async () => {
      const svgContent = await getSvgContent(`/${files.name}/${DSAFilePaths["svg"]}`)
      setSvgData({ __html: svgContent });
    }
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (svgData && svgContainerRef.current) {
      const svgElement = svgContainerRef.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.width = '100%';
        svgElement.style.height = 'auto';
        svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');

        if (!svgElement.getAttribute('viewBox')) {
          const width = svgElement.getAttribute('width') || svgElement.clientWidth;
          const height = svgElement.getAttribute('height') || svgElement.clientHeight;
          svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
        }
      }
    }
  }, [svgData]);

  const handleFullscreen = () => {
    setIsViewerOpen(true);
  };

  return (
    <div className="h-full bg-black text-white">
      <div className="h-full overflow-y-auto no-scrollbar scrollbar-track-neutral-800/50 scrollbar-thumb-neutral-600/70">
        <div className="space-y-4 p-4">
          {question && (
            <div className="bg-neutral-900/90 border border-neutral-700/60 rounded-lg overflow-hidden backdrop-blur-sm">
              <div className="border-b border-neutral-700/60 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
                <div className="flex items-center justify-between p-3">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-3">
                      <span className="text-xs font-semibold text-white bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent border border-blue-500/30 bg-blue-500/10 px-2 py-1 rounded-md backdrop-blur-sm">
                        Problem {questionNumber} of {totalQuestions}
                      </span>
                    </div>
                    <div className="h-4 w-px bg-gradient-to-b from-neutral-600 to-neutral-800"></div>
                    <span className="text-xs text-neutral-300 font-medium bg-neutral-800/50 px-2 py-1 rounded-md border border-neutral-700/50">Data Structures & Algorithms</span>
                  </div>
                </div>
              </div>
              <div className="p-3">
                <div className="prose prose-invert max-w-none">
                  <p className="text-sm text-white leading-relaxed font-medium whitespace-pre-wrap mb-0">
                    {question}
                  </p>
                </div>
              </div>
            </div>
          )}
          <div className="bg-neutral-900/90 border border-neutral-700/60 rounded-lg overflow-hidden backdrop-blur-sm">
            <div className="border-b border-neutral-700/60 bg-gradient-to-r from-purple-500/10 to-blue-500/10">
              <div className="flex items-center justify-between p-3">
                <div className="flex items-center space-x-2">
                  <div className="p-1 bg-purple-500/20 rounded-md border border-purple-500/30">
                    <FileText size={14} className="text-purple-300" />
                  </div>
                  <div>
                    <h3 className="text-sm font-bold text-white">Problem Visualization</h3>
                  </div>
                </div>
                {svgData && (
                  <button
                    onClick={handleFullscreen}
                    className="group flex items-center space-x-1.5 px-2.5 py-1 text-xs font-medium text-blue-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 border border-blue-500/40 hover:border-blue-400 rounded-md transition-all duration-300 backdrop-blur-sm"
                  >
                    <Maximize2 size={12} className="group-hover:scale-110 transition-transform duration-200" />
                    <span>View Fullscreen</span>
                  </button>
                )}
              </div>
            </div>
            <div className="p-3">
              {svgData ? (
                <div className="bg-white/95 rounded-lg overflow-hidden border border-neutral-600/30 shadow-inner backdrop-blur-sm">
                  <div
                    ref={svgContainerRef}
                    dangerouslySetInnerHTML={svgData}
                    className="w-full"
                    style={{ maxWidth: '100%', overflow: 'hidden' }}
                  />
                </div>
              ) : (
                <div className="bg-neutral-800/60 rounded-lg p-4 text-center border border-neutral-700/50 backdrop-blur-sm">
                  <div className="relative mx-auto mb-2 w-5 h-5">
                    <div className="absolute inset-0 border-2 border-blue-500/30 rounded-full"></div>
                    <div className="absolute inset-0 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                  <p className="text-neutral-300 text-xs font-medium">Loading visualization...</p>
                </div>
              )}
            </div>
          </div>
          {expectedComplexity && (expectedComplexity.time || expectedComplexity.space) && (
            <div className="bg-neutral-900/90 border border-neutral-700/60 rounded-lg overflow-hidden backdrop-blur-sm">
              <div className="border-b border-neutral-700/60 bg-gradient-to-r from-amber-500/10 to-orange-500/10">
                <div className="flex items-center space-x-2 p-3">
                  <div className="p-1 bg-amber-500/20 rounded-md border border-amber-500/30">
                    <Target size={14} className="text-amber-300" />
                  </div>
                  <div>
                    <h3 className="text-sm font-bold text-white">Target Complexity</h3>
                  </div>
                </div>
              </div>
              <div className="p-3">
                <div className="mb-4 p-4 bg-gradient-to-r from-blue-500/15 to-purple-500/15 border border-blue-500/40 rounded-lg backdrop-blur-sm">
                  <div className="flex items-start space-x-3">
                    <div className="p-1.5 bg-blue-500/20 rounded-lg border border-blue-500/30">
                      <span className="text-blue-300 text-sm">💡</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-bold text-blue-200 mb-3">Scoring Rules</h4>
                      <div className="space-y-2.5">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <span className="text-xs text-blue-300/90">
                            You can move to next question with any <span className="font-bold text-green-400">correct solution</span>
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                          <span className="text-xs text-blue-300/90">
                            For <span className="font-bold text-amber-300">maximum points</span>, focus on meeting the complexity targets below
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {expectedComplexity.time && (
                    <div className="bg-neutral-800/60 border border-neutral-700/60 rounded-lg p-3 backdrop-blur-sm hover:bg-neutral-800/80 transition-colors duration-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <Clock size={14} className="text-amber-400" />
                        <span className="text-xs font-bold text-amber-400">Time Complexity</span>
                      </div>
                      <code className="text-amber-300 text-sm font-mono bg-neutral-900/80 px-3 py-2 rounded-md block border border-neutral-700/50 font-bold tracking-wide">
                        {expectedComplexity.time}
                      </code>
                    </div>
                  )}
                  {expectedComplexity.space && (
                    <div className="bg-neutral-800/60 border border-neutral-700/60 rounded-lg p-3 backdrop-blur-sm hover:bg-neutral-800/80 transition-colors duration-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <Code2 size={14} className="text-amber-400" />
                        <span className="text-xs font-bold text-amber-400">Space Complexity</span>
                      </div>
                      <code className="text-amber-300 text-sm font-mono bg-neutral-900/80 px-3 py-2 rounded-md block border border-neutral-700/50 font-bold tracking-wide">
                        {expectedComplexity.space}
                      </code>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          {constraints && constraints.length > 0 && (
            <div className="bg-neutral-900/90 border border-neutral-700/60 rounded-lg overflow-hidden backdrop-blur-sm">
              <div className="border-b border-neutral-700/60 bg-gradient-to-r from-purple-500/10 to-blue-500/10">
                <div className="flex items-center space-x-2 p-3">
                  <div className="p-1 bg-purple-500/20 rounded-md border border-purple-500/30">
                    <AlertTriangle size={14} className="text-purple-300" />
                  </div>
                  <div>
                    <h3 className="text-sm font-bold text-white">Constraints</h3>
                  </div>
                </div>
              </div>
              <div className="p-3">
                <ul className="space-y-2">
                  {constraints.map((constraint, index) => (
                    <li key={index} className="flex items-start space-x-2 text-xs text-neutral-300 hover:bg-neutral-800/40 p-1.5 rounded-md transition-colors">
                      <span className="text-purple-400 font-bold mt-0.5">•</span>
                      <span>{constraint}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
          {exampleInputOutput && exampleInputOutput.length > 0 && (
            <div className="bg-neutral-900/90 border border-neutral-700/60 rounded-lg overflow-hidden backdrop-blur-sm">
              <div className="border-b border-neutral-700/60 bg-gradient-to-r from-green-500/10 to-emerald-500/10">
                <div className="flex items-center space-x-2 p-3">
                  <div className="p-1 bg-green-500/20 rounded-md border border-green-500/30">
                    <PlayCircle size={14} className="text-green-300" />
                  </div>
                  <div>
                    <h3 className="text-sm font-bold text-white">Test Cases</h3>
                  </div>
                </div>
              </div>
              <div className="p-3">
                <div className="space-y-3">
                  {exampleInputOutput.map((example, index) => (
                    <div key={index} className="bg-neutral-800/40 border border-neutral-700/60 rounded-lg p-3 backdrop-blur-sm">
                      <div className="flex items-center space-x-1.5 mb-2">
                        <div className="px-2 py-0.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-md">
                          <span className="text-xs font-bold text-blue-300">Example {index + 1}</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-1.5">
                            <span className="text-xs font-bold text-blue-300 min-w-[45px]">Input:</span>
                            <div className="h-px bg-gradient-to-r from-blue-500/50 to-transparent flex-1"></div>
                          </div>
                          <div className="bg-neutral-900/80 border border-neutral-700/60 rounded-md p-2.5 backdrop-blur-sm">
                            <pre className="text-blue-200 text-xs font-mono whitespace-pre-wrap break-words leading-relaxed">
                              {example.input}
                            </pre>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-1.5">
                            <span className="text-xs font-bold text-emerald-300 min-w-[45px]">Output:</span>
                            <div className="h-px bg-gradient-to-r from-emerald-500/50 to-transparent flex-1"></div>
                          </div>
                          <div className="bg-neutral-900/80 border border-neutral-700/60 rounded-md p-2.5 backdrop-blur-sm">
                            <pre className="text-emerald-200 text-xs font-mono whitespace-pre-wrap break-words leading-relaxed">
                              {example.output}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <SVGViewer
        svgData={svgData}
        isOpen={isViewerOpen}
        onClose={() => setIsViewerOpen(false)}
        problemStatement={question}
      />
    </div>
  );
};

export default DSAQuestion;