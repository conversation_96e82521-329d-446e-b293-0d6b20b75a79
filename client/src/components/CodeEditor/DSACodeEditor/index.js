import { useEditor } from '@/hooks/useEditor';
import { Interview } from '@/provider/InterviewProvider';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import { useEffect, useState } from 'react';
import { disableShortcuts } from '@/lib/utilities/disableShortcuts';
import DSAQuestion from './DSAQuestion';
import ExtentionBar from './ExtentionBar';
import { findCodeFile } from '@/lib/utilities/CodeRoom/directory';
import { DSAFilePaths } from '@/values/DSAFilePaths';
import { CodeRoom } from '@/provider/CoderoomProvider';
import { saveCodeRoomState } from '@/lib/utilities/codeRoomStatePersistence';

function DSACodeEditor() {
  const { interviewDetails } = Interview();
  const { RenderedEditor } = useEditor();
  const { files, handleFileSelect, filesData } = CodeRoom();

  const [fileName, setFileName] = useState(DSAFilePaths['coding']);

  useEffect(() => {
    if (interviewDetails?.isDemo) {
      return;
    }
    window.addEventListener('keydown', disableShortcuts);
    return () => {
      window.removeEventListener('keydown', disableShortcuts);
    };
  }, [interviewDetails?.isDemo]);

  useEffect(() => {
    const fetchInitialData = async () => {
      const actualCodeFile = findCodeFile(files);
      if (actualCodeFile) {
        await handleFileSelect(`/${files.name}/${actualCodeFile.name}`);
        setFileName(actualCodeFile.name);
      } else {
        const fileName = DSAFilePaths['coding'];
        const filePath = `/${files.name}/${fileName}`;

        filesData.current[filePath] = {
          content: '--------- Code Here ---------',
          change: 'create',
          type: 1,
          sentToServer: false,
          publishedToServer: false,
        };
        saveCodeRoomState(filesData.current);

        const newFiles = JSON.parse(JSON.stringify(files));

        const fileExists = newFiles.items.some(item =>
          item.name === fileName && item.type === 1
        );

        if (!fileExists) {
          console.log('Adding new file:', fileName);
          newFiles.items.unshift({
            name: fileName,
            type: 1,
            inFocus: true,
          });
        }

        await handleFileSelect(filePath, newFiles, false, false);
        setFileName(fileName);
      }
    }
    fetchInitialData();
  }, []);

  return (
    <div className="flex h-full w-full flex-col font-serif">
      <ResizablePanelGroup
        direction="horizontal"
        className="h-full w-full border-t font-serif">
        <ResizablePanel defaultSize={50} minSize={30}>
          <DSAQuestion />
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={50} minSize={30}>
          <div className="relative flex h-full w-full flex-col bg-black">
            <ExtentionBar fileName={fileName} setFileName={setFileName} />
            <div className="min-h-0 flex-1">
              {RenderedEditor && <RenderedEditor />}
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}

export default DSACodeEditor;
