'use client';

import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import {z} from 'zod';

import {Button} from '@/components/ui/button';
import {Form} from '@/components/ui/form';
import {useToast} from '@/components/ui/use-toast';
import {TextField} from '@/components/FormField';
import { reportError, submitAssignmentByGithubLink } from '@/lib/utilities/api';
import {trackEvent} from '@/lib/firebase/firebaseWrapper';
const {githubRepoUrlRegex} = require('@/values/assignment');

const assignmentSubmissionSchema = z.object({
  assignmentLink: z
    .string()
    .url()
    .refine(value => githubRepoUrlRegex.test(value), {
      message:
        'Invalid GitHub repository link. Make sure the URL is either the base repository or a valid path/branch within it.',
    }),
});

const GithubLink = ({props}) => {
  const {toast} = useToast();
  const form = useForm({
    resolver: zodResolver(assignmentSubmissionSchema),
    defaultValues: {
      assignmentLink: '',
    },
  });

  const onSubmit = formData => {
    props.setLoading(true);
    submitAssignmentByGithubLink(
      formData.assignmentLink,
      props.assignmentId,
      props.companyId,
    )
      .then(res => {
        trackEvent('SubmitAssignmentData', {
          assignmentLink: formData.assignmentLink,
          assignmentId: props.assignmentId,
        });
        props.setSubmissionStatus(true);
        toast({
          variant: 'success',
          description: 'Assignment submitted successfully.',
        });
      })
      .catch(error => {
        console.log('Error while submitting assignment:', error);
        trackEvent('submitAssignment', {
          err: {
            message: error.message || 'No error message available',
            stack: error.stack || 'No stack trace available',
          },
        });
        trackEvent('SubmitAssignmentDataOnError', {
          assignmentLink: formData.assignmentLink,
          assignmentId: props.assignmentId,
        });
        if (error.data?.code === 'alreadySubmitted')
          props.setSubmissionStatus(true);
        toast({
          variant: 'destructive',
          description:
            error.data?.message ||
            'Something went wrong. Could not submit assignment.',
        });
        reportError("SubmitAssignmentGithub", error, false);
      })
      .finally(() => {
        props.setLoading(false);
      });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-8">
        <TextField
          disabled={
            props.submissionStatus ||
            !props.assignmentDetails ||
            props.disabled ||
            props.loading
          }
          control={form.control}
          name="assignmentLink"
          label="Github Link"
          placeholder="Enter github link"
        />
        <Button
          disabled={
            props.submissionStatus ||
            props.disabled ||
            !props.assignmentDetails ||
            props.loading
          }
          type="submit">
          {props.submissionStatus
            ? 'Submitted'
            : props.loading
              ? 'Submitting...'
              : 'Submit'}
        </Button>
      </form>
    </Form>
  );
};

export default GithubLink;
