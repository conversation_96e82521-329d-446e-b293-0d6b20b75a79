'use client';

import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger} from '@/components/ui/tabs';
import GithubLink from './GithubLink';
import DirectoryPicker from './DirectoryPicker';

const SubmissionForm = props => {
  return (
    <div className="w-full px-4 md:pr-0">
      <h2 className="text-2xl font-bold text-primary">Choose a method</h2>
      <Tabs defaultValue="github-link" className="w-full">
        <TabsList className="flex w-full justify-between">
          <TabsTrigger
            value="github-link"
            className="w-full"
            disabled={props.loading}>
            Github Link
          </TabsTrigger>
          <TabsTrigger
            value="upload-directory"
            className="w-full"
            disabled={props.loading}>
            Upload Directory
          </TabsTrigger>
        </TabsList>

        <TabsContent value="github-link">
          <GithubLink props={props} />
        </TabsContent>

        <TabsContent value="upload-directory">
          <DirectoryPicker props={props} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SubmissionForm;
