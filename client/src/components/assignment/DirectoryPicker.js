'use client';

import {Button} from '@/components/ui/button';
import {useToast} from '@/components/ui/use-toast';
import {ToastAction} from '@/components/ui/toast';
import { reportError, submitAssignmentByDirectoryUpload } from '@/lib/utilities/api';
import React, {useState, useCallback} from 'react';
import {useDropzone} from 'react-dropzone';
import {FileIcon, Cross2Icon} from '@radix-ui/react-icons';
import {
  MAX_FILE_SIZE,
  MAX_FILE_COUNT,
  EXCLUDED_FOLDERS,
} from '@/values/assignment';
import Link from 'next/link';

const DirectoryPicker = ({props}) => {
  const {toast} = useToast();
  const [files, setFiles] = useState([]);
  const [totalSize, setTotalSize] = useState(0);

  const handleRemoveFile = useCallback(index => {
    setFiles(prevFiles => {
      const removedFile = prevFiles[index];
      const newFiles = prevFiles.filter((_, i) => i !== index);
      setTotalSize(prevSize => prevSize - removedFile.size / (1024 * 1024));
      return newFiles;
    });
  }, []);

  const handleSubmit = async () => {
    if (files.length === 0) {
      console.log('No files to upload');
      return;
    }
    props.setLoading(true);
    const controller = new AbortController();
    let progressToast;
    try {
      progressToast = toast({
        title: 'Please wait...',
        description: 'Uploading... 0%',
        duration: Infinity,
        action: (
          <ToastAction altText="Cancel" onClick={() => controller.abort()}>
            Cancel
          </ToastAction>
        ),
      });

      await submitAssignmentByDirectoryUpload(
        files,
        props.assignmentId,
        progress => {
          progressToast.update({
            description:
              progress == 100
                ? 'Upload Completed. Processing files...'
                : `Uploading... ${progress}%`,
          });
        },
        controller.signal,
        props.companyId,
      );

      toast({
        variant: 'success',
        title: 'Upload Completed',
        description: 'All files have been uploaded successfully.',
      });

      await new Promise(resolve => setTimeout(resolve, 2000));

      progressToast.dismiss();

      setFiles([]);
      setTotalSize(0);
      props.setSubmissionStatus(true);
    } catch (error) {
      progressToast?.dismiss();

      toast({
        variant: 'destructive',
        description:
          error.response?.data?.message ||
          'Something went wrong. Could not submit assignment.',
      });
      reportError("SubmitAssignmentDirectory", error, false);
    } finally {
      props.setLoading(false);
    }
  };

  const handleCancel = () => {
    setFiles([]);
    setTotalSize(0);
  };

  const getFileIdentifier = file => {
    return `${file.name}_${file.size}_${file.lastModified}`;
  };

  const handleDrop = useCallback(
    acceptedFiles => {
      setFiles(prevFiles => {
        const existingFileIds = new Set(prevFiles.map(getFileIdentifier));
        const newUniqueFiles = acceptedFiles.filter(
          file =>
            !existingFileIds.has(getFileIdentifier(file)) &&
            !EXCLUDED_FOLDERS.some(folder => file.path.includes(folder)),
        );

        if (newUniqueFiles.length === 0) {
          toast({
            variant: 'destructive',
            description:
              'No new files added. Files might be duplicates or from excluded folders.',
          });
          return prevFiles;
        }

        const hasNoFiles =
          prevFiles.length === 0 || prevFiles.every(file => file.type === '');
        const onlyZipFiles = acceptedFiles.every(file =>
          file.name.endsWith('.zip'),
        );

        if (hasNoFiles && onlyZipFiles) {
          toast({
            variant: 'destructive',
            description:
              'Please extract the zip file and select the files inside it.',
          });
          return prevFiles;
        }

        const totalNewFiles = prevFiles.length + newUniqueFiles.length;
        if (totalNewFiles > MAX_FILE_COUNT) {
          toast({
            variant: 'destructive',
            title: 'File count exceeds limit',
            description: `You can only upload a maximum of ${MAX_FILE_COUNT} files.`,
          });
          return prevFiles;
        }

        const newFiles = [...prevFiles, ...newUniqueFiles];
        const additionalSize = newUniqueFiles.reduce(
          (sum, file) => sum + file.size,
          0,
        );
        const newTotalSize = totalSize + additionalSize;

        if (newTotalSize > MAX_FILE_SIZE * 1024 * 1024) {
          toast({
            variant: 'destructive',
            title: 'File size exceeds limit',
            description: `File size exceeds limit of ${MAX_FILE_SIZE}MB`,
          });
          return prevFiles;
        }

        setTotalSize(newTotalSize / (1024 * 1024));
        return newFiles;
      });
    },
    [totalSize],
  );

  const {getRootProps, getInputProps} = useDropzone({onDrop: handleDrop});

  return (
    <div>
      <div
        style={{
          pointerEvents:
            !props.assignmentDetails ||
            props.disabled ||
            props.loading ||
            props.submissionStatus
              ? 'none'
              : 'auto',
        }}
        {...getRootProps({
          className:
            'flex flex-col gap-1 items-center justify-center border-2 border-dashed border-neutral-300 p-4 text-center text-neutral-500 hover:border-neutral-400 rounded-sm ',
        })}
        onClick={e => {
          e.stopPropagation();
          e.preventDefault();
        }}>
        <FileIcon className="h-16 w-16" />
        <p className="text-xl">Drag and drop your files here</p>

        <input {...getInputProps()} />
      </div>
      <div className="flex items-start justify-between pt-1 text-sm text-neutral-500">
        <p>
          <span className="font-medium">Note:</span> Certain folders are
          excluded from upload.{' '}
          <Link
            href="/help/assignment-submission"
            className="text-blue-700 hover:underline"
            target="_blank"
            rel="noopener noreferrer">
            Learn more
          </Link>
          .
        </p>
        <div className="flex flex-col items-end  gap-1">
          <p>
            <span className="font-medium">Size limit:</span>{' '}
            {totalSize.toFixed(2)} / {MAX_FILE_SIZE} MB
          </p>
          <p>
            <span className="font-medium">Count limit:</span> {files.length} /{' '}
            {MAX_FILE_COUNT}
          </p>
        </div>
      </div>

      {files.length > 0 && (
        <div>
          <h4 className="py-1 font-medium">Selected files:</h4>
          <ul>
            {files.map((file, index) => {
              const isGitFolder = file.path.includes('.git');
              return (
                !isGitFolder && (
                  <li key={index} className="flex justify-between py-1">
                    {file.path}{' '}
                    <Cross2Icon
                      className="h-4 w-4 text-neutral-500 hover:text-destructive"
                      onClick={() => handleRemoveFile(index)}
                    />
                  </li>
                )
              );
            })}
          </ul>
        </div>
      )}
      <div className="flex gap-2 py-4">
        <Button
          onClick={handleSubmit}
          disabled={
            files.length === 0 || props.loading || props.submissionStatus
          }>
          {props.submissionStatus ? 'Submitted' : 'Submit'}
        </Button>
        <Button
          disabled={
            files.length === 0 || props.loading || props.submissionStatus
          }
          onClick={handleCancel}
          className="bg-destructive hover:bg-destructive">
          Clear Selection
        </Button>
      </div>
    </div>
  );
};

export default DirectoryPicker;
