'use client';

import {useCountdown} from '@/hooks';
import {CodeRoom} from '@/provider/CoderoomProvider';
import {Interview} from '@/provider/InterviewProvider';
import {Tutorial} from '@/provider/TutorialProvider';
import interviewStates from '@/values/interviewStates';
import {tutorialSteps} from '@/values/tutorialSteps';
import {useEffect} from 'react';

const InterviewTimer = ({duration, onTimeup, triggers}) => {
  const {setTime, remainingTime, time} = useCountdown(onTimeup, triggers);
  const {tutorialAction} = CodeRoom();
  const {tutorialStep} = Tutorial();
  const {interviewState} = Interview();

  useEffect(() => {
    if (duration) setTime(duration);
  }, [duration]);

  return (
    <span
      className="relative mx-10"
      data-overlay={tutorialSteps.TIMER}
      data-step={tutorialSteps.TIMER}
      onClick={() => {
        if (
          interviewState === interviewStates.CODING_TUTORIAL &&
          tutorialStep === tutorialSteps.TIMER
        ) {
          tutorialAction();
        }
      }}>
      <p
        key={time}
        className="flex w-[120px] items-center font-mono tabular-nums">
        <span className="mr-1 inline-flex">Time Left: </span>
        <span className="inline-flex w-[2ch] justify-center">
          {remainingTime.minutes}
        </span>
        <span className="inline-flex w-[1ch] justify-center">:</span>
        <span className="inline-flex w-[2ch] justify-center">
          {remainingTime.seconds}
        </span>
      </p>
    </span>
  );
};

export default InterviewTimer;
