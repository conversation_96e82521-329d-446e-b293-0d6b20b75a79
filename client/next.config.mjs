/** @type {import('next').NextConfig} */

import MonacoWebpackPlugin from 'monaco-editor-webpack-plugin';
const monacoRules = [
  {
    test: /\.ttf$/,
    type: 'asset/resource',
  },
];

const nextConfig = {
  output: 'standalone',
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'evalai.neusort.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  webpack: (config, {isServer}) => {
    if (!isServer) {
      config.plugins.push(
        new MonacoWebpackPlugin({
          filename: 'static/[name].worker.js',
        }),
      );
      config.module.rules.push(...monacoRules);
    }

    return config;
  },
};

export default nextConfig;
