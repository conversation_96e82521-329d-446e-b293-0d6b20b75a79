[{"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (af-ZA, AdriNeural)", "privLocale": "af-ZA", "privShortName": "af-ZA-AdriNeural", "privLocaleName": "Afrikaans (South Africa)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "147"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (af-ZA, WillemNeural)", "privLocale": "af-ZA", "privShortName": "af-ZA-WillemNeural", "privLocaleName": "Afrikaans (South Africa)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (am-ET, MekdesNeural)", "privLocale": "am-ET", "privShortName": "am-ET-MekdesNeural", "privLocaleName": "Amharic (Ethiopia)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "መቅደስ", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "117"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (am-ET, AmehaNeural)", "privLocale": "am-ET", "privShortName": "am-ET-AmehaNeural", "privLocaleName": "Amharic (Ethiopia)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "አምሀ", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "112"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-AE, FatimaNeural)", "privLocale": "ar-AE", "privShortName": "ar-AE-FatimaNeural", "privLocaleName": "Arabic (United Arab Emirates)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "فاطمة", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "110"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-A<PERSON>, HamdanNeural)", "privLocale": "ar-AE", "privShortName": "ar-AE-HamdanNeural", "privLocaleName": "Arabic (United Arab Emirates)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "حمدان", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "115"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-BH, LailaNeural)", "privLocale": "ar-BH", "privShortName": "ar-BH-<PERSON><PERSON>eural", "privLocaleName": "Arabic (Bahrain)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "ليلى", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "108"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-BH, AliNeural)", "privLocale": "ar-BH", "privShortName": "ar-BH-AliNeural", "privLocaleName": "Arabic (Bahrain)", "privDisplayName": "<PERSON>", "privLocalName": "علي", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "121"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-DZ, AminaNeural)", "privLocale": "ar-DZ", "privShortName": "ar-DZ-AminaNeural", "privLocaleName": "Arabic (Algeria)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "أمينة", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "110"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-DZ, IsmaelNeural)", "privLocale": "ar-DZ", "privShortName": "ar-DZ-Ismael<PERSON>eural", "privLocaleName": "Arabic (Algeria)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "إسماعيل", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "115"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-EG, SalmaNeural)", "privLocale": "ar-EG", "privShortName": "ar-EG-SalmaNeural", "privLocaleName": "Arabic (Egypt)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "سلمى", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "103"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, ShakirNeural)", "privLocale": "ar-EG", "privShortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Arabic (Egypt)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "شاكر", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "115"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, RanaNeural)", "privLocale": "ar-IQ", "privShortName": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Arabic (Iraq)", "privDisplayName": "<PERSON>", "privLocalName": "رنا", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "98"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, BasselNeural)", "privLocale": "ar-IQ", "privShortName": "ar-IQ-BasselNeural", "privLocaleName": "Arabic (Iraq)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "باسل", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "113"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-J<PERSON>, SanaNeural)", "privLocale": "ar-JO", "privShortName": "ar-JO-SanaNeural", "privLocaleName": "Arabic (Jordan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "سناء", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "98"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-JO, TaimNeural)", "privLocale": "ar-JO", "privShortName": "ar-JO-TaimNeural", "privLocaleName": "Arabic (Jordan)", "privDisplayName": "Taim", "privLocalName": "تيم", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "113"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-K<PERSON>, NouraNeural)", "privLocale": "ar-KW", "privShortName": "ar-KW-NouraNeural", "privLocaleName": "Arabic (Kuwait)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "نورا", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "121"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-K<PERSON>, FahedNeural)", "privLocale": "ar-KW", "privShortName": "ar-KW-FahedNeural", "privLocaleName": "Arabic (Kuwait)", "privDisplayName": "<PERSON>ahed", "privLocalName": "ف<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "128"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-LB, LaylaNeural)", "privLocale": "ar-LB", "privShortName": "ar-LB-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Arabic (Lebanon)", "privDisplayName": "Layla", "privLocalName": "ليلى", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "99"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-LB, RamiNeural)", "privLocale": "ar-LB", "privShortName": "ar-LB-RamiNeural", "privLocaleName": "Arabic (Lebanon)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "رامي", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "101"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-L<PERSON>, ImanNeural)", "privLocale": "ar-LY", "privShortName": "ar-LY-ImanNeural", "privLocaleName": "Arabic (Libya)", "privDisplayName": "Iman", "privLocalName": "إيمان", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "108"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (a<PERSON><PERSON><PERSON><PERSON>, OmarNeural)", "privLocale": "ar-LY", "privShortName": "ar-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Arabic (Libya)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON><PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "111"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-MA, MounaNeural)", "privLocale": "ar-<PERSON>", "privShortName": "ar-<PERSON><PERSON><PERSON><PERSON>N<PERSON><PERSON>", "privLocaleName": "Arabic (Morocco)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "من<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "121"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON>, Jamal<PERSON><PERSON>)", "privLocale": "ar-<PERSON>", "privShortName": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Arabic (Morocco)", "privDisplayName": "<PERSON>", "privLocalName": "جمال", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "128"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-OM, AyshaNeural)", "privLocale": "ar-OM", "privShortName": "ar-OM-AyshaNeural", "privLocaleName": "Arabic (Oman)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "عائشة", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "118"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar<PERSON><PERSON><PERSON>, AbdullahNeural)", "privLocale": "ar-OM", "privShortName": "ar-O<PERSON><PERSON>Abdullah<PERSON><PERSON><PERSON>", "privLocaleName": "Arabic (Oman)", "privDisplayName": "<PERSON>", "privLocalName": "عبدا<PERSON><PERSON>ه", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "123"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-QA, AmalNeural)", "privLocale": "ar-QA", "privShortName": "ar-QA-AmalNeural", "privLocaleName": "Arabic (Qatar)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "113"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-QA, MoazNeural)", "privLocale": "ar-QA", "privShortName": "ar-QA-MoazNeural", "privLocaleName": "Arabic (Qatar)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "معاذ", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-SA, ZariyahNeural)", "privLocale": "ar-SA", "privShortName": "ar-SA-ZariyahNeural", "privLocaleName": "Arabic (Saudi Arabia)", "privDisplayName": "Zariyah", "privLocalName": "زارية", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "105"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-SA, HamedNeural)", "privLocale": "ar-SA", "privShortName": "ar-SA-HamedNeural", "privLocaleName": "Arabic (Saudi Arabia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "107"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-S<PERSON>, AmanyNeural)", "privLocale": "ar-SY", "privShortName": "ar-SY-AmanyNeural", "privLocaleName": "Arabic (Syria)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "أ<PERSON><PERSON>ي", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "122"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, LaithNeural)", "privLocale": "ar-SY", "privShortName": "ar-S<PERSON>-<PERSON>thNeural", "privLocaleName": "Arabic (Syria)", "privDisplayName": "<PERSON>th", "privLocalName": "ليث", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-TN, ReemNeural)", "privLocale": "ar-TN", "privShortName": "ar-TN-ReemNeural", "privLocaleName": "Arabic (Tunisia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "ريم", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "112"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-T<PERSON>, HediNeural)", "privLocale": "ar-TN", "privShortName": "ar-TN-HediNeural", "privLocaleName": "Arabic (Tunisia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "هادي", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "118"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)", "privLocale": "ar-YE", "privShortName": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Arabic (Yemen)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "مريم", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "108"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ar-Y<PERSON>, SalehNeural)", "privLocale": "ar-YE", "privShortName": "ar-YE-SalehNeural", "privLocaleName": "Arabic (Yemen)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "صالح", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "115"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (az-AZ, BanuNeural)", "privLocale": "az-AZ", "privShortName": "az-AZ-BanuNeural", "privLocaleName": "Azerbaijani (Latin, Azerbaijan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (az-AZ, BabekNeural)", "privLocale": "az-AZ", "privShortName": "az-AZ-BabekNeural", "privLocaleName": "Azerbaijani (Latin, Azerbaijan)", "privDisplayName": "Babek", "privLocalName": "Babək", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (bg-BG, KalinaNeural)", "privLocale": "bg-BG", "privShortName": "bg-BG-KalinaNeural", "privLocaleName": "Bulgarian (Bulgaria)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "Калина", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "125"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (bg-BG, BorislavNeural)", "privLocale": "bg-BG", "privShortName": "bg-BG-<PERSON><PERSON>Neural", "privLocaleName": "Bulgarian (Bulgaria)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "Борислав", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "132"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (bn-BD, NabanitaNeural)", "privLocale": "bn-BD", "privShortName": "bn-BD-NabanitaNeural", "privLocaleName": "Bangla (Bangladesh)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "নবনীতা", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "123"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (bn-BD, PradeepNeural)", "privLocale": "bn-BD", "privShortName": "bn-BD-<PERSON><PERSON>epNeural", "privLocaleName": "Bangla (Bangladesh)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "প্রদ<PERSON><PERSON><PERSON>প", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "125"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (bn-IN, TanishaaNeural)", "privLocale": "bn-IN", "privShortName": "bn-IN-<PERSON><PERSON>aaNeural", "privLocaleName": "Bengali (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "তানিশা", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "123"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (bn-IN, BashkarNeural)", "privLocale": "bn-IN", "privShortName": "bn-IN-BashkarNeural", "privLocaleName": "Bengali (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "ভাস্কর", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "131"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (bs-BA, VesnaNeural)", "privLocale": "bs-BA", "privShortName": "bs-BA-<PERSON><PERSON><PERSON>Neural", "privLocaleName": "Bosnian (Bosnia and Herzegovina)", "privDisplayName": "Vesna", "privLocalName": "Vesna", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (bs-BA, GoranNeural)", "privLocale": "bs-BA", "privShortName": "bs-<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Bosnian (Bosnia and Herzegovina)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ca<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>)", "privLocale": "ca-ES", "privShortName": "ca-ES-<PERSON><PERSON>", "privLocaleName": "Catalan (Spain)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "147"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ca-ES, EnricNeural)", "privLocale": "ca-ES", "privShortName": "ca-ES-EnricNeural", "privLocaleName": "Catalan (Spain)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ca-ES, AlbaNeural)", "privLocale": "ca-ES", "privShortName": "ca-ES-AlbaNeural", "privLocaleName": "Catalan (Spain)", "privDisplayName": "Alba", "privLocalName": "Alba", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (cs-CZ, VlastaNeural)", "privLocale": "cs-CZ", "privShortName": "cs-CZ-VlastaNeural", "privLocaleName": "Czech (Czechia)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "118"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (cs-CZ, AntoninNeural)", "privLocale": "cs-CZ", "privShortName": "cs-CZ-<PERSON>inNeural", "privLocaleName": "Czech (Czechia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (cy-GB, NiaNeural)", "privLocale": "cy-GB", "privShortName": "cy-GB-NiaNeural", "privLocaleName": "Welsh (United Kingdom)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "136"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (cy-GB, AledNeural)", "privLocale": "cy-GB", "privShortName": "cy-GB-AledNeural", "privLocaleName": "Welsh (United Kingdom)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (da<PERSON><PERSON><PERSON>, ChristelNeural)", "privLocale": "da-DK", "privShortName": "da-DK-<PERSON><PERSON><PERSON>", "privLocaleName": "Danish (Denmark)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (da-<PERSON><PERSON>, JeppeNeural)", "privLocale": "da-DK", "privShortName": "da-D<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Danish (Denmark)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "147"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, IngridNeural)", "privLocale": "de-AT", "privShortName": "de-AT-IngridNeural", "privLocaleName": "German (Austria)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "128"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, JonasNeural)", "privLocale": "de-AT", "privShortName": "de-AT-JonasNeural", "privLocaleName": "German (Austria)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "130"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-CH, LeniNeural)", "privLocale": "de-CH", "privShortName": "de-CH-LeniNeural", "privLocaleName": "German (Switzerland)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "121"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, JanNeural)", "privLocale": "de-CH", "privShortName": "de-CH-Jan<PERSON><PERSON><PERSON>", "privLocaleName": "German (Switzerland)", "privDisplayName": "Jan", "privLocalName": "Jan", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "privLocale": "de-DE", "privShortName": "de-DE-Katja<PERSON>eural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "121"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, ConradNeural)", "privLocale": "de-DE", "privShortName": "de-DE-ConradNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "128"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-DE, AmalaNeural)", "privLocale": "de-DE", "privShortName": "de-DE-AmalaNeural", "privLocaleName": "German (Germany)", "privDisplayName": "Amala", "privLocalName": "Amala", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "115"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-DE, BerndNeural)", "privLocale": "de-DE", "privShortName": "de-DE-BerndNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "123"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, ChristophNeural)", "privLocale": "de-DE", "privShortName": "de-DE-ChristophNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "128"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-DE, ElkeNeural)", "privLocale": "de-DE", "privShortName": "de-DE-ElkeNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-DE, GiselaNeural)", "privLocale": "de-DE", "privShortName": "de-DE-GiselaNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "110"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-DE, KasperNeural)", "privLocale": "de-DE", "privShortName": "de-DE-KasperNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "129"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, <PERSON>ian<PERSON>al)", "privLocale": "de-DE", "privShortName": "de-DE-<PERSON><PERSON><PERSON>", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "126"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-DE, KlarissaNeural)", "privLocale": "de-DE", "privShortName": "de-DE-KlarissaNeural", "privLocaleName": "German (Germany)", "privDisplayName": "Klarissa", "privLocalName": "Klarissa", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "116"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, KlausNeural)", "privLocale": "de-DE", "privShortName": "de-DE-KlausN<PERSON>", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "106"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, LouisaNeural)", "privLocale": "de-DE", "privShortName": "de-DE-LouisaNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, MajaNeural)", "privLocale": "de-DE", "privShortName": "de-DE-Maj<PERSON><PERSON><PERSON>", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "116"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, RalfNeural)", "privLocale": "de-DE", "privShortName": "de-DE-RalfNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "127"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (de-DE, TanjaNeural)", "privLocale": "de-DE", "privShortName": "de-DE-TanjaNeural", "privLocaleName": "German (Germany)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (el-GR, AthinaNeural)", "privLocale": "el-GR", "privShortName": "el-GR-AthinaNeural", "privLocaleName": "Greek (Greece)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "Αθηνά", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "156"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (el-GR, NestorasNeural)", "privLocale": "el-GR", "privShortName": "el-GR-NestorasNeural", "privLocaleName": "Greek (Greece)", "privDisplayName": "Nestoras", "privLocalName": "Νέστορας", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "158"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "139"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, WilliamNeural)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, AnnetteNeural)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, CarlyNeural)", "privLocale": "en-AU", "privShortName": "en-AU-CarlyN<PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "137"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, DarrenNeural)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "159"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, DuncanNeural)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "153"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, ElsieNeural)", "privLocale": "en-AU", "privShortName": "en-AU-ElsieN<PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, FreyaNeural)", "privLocale": "en-AU", "privShortName": "en-AU-Freya<PERSON>eural", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, JoanneNeural)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "153"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-AU, KenNeural)", "privLocale": "en-AU", "privShortName": "en-AU-KenN<PERSON>al", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "159"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-AU, KimNeural)", "privLocale": "en-AU", "privShortName": "en-AU-Kim<PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, NeilNeural)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-AU, TimNeural)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "144"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, TinaNeural)", "privLocale": "en-AU", "privShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Australia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "136"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-CA, ClaraNeural)", "privLocale": "en-CA", "privShortName": "en-CA-ClaraNeural", "privLocaleName": "English (Canada)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "167"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, LiamNeural)", "privLocale": "en-CA", "privShortName": "en-CA-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Canada)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "180"}, {"privStyleList": ["cheerful", "sad"], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, SoniaN<PERSON>al)", "privLocale": "en-GB", "privShortName": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": ["cheerful", "chat"], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, RyanNeural)", "privLocale": "en-GB", "privShortName": "en-GB-RyanN<PERSON><PERSON>", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "161"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "privLocale": "en-GB", "privShortName": "en-GB-LibbyNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "138"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, AbbiNeural)", "privLocale": "en-GB", "privShortName": "en-GB-AbbiNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "145"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, AlfieNeural)", "privLocale": "en-GB", "privShortName": "en-GB-AlfieNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, BellaNeural)", "privLocale": "en-GB", "privShortName": "en-GB-BellaNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "146"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, ElliotNeural)", "privLocale": "en-GB", "privShortName": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, EthanNeural)", "privLocale": "en-GB", "privShortName": "en-GB-EthanNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, HollieNeural)", "privLocale": "en-GB", "privShortName": "en-GB-HollieNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>llie", "privLocalName": "<PERSON>llie", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "144"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, <PERSON><PERSON>)", "privLocale": "en-GB", "privShortName": "en-GB-<PERSON><PERSON><PERSON>", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "141"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, NoahNeural)", "privLocale": "en-GB", "privShortName": "en-GB-NoahNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, OliverNeural)", "privLocale": "en-GB", "privShortName": "en-GB-OliverNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, OliviaNeural)", "privLocale": "en-GB", "privShortName": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, ThomasNeural)", "privLocale": "en-GB", "privShortName": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-GB, MiaNeural)", "privLocale": "en-GB", "privShortName": "en-GB-MiaNeural", "privLocaleName": "English (United Kingdom)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "Deprecated"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-HK, YanNeural)", "privLocale": "en-HK", "privShortName": "en-HK-YanNeural", "privLocaleName": "English (Hong Kong SAR)", "privDisplayName": "Yan", "privLocalName": "Yan", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "144"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-HK, SamNeural)", "privLocale": "en-HK", "privShortName": "en-HK-SamNeural", "privLocaleName": "English (Hong Kong SAR)", "privDisplayName": "Sam", "privLocalName": "Sam", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "140"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, EmilyN<PERSON><PERSON>)", "privLocale": "en-IE", "privShortName": "en-IE-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Ireland)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, ConnorNeural)", "privLocale": "en-IE", "privShortName": "en-IE-Connor<PERSON><PERSON><PERSON>", "privLocaleName": "English (Ireland)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "146"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-IN, NeerjaNeural)", "privLocale": "en-IN", "privShortName": "en-IN-NeerjaNeural", "privLocaleName": "English (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "139"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-IN, PrabhatNeural)", "privLocale": "en-IN", "privShortName": "en-IN-PrabhatNeural", "privLocaleName": "English (India)", "privDisplayName": "Prab<PERSON>", "privLocalName": "Prab<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "129"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, AsiliaNeural)", "privLocale": "en-KE", "privShortName": "en-KE-AsiliaNeural", "privLocaleName": "English (Kenya)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-KE, ChilembaNeural)", "privLocale": "en-KE", "privShortName": "en-KE-ChilembaNeural", "privLocaleName": "English (Kenya)", "privDisplayName": "Chilemba", "privLocalName": "Chilemba", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "156"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-NG, EzinneNeural)", "privLocale": "en-NG", "privShortName": "en-NG-EzinneNeural", "privLocaleName": "English (Nigeria)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "142"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-NG, AbeoNeural)", "privLocale": "en-NG", "privShortName": "en-NG-AbeoNeural", "privLocaleName": "English (Nigeria)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "153"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, MollyNeural)", "privLocale": "en-NZ", "privShortName": "en-NZ-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (New Zealand)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "132"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-NZ, MitchellNeural)", "privLocale": "en-NZ", "privShortName": "en-NZ-MitchellNeural", "privLocaleName": "English (New Zealand)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-PH, RosaNeural)", "privLocale": "en-PH", "privShortName": "en-PH-RosaNeural", "privLocaleName": "English (Philippines)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "137"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, JamesNeural)", "privLocale": "en-PH", "privShortName": "en-PH-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (Philippines)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "147"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-SG, LunaNeural)", "privLocale": "en-SG", "privShortName": "en-SG-LunaNeural", "privLocaleName": "English (Singapore)", "privDisplayName": "Luna", "privLocalName": "Luna", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, WayneNeural)", "privLocale": "en-SG", "privShortName": "en-SG-Wayne<PERSON><PERSON><PERSON>", "privLocaleName": "English (Singapore)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-TZ, ImaniNeural)", "privLocale": "en-TZ", "privShortName": "en-TZ-ImaniNeural", "privLocaleName": "English (Tanzania)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "142"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-TZ, ElimuNeural)", "privLocale": "en-TZ", "privShortName": "en-TZ-ElimuNeural", "privLocaleName": "English (Tanzania)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, AvaNeural)", "privLocale": "en-US", "privShortName": "en-US-AvaNeural", "privLocaleName": "English (United States)", "privDisplayName": "Ava", "privLocalName": "Ava", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, AndrewNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, EmmaNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, BrianNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA"}, {"privStyleList": ["assistant", "chat", "customerservice", "newscast", "angry", "cheerful", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, JennyNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": ["newscast", "angry", "cheerful", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "privLocale": "en-US", "privShortName": "en-US-GuyN<PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "215"}, {"privStyleList": ["chat", "customerservice", "narration-professional", "newscast-casual", "newscast-formal", "cheerful", "empathetic", "angry", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "privLocale": "en-US", "privShortName": "en-US-AriaNeural", "privLocaleName": "English (United States)", "privDisplayName": "Aria", "privLocalName": "Aria", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": ["chat", "angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, DavisNeural)", "privLocale": "en-US", "privShortName": "en-US-DavisNeural", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, JaneNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, JasonNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "156"}, {"privStyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, SaraNeural)", "privLocale": "en-US", "privShortName": "en-US-SaraNeural", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, TonyNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "156"}, {"privStyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, NancyNeural)", "privLocale": "en-US", "privShortName": "en-US-NancyNeural", "privLocaleName": "English (United States)", "privDisplayName": "Nancy", "privLocalName": "Nancy", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, AmberNeural)", "privLocale": "en-US", "privShortName": "en-US-AmberN<PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "Amber", "privLocalName": "Amber", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, AnaNeural)", "privLocale": "en-US", "privShortName": "en-US-AnaNeural", "privLocaleName": "English (United States)", "privDisplayName": "Ana", "privLocalName": "Ana", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, AshleyNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, BrandonNeural)", "privLocale": "en-US", "privShortName": "en-US-BrandonN<PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "156"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, ChristopherNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, CoraNeural)", "privLocale": "en-US", "privShortName": "en-US-CoraNeural", "privLocaleName": "English (United States)", "privDisplayName": "Cora", "privLocalName": "Cora", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "146"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, ElizabethNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, EricNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "147"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, JacobNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, JennyMultilingualNeural)", "privLocale": "en-US", "privShortName": "en-US-JennyMultilingualNeural", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "190", "privSecondaryLocaleList": ["de-DE", "en-AU", "en-CA", "en-GB", "es-ES", "es-MX", "fr-CA", "fr-FR", "it-IT", "ja-<PERSON>", "ko-KR", "pt-BR", "zh-CN"]}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, JennyMultilingualV2Neural)", "privLocale": "en-US", "privShortName": "en-US-JennyMultilingualV2Neural", "privLocaleName": "English (United States)", "privDisplayName": "Jenny Multilingual V2", "privLocalName": "Jenny Multilingual V2", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "190", "privSecondaryLocaleList": ["ar-EG", "ar-SA", "ca-ES", "cs-CZ", "da-DK", "de-AT", "de-CH", "de-DE", "en-AU", "en-CA", "en-GB", "en-HK", "en-IE", "en-IN", "en-US", "es-ES", "es-MX", "fi-FI", "fr-BE", "fr-CA", "fr-CH", "fr-FR", "hi-IN", "hu-HU", "id-ID", "it-IT", "ja-<PERSON>", "ko-KR", "nb-NO", "nl-BE", "nl-NL", "pl-PL", "pt-BR", "pt-PT", "ru-RU", "sv-SE", "th-TH", "tr-TR", "zh-CN", "zh-HK", "zh-TW"]}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, MichelleNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, MonicaNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "145"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, RogerNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, RyanMultilingualNeural)", "privLocale": "en-US", "privShortName": "en-US-RyanMultilingualNeural", "privLocaleName": "English (United States)", "privDisplayName": "Ryan Multilingual", "privLocalName": "Ryan Multilingual", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "190", "privSecondaryLocaleList": ["ar-EG", "ar-SA", "ca-ES", "cs-CZ", "da-DK", "de-AT", "de-CH", "de-DE", "en-AU", "en-CA", "en-GB", "en-HK", "en-IE", "en-IN", "en-US", "es-ES", "es-MX", "fi-FI", "fr-BE", "fr-CA", "fr-CH", "fr-FR", "hi-IN", "hu-HU", "id-ID", "it-IT", "ja-<PERSON>", "ko-KR", "nb-NO", "nl-BE", "nl-NL", "pl-PL", "pt-BR", "pt-PT", "ru-RU", "sv-SE", "th-TH", "tr-TR", "zh-CN", "zh-HK", "zh-TW"]}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-US, SteffanNeural)", "privLocale": "en-US", "privShortName": "en-US-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "English (United States)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, LeahNeural)", "privLocale": "en-ZA", "privShortName": "en-ZA-LeahNeural", "privLocaleName": "English (South Africa)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (en-ZA, LukeNeural)", "privLocale": "en-ZA", "privShortName": "en-ZA-LukeNeural", "privLocaleName": "English (South Africa)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "168"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-AR, ElenaNeural)", "privLocale": "es-AR", "privShortName": "es-AR-ElenaNeural", "privLocaleName": "Spanish (Argentina)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-AR, TomasNeural)", "privLocale": "es-AR", "privShortName": "es-AR-TomasNeural", "privLocaleName": "Spanish (Argentina)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "158"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-BO, SofiaNeural)", "privLocale": "es-BO", "privShortName": "es-BO-SofiaNeural", "privLocaleName": "Spanish (Bolivia)", "privDisplayName": "Sofia", "privLocalName": "Sofia", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "159"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-B<PERSON>, MarceloNeural)", "privLocale": "es-BO", "privShortName": "es-BO-MarceloNeural", "privLocaleName": "Spanish (Bolivia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-CL, CatalinaNeural)", "privLocale": "es-CL", "privShortName": "es-CL-CatalinaNeural", "privLocaleName": "Spanish (Chile)", "privDisplayName": "Catalina", "privLocalName": "Catalina", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "295"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-CL, LorenzoNeural)", "privLocale": "es-CL", "privShortName": "es-CL-LorenzoNeural", "privLocaleName": "Spanish (Chile)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "318"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-CO, SalomeNeural)", "privLocale": "es-CO", "privShortName": "es-CO-SalomeNeural", "privLocaleName": "Spanish (Colombia)", "privDisplayName": "Salome", "privLocalName": "Salome", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-CO, GonzaloNeural)", "privLocale": "es-CO", "privShortName": "es-CO-GonzaloNeural", "privLocaleName": "Spanish (Colombia)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "161"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-CR, MariaNeural)", "privLocale": "es-CR", "privShortName": "es-CR-MariaNeural", "privLocaleName": "Spanish (Costa Rica)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "138"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-CR, JuanNeural)", "privLocale": "es-CR", "privShortName": "es-CR-JuanNeural", "privLocaleName": "Spanish (Costa Rica)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-CU, BelkysNeural)", "privLocale": "es-CU", "privShortName": "es-CU-BelkysNeural", "privLocaleName": "Spanish (Cuba)", "privDisplayName": "Belkys", "privLocalName": "Belkys", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-CU, ManuelNeural)", "privLocale": "es-CU", "privShortName": "es-CU-ManuelNeural", "privLocaleName": "Spanish (Cuba)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "137"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-D<PERSON>, Ramona<PERSON>al)", "privLocale": "es-DO", "privShortName": "es-DO-RamonaNeural", "privLocaleName": "Spanish (Dominican Republic)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "130"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-D<PERSON>, EmilioNeural)", "privLocale": "es-DO", "privShortName": "es-DO-EmilioNeural", "privLocaleName": "Spanish (Dominican Republic)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-EC, AndreaNeural)", "privLocale": "es-EC", "privShortName": "es-EC-AndreaNeural", "privLocaleName": "Spanish (Ecuador)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-EC, LuisNeural)", "privLocale": "es-EC", "privShortName": "es-EC-LuisNeural", "privLocaleName": "Spanish (Ecuador)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "138"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "privLocale": "es-ES", "privShortName": "es-ES-Elvira<PERSON>eural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, AlvaroNeural)", "privLocale": "es-ES", "privShortName": "es-ES-AlvaroNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, AbrilNeural)", "privLocale": "es-ES", "privShortName": "es-ES-AbrilNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "Abril", "privLocalName": "Abril", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "146"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, ArnauNeural)", "privLocale": "es-ES", "privShortName": "es-ES-ArnauNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "Arna<PERSON>", "privLocalName": "Arna<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "142"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, DarioNeural)", "privLocale": "es-ES", "privShortName": "es-ES-DarioNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "Dar<PERSON>", "privLocalName": "Dar<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "164"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, EliasNeural)", "privLocale": "es-ES", "privShortName": "es-ES-EliasNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, EstrellaNeural)", "privLocale": "es-ES", "privShortName": "es-ES-EstrellaNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "Estrella", "privLocalName": "Estrella", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, IreneNeural)", "privLocale": "es-ES", "privShortName": "es-ES-IreneNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, LaiaNeural)", "privLocale": "es-ES", "privShortName": "es-ES-LaiaNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "141"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, LiaNeural)", "privLocale": "es-ES", "privShortName": "es-ES-LiaNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "Lia", "privLocalName": "Lia", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, NilNeural)", "privLocale": "es-ES", "privShortName": "es-ES-NilNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON>l", "privLocalName": "<PERSON>l", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, SaulNeural)", "privLocale": "es-ES", "privShortName": "es-ES-SaulNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, TeoNeural)", "privLocale": "es-ES", "privShortName": "es-ES-TeoNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, TrianaNeural)", "privLocale": "es-ES", "privShortName": "es-ES-TrianaNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "Triana", "privLocalName": "Triana", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-ES, VeraNeural)", "privLocale": "es-ES", "privShortName": "es-ES-VeraNeural", "privLocaleName": "Spanish (Spain)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-G<PERSON>, TeresaNeural)", "privLocale": "es-GQ", "privShortName": "es-GQ-Teresa<PERSON><PERSON><PERSON>", "privLocaleName": "Spanish (Equatorial Guinea)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-GQ, JavierNeural)", "privLocale": "es-GQ", "privShortName": "es-GQ-JavierNeural", "privLocaleName": "Spanish (Equatorial Guinea)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "129"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-GT, MartaNeural)", "privLocale": "es-GT", "privShortName": "es-GT-MartaNeural", "privLocaleName": "Spanish (Guatemala)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "159"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-GT, AndresNeural)", "privLocale": "es-GT", "privShortName": "es-GT-AndresNeural", "privLocaleName": "Spanish (Guatemala)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-HN, KarlaNeural)", "privLocale": "es-HN", "privShortName": "es-HN-Karla<PERSON>eural", "privLocaleName": "Spanish (Honduras)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "137"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-HN, CarlosNeural)", "privLocale": "es-HN", "privShortName": "es-HN-CarlosNeural", "privLocaleName": "Spanish (Honduras)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "privLocale": "es-MX", "privShortName": "es-MX-DaliaNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "Dal<PERSON>", "privLocalName": "Dal<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "145"}, {"privStyleList": ["cheerful", "chat"], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, JorgeNeural)", "privLocale": "es-MX", "privShortName": "es-MX-JorgeNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, BeatrizNeural)", "privLocale": "es-MX", "privShortName": "es-MX-BeatrizNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, CandelaNeural)", "privLocale": "es-MX", "privShortName": "es-MX-CandelaNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "Candela", "privLocalName": "Candela", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, CarlotaNeural)", "privLocale": "es-MX", "privShortName": "es-MX-CarlotaNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "145"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, CecilioNeural)", "privLocale": "es-MX", "privShortName": "es-MX-CecilioNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, GerardoNeural)", "privLocale": "es-MX", "privShortName": "es-MX-Gerardo<PERSON>eural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "141"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, LarissaNeural)", "privLocale": "es-MX", "privShortName": "es-MX-LarissaNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "Larissa", "privLocalName": "Larissa", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "151"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, LibertoNeural)", "privLocale": "es-MX", "privShortName": "es-MX-Liberto<PERSON>eural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, LucianoNeural)", "privLocale": "es-MX", "privShortName": "es-MX-LucianoNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "139"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, MarinaNeural)", "privLocale": "es-MX", "privShortName": "es-MX-MarinaNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "Marina", "privLocalName": "Marina", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "136"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, NuriaNeural)", "privLocale": "es-MX", "privShortName": "es-MX-NuriaNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, PelayoNeural)", "privLocale": "es-MX", "privShortName": "es-MX-PelayoNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "Pelayo", "privLocalName": "Pelayo", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, RenataNeural)", "privLocale": "es-MX", "privShortName": "es-MX-RenataNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "Renata", "privLocalName": "Renata", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "153"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-MX, YagoNeural)", "privLocale": "es-MX", "privShortName": "es-MX-YagoNeural", "privLocaleName": "Spanish (Mexico)", "privDisplayName": "<PERSON>go", "privLocalName": "<PERSON>go", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-NI, YolandaNeural)", "privLocale": "es-NI", "privShortName": "es-NI-Yo<PERSON>aNeural", "privLocaleName": "Spanish (Nicaragua)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "130"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-NI, FedericoNeural)", "privLocale": "es-NI", "privShortName": "es-NI-FedericoNeural", "privLocaleName": "Spanish (Nicaragua)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-PA, MargaritaNeural)", "privLocale": "es-PA", "privShortName": "es-PA-MargaritaNeural", "privLocaleName": "Spanish (Panama)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-PA, RobertoNeural)", "privLocale": "es-PA", "privShortName": "es-PA-RobertoNeural", "privLocaleName": "Spanish (Panama)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-PE, CamilaNeural)", "privLocale": "es-PE", "privShortName": "es-PE-CamilaNeural", "privLocaleName": "Spanish (Peru)", "privDisplayName": "Camila", "privLocalName": "Camila", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "159"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-PE, AlexNeural)", "privLocale": "es-PE", "privShortName": "es-PE-AlexNeural", "privLocaleName": "Spanish (Peru)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-<PERSON>, <PERSON>a<PERSON>)", "privLocale": "es-PR", "privShortName": "es-PR-Karin<PERSON>", "privLocaleName": "Spanish (Puerto Rico)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "130"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-PR, VictorNeural)", "privLocale": "es-PR", "privShortName": "es-PR-VictorNeural", "privLocaleName": "Spanish (Puerto Rico)", "privDisplayName": "Victor", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "138"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-PY, TaniaNeural)", "privLocale": "es-PY", "privShortName": "es-PY-TaniaNeural", "privLocaleName": "Spanish (Paraguay)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "151"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-PY, MarioNeural)", "privLocale": "es-PY", "privShortName": "es-PY-<PERSON>", "privLocaleName": "Spanish (Paraguay)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "168"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-SV, LorenaNeural)", "privLocale": "es-SV", "privShortName": "es-SV-LorenaNeural", "privLocaleName": "Spanish (El Salvador)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "159"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-SV, RodrigoNeural)", "privLocale": "es-SV", "privShortName": "es-SV-RodrigoNeural", "privLocaleName": "Spanish (El Salvador)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-US, PalomaNeural)", "privLocale": "es-US", "privShortName": "es-US-PalomaNeural", "privLocaleName": "Spanish (United States)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-US, AlonsoNeural)", "privLocale": "es-US", "privShortName": "es-US-AlonsoNeural", "privLocaleName": "Spanish (United States)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "138"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-UY, ValentinaNeural)", "privLocale": "es-UY", "privShortName": "es-UY-ValentinaNeural", "privLocaleName": "Spanish (Uruguay)", "privDisplayName": "Valentina", "privLocalName": "Valentina", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-UY, MateoNeural)", "privLocale": "es-UY", "privShortName": "es-UY-MateoNeural", "privLocaleName": "Spanish (Uruguay)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "158"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-VE, PaolaNeural)", "privLocale": "es-VE", "privShortName": "es-VE-PaolaNeural", "privLocaleName": "Spanish (Venezuela)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "130"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (es-V<PERSON>, SebastianNeural)", "privLocale": "es-VE", "privShortName": "es-VE-SebastianNeural", "privLocaleName": "Spanish (Venezuela)", "privDisplayName": "<PERSON>", "privLocalName": "Sebastián", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (et-EE, AnuNeural)", "privLocale": "et-EE", "privShortName": "et-EE-AnuNeural", "privLocaleName": "Estonian (Estonia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (et-EE, KertNeural)", "privLocale": "et-EE", "privShortName": "et-EE-KertNeural", "privLocaleName": "Estonian (Estonia)", "privDisplayName": "Kert", "privLocalName": "Kert", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (eu-ES, AinhoaNeural)", "privLocale": "eu-ES", "privShortName": "eu-ES-AinhoaNeural", "privLocaleName": "Basque", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "102"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (eu-ES, AnderNeural)", "privLocale": "eu-ES", "privShortName": "eu-ES-AnderNeural", "privLocaleName": "Basque", "privDisplayName": "Ander", "privLocalName": "Ander", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "102"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fa-IR, DilaraNeural)", "privLocale": "fa-IR", "privShortName": "fa-IR-DilaraNeural", "privLocaleName": "Persian (Iran)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "دلارا", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fa-IR, FaridNeural)", "privLocale": "fa-IR", "privShortName": "fa-IR-FaridNeural", "privLocaleName": "Persian (Iran)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "فرید", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fi-FI, SelmaNeural)", "privLocale": "fi-FI", "privShortName": "fi-FI-SelmaNeural", "privLocaleName": "Finnish (Finland)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "91"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fi-FI, HarriNeural)", "privLocale": "fi-FI", "privShortName": "fi-FI-HarriNeural", "privLocaleName": "Finnish (Finland)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "97"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fi-FI, NooraNeural)", "privLocale": "fi-FI", "privShortName": "fi-FI-NooraNeural", "privLocaleName": "Finnish (Finland)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "96"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fil-PH, BlessicaNeural)", "privLocale": "fil-PH", "privShortName": "fil-PH-BlessicaNeural", "privLocaleName": "Filipino (Philippines)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "140"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fil-PH, AngeloNeural)", "privLocale": "fil-PH", "privShortName": "fil-PH-AngeloNeural", "privLocaleName": "Filipino (Philippines)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "144"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, CharlineNeural)", "privLocale": "fr-BE", "privShortName": "fr-BE-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "French (Belgium)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr<PERSON><PERSON><PERSON>, GerardN<PERSON>al)", "privLocale": "fr-BE", "privShortName": "fr-B<PERSON>-<PERSON><PERSON>", "privLocaleName": "French (Belgium)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "172"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "privLocale": "fr-CA", "privShortName": "fr-CA-<PERSON><PERSON>vie<PERSON>eural", "privLocaleName": "French (Canada)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON>, JeanNeural)", "privLocale": "fr-CA", "privShortName": "fr-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "French (Canada)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-CA, AntoineNeural)", "privLocale": "fr-CA", "privShortName": "fr-CA-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "French (Canada)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "159"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-CH, ArianeNeural)", "privLocale": "fr-CH", "privShortName": "fr-CH-ArianeNeural", "privLocaleName": "French (Switzerland)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "158"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-CH, FabriceNeural)", "privLocale": "fr-CH", "privShortName": "fr-CH-FabriceNeural", "privLocaleName": "French (Switzerland)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "172"}, {"privStyleList": ["cheerful", "sad"], "privName": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": ["cheerful", "sad"], "privName": "Microsoft Server Speech Text to Speech Voice (fr-F<PERSON>, HenriNeural)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "165"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, AlainN<PERSON>al)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "165"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-FR, BrigitteNeural)", "privLocale": "fr-FR", "privShortName": "fr-FR-B<PERSON>itteNeural", "privLocaleName": "French (France)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "153"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-FR, CelesteNeural)", "privLocale": "fr-FR", "privShortName": "fr-FR-CelesteNeural", "privLocaleName": "French (France)", "privDisplayName": "Celeste", "privLocalName": "Celeste", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, ClaudeN<PERSON>al)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-FR, <PERSON>ie<PERSON>al)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-FR, EloiseNeural)", "privLocale": "fr-FR", "privShortName": "fr-FR-EloiseNeural", "privLocaleName": "French (France)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "150"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, JacquelineNeural)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, JeromeN<PERSON>)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "165"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-FR, JosephineNeural)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, MauriceN<PERSON>)", "privLocale": "fr-FR", "privShortName": "fr-FR-<PERSON><PERSON>", "privLocaleName": "French (France)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "162"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-F<PERSON>, YvesNeural)", "privLocale": "fr-FR", "privShortName": "fr-FR-YvesNeural", "privLocaleName": "French (France)", "privDisplayName": "Yves", "privLocalName": "Yves", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "162"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (fr-FR, YvetteNeural)", "privLocale": "fr-FR", "privShortName": "fr-FR-YvetteNeural", "privLocaleName": "French (France)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "156"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ga-IE, OrlaNeural)", "privLocale": "ga-IE", "privShortName": "ga-IE-OrlaNeural", "privLocaleName": "Irish (Ireland)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "139"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ga-IE, ColmNeural)", "privLocale": "ga-IE", "privShortName": "ga-IE-ColmNeural", "privLocaleName": "Irish (Ireland)", "privDisplayName": "Colm", "privLocalName": "Colm", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (gl-ES, SabelaNeural)", "privLocale": "gl-ES", "privShortName": "gl-ES-SabelaNeural", "privLocaleName": "Galician", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "141"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (gl-ES, RoiNeural)", "privLocale": "gl-ES", "privShortName": "gl-ES-RoiNeural", "privLocaleName": "Galician", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (gu-IN, DhwaniNeural)", "privLocale": "gu-IN", "privShortName": "gu-IN-DhwaniNeural", "privLocaleName": "Gujarati (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "ધ્વની", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "89"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (gu-IN, NiranjanNeural)", "privLocale": "gu-IN", "privShortName": "gu-IN-NiranjanNeural", "privLocaleName": "Gujarati (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "નિરંજન", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "107"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (he-IL, HilaNeural)", "privLocale": "he-IL", "privShortName": "he-IL-HilaNeural", "privLocaleName": "Hebrew (Israel)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "הילה", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "113"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (he-IL, AvriNeural)", "privLocale": "he-IL", "privShortName": "he-IL-AvriNeural", "privLocaleName": "Hebrew (Israel)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "א<PERSON><PERSON>י", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "106"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (hi-IN, SwaraNeural)", "privLocale": "hi-IN", "privShortName": "hi-IN-SwaraNeural", "privLocaleName": "Hindi (India)", "privDisplayName": "Swara", "privLocalName": "स्वरा", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "117"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (hi-<PERSON>, <PERSON><PERSON><PERSON>)", "privLocale": "hi-IN", "privShortName": "hi-IN-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Hindi (India)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>र", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (hr-HR, GabrijelaNeural)", "privLocale": "hr-HR", "privShortName": "hr-HR-<PERSON><PERSON><PERSON><PERSON>laNeural", "privLocaleName": "Croatian (Croatia)", "privDisplayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "124"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (hr-HR, SreckoNeural)", "privLocale": "hr-HR", "privShortName": "hr-HR-SreckoNeural", "privLocaleName": "Croatian (Croatia)", "privDisplayName": "Srecko", "privLocalName": "<PERSON><PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "133"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (hu-HU, NoemiNeural)", "privLocale": "hu-HU", "privShortName": "hu-HU-NoemiNeural", "privLocaleName": "Hungarian (Hungary)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "110"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (hu-HU, TamasNeural)", "privLocale": "hu-HU", "privShortName": "hu-HU-TamasNeural", "privLocaleName": "Hungarian (Hungary)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "Tamás", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "124"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (hy-AM, AnahitNeural)", "privLocale": "hy-AM", "privShortName": "hy-AM-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Armenian (Armenia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "Անահիտ", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "112"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (hy-AM, HaykNeural)", "privLocale": "hy-AM", "privShortName": "hy-AM-HaykNeural", "privLocaleName": "Armenian (Armenia)", "privDisplayName": "Hayk", "privLocalName": "Հայկ", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "112"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (id-ID, GadisNeural)", "privLocale": "id-ID", "privShortName": "id-ID-GadisNeural", "privLocaleName": "Indonesian (Indonesia)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "111"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (id-ID, ArdiNeural)", "privLocale": "id-ID", "privShortName": "id-ID-ArdiNeural", "privLocaleName": "Indonesian (Indonesia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "124"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (is-IS, GudrunNeural)", "privLocale": "is-IS", "privShortName": "is-IS-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Icelandic (Iceland)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (is-IS, GunnarNeural)", "privLocale": "is-IS", "privShortName": "is-IS-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Icelandic (Iceland)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "144"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "privLocale": "it-IT", "privShortName": "it-IT-ElsaNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "148"}, {"privStyleList": ["cheerful", "chat"], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, IsabellaNeural)", "privLocale": "it-IT", "privShortName": "it-IT-IsabellaNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "Isabella", "privLocalName": "Isabella", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, DiegoNeural)", "privLocale": "it-IT", "privShortName": "it-IT-DiegoNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "Diego", "privLocalName": "Diego", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, BenignoNeural)", "privLocale": "it-IT", "privShortName": "it-IT-BenignoNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "153"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, CalimeroNeural)", "privLocale": "it-IT", "privShortName": "it-IT-CalimeroNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "Calimero", "privLocalName": "Calimero", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "142"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, CataldoNeural)", "privLocale": "it-IT", "privShortName": "it-IT-Cat<PERSON>Neural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "149"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, FabiolaNeural)", "privLocale": "it-IT", "privShortName": "it-IT-FabiolaNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, FiammaNeural)", "privLocale": "it-IT", "privShortName": "it-IT-FiammaNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "141"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, GianniNeural)", "privLocale": "it-IT", "privShortName": "it-IT-<PERSON><PERSON><PERSON>", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "139"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, ImeldaNeural)", "privLocale": "it-IT", "privShortName": "it-IT-ImeldaNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "Imelda", "privLocalName": "Imelda", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "140"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, IrmaNeural)", "privLocale": "it-IT", "privShortName": "it-IT-IrmaNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "142"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, LisandroNeural)", "privLocale": "it-IT", "privShortName": "it-IT-LisandroNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "137"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, PalmiraNeural)", "privLocale": "it-IT", "privShortName": "it-IT-PalmiraNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "139"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, PierinaNeural)", "privLocale": "it-IT", "privShortName": "it-IT-<PERSON><PERSON>Neural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (it-IT, RinaldoNeural)", "privLocale": "it-IT", "privShortName": "it-IT-RinaldoNeural", "privLocaleName": "Italian (Italy)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "137"}, {"privStyleList": ["chat", "customerservice", "cheerful"], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "privLocale": "ja-<PERSON>", "privShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Japanese (Japan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "七海", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "305"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ja<PERSON><PERSON>, KeitaNeural)", "privLocale": "ja-<PERSON>", "privShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Japanese (Japan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "圭太", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "337"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ja-JP, AoiNeural)", "privLocale": "ja-<PERSON>", "privShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Japanese (Japan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "碧衣", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "270"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (j<PERSON><PERSON><PERSON>, DaichiNeural)", "privLocale": "ja-<PERSON>", "privShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Japanese (Japan)", "privDisplayName": "Dai<PERSON>", "privLocalName": "大智", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "312"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (j<PERSON><PERSON><PERSON>, MayuNeural)", "privLocale": "ja-<PERSON>", "privShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Japanese (Japan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "真夕", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "302"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ja-<PERSON>, NaokiNeural)", "privLocale": "ja-<PERSON>", "privShortName": "ja-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Japanese (Japan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "直紀", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "312"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ja-<PERSON>, ShioriNeural)", "privLocale": "ja-<PERSON>", "privShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Japanese (Japan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "志織", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "296"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (jv-ID, SitiNeural)", "privLocale": "jv-ID", "privShortName": "jv-ID-SitiNeural", "privLocaleName": "Javanese (Latin, Indonesia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "104"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (jv-ID, DimasNeural)", "privLocale": "jv-ID", "privShortName": "jv-ID-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Javanese (Latin, Indonesia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "115"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ka-GE, EkaNeural)", "privLocale": "ka-GE", "privShortName": "ka-GE-EkaNeural", "privLocaleName": "<PERSON> (Georgia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "ეკა", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "104"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ka-GE, GiorgiNeural)", "privLocale": "ka-GE", "privShortName": "ka-GE-GiorgiNeural", "privLocaleName": "<PERSON> (Georgia)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "გიორგი", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "104"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (kk-KZ, AigulNeural)", "privLocale": "kk-KZ", "privShortName": "kk-KZ-AigulNeural", "privLocaleName": "Kazakh (Kazakhstan)", "privDisplayName": "Aigul", "privLocalName": "Айгүл", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "107"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (kk-KZ, DauletNeural)", "privLocale": "kk-KZ", "privShortName": "kk-KZ-DauletNeural", "privLocaleName": "Kazakh (Kazakhstan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "Дәулет", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "111"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (km-KH, SreymomNeural)", "privLocale": "km-KH", "privShortName": "km-KH-SreymomNeural", "privLocaleName": "Khmer (Cambodia)", "privDisplayName": "Sreymom", "privLocalName": "ស្រីមុំ", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "25"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (km-KH, PisethNeural)", "privLocale": "km-KH", "privShortName": "km-KH-PisethNeural", "privLocaleName": "Khmer (Cambodia)", "privDisplayName": "Piseth", "privLocalName": "ពិសិដ្ឋ", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "25"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (kn-IN, SapnaNeural)", "privLocale": "kn-IN", "privShortName": "kn-IN-Sa<PERSON>naNeural", "privLocaleName": "Kannada (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "ಸಪ್ನಾ", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "94"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (kn-<PERSON>, GaganNeural)", "privLocale": "kn-IN", "privShortName": "kn-IN-GaganNeural", "privLocaleName": "Kannada (India)", "privDisplayName": "Gaga<PERSON>", "privLocalName": "ಗಗನ್", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "100"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "privLocale": "ko-KR", "privShortName": "ko-KR-SunHiNeural", "privLocaleName": "Korean (Korea)", "privDisplayName": "Sun-Hi", "privLocalName": "선히", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "274"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ko-KR, InJoonNeural)", "privLocale": "ko-KR", "privShortName": "ko-KR-InJoonNeural", "privLocaleName": "Korean (Korea)", "privDisplayName": "InJoon", "privLocalName": "인준", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "253"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ko-KR, BongJinNeural)", "privLocale": "ko-KR", "privShortName": "ko-KR-BongJinNeural", "privLocaleName": "Korean (Korea)", "privDisplayName": "<PERSON><PERSON><PERSON><PERSON>", "privLocalName": "봉진", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "262"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ko-KR, GookMinNeural)", "privLocale": "ko-KR", "privShortName": "ko-KR-GookMinNeural", "privLocaleName": "Korean (Korea)", "privDisplayName": "GookMin", "privLocalName": "국민", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "278"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ko-K<PERSON>, JiMinNeural)", "privLocale": "ko-KR", "privShortName": "ko-KR-JiMinNeural", "privLocaleName": "Korean (Korea)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "지민", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "291"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ko-K<PERSON>, SeoHyeonNeural)", "privLocale": "ko-KR", "privShortName": "ko-KR-SeoHyeonNeural", "privLocaleName": "Korean (Korea)", "privDisplayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocalName": "서현", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "258"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ko-KR, SoonBokNeural)", "privLocale": "ko-KR", "privShortName": "ko-KR-SoonBokNeural", "privLocaleName": "Korean (Korea)", "privDisplayName": "SoonBok", "privLocalName": "순복", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "271"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ko-K<PERSON>, YuJinNeural)", "privLocale": "ko-KR", "privShortName": "ko-KR-YuJinNeural", "privLocaleName": "Korean (Korea)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "유진", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "288"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (lo-LA, KeomanyNeural)", "privLocale": "lo-LA", "privShortName": "lo-LA-KeomanyNeural", "privLocaleName": "<PERSON> (Laos)", "privDisplayName": "Keomany", "privLocalName": "ແກ້ວມະນີ", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "33"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (lo-LA, ChanthavongNeural)", "privLocale": "lo-LA", "privShortName": "lo-LA-ChanthavongNeural", "privLocaleName": "<PERSON> (Laos)", "privDisplayName": "<PERSON><PERSON><PERSON><PERSON>", "privLocalName": "ຈັນທະວົງ", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "35"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (lt-LT, OnaNeural)", "privLocale": "lt-LT", "privShortName": "lt-LT-OnaNeural", "privLocaleName": "Lithuanian (Lithuania)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "107"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (lt-LT, LeonasNeural)", "privLocale": "lt-LT", "privShortName": "lt-LT-LeonasNeural", "privLocaleName": "Lithuanian (Lithuania)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "111"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (lv-LV, EveritaNeural)", "privLocale": "lv-LV", "privShortName": "lv-LV-EveritaNeural", "privLocaleName": "Latvian (Latvia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "106"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (lv-LV, NilsNeural)", "privLocale": "lv-LV", "privShortName": "lv-LV-NilsNeural", "privLocaleName": "Latvian (Latvia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "120"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (mk-MK, MarijaNeural)", "privLocale": "mk-MK", "privShortName": "mk-MK-MarijaNeural", "privLocaleName": "Macedonian (North Macedonia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "Марија", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "127"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (mk-M<PERSON>, AleksandarNeural)", "privLocale": "mk-MK", "privShortName": "mk-MK-AleksandarNeural", "privLocaleName": "Macedonian (North Macedonia)", "privDisplayName": "Aleksandar", "privLocalName": "Александар", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "127"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ml-IN, SobhanaNeural)", "privLocale": "ml-IN", "privShortName": "ml-IN-SobhanaNeural", "privLocaleName": "Malayalam (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "ശോഭന", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "87"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ml-IN, MidhunNeural)", "privLocale": "ml-IN", "privShortName": "ml-IN-MidhunNeural", "privLocaleName": "Malayalam (India)", "privDisplayName": "Midhun", "privLocalName": "മിഥുൻ", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "93"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (mn-MN, YesuiNeural)", "privLocale": "mn-MN", "privShortName": "mn-MN-<PERSON>uiNeural", "privLocaleName": "Mongolian (Mongolia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "Есүй", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "142"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (mn-MN, BataaNeural)", "privLocale": "mn-MN", "privShortName": "mn-MN-BataaNeural", "privLocaleName": "Mongolian (Mongolia)", "privDisplayName": "Bataa", "privLocalName": "Батаа", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "142"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (mr-IN, AarohiNeural)", "privLocale": "mr-<PERSON>", "privShortName": "mr-<PERSON>-<PERSON><PERSON>hiNeural", "privLocaleName": "Marathi (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "आरोही", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "99"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (mr-<PERSON>, ManoharNeural)", "privLocale": "mr-<PERSON>", "privShortName": "mr-<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Marathi (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "मनो<PERSON>र", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "100"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ms<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>al)", "privLocale": "ms-MY", "privShortName": "ms-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "<PERSON> (Malaysia)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "112"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ms-MY, OsmanNeural)", "privLocale": "ms-MY", "privShortName": "ms-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "<PERSON> (Malaysia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "118"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (mt-MT, GraceNeural)", "privLocale": "mt-MT", "privShortName": "mt-MT-GraceNeural", "privLocaleName": "Maltese (Malta)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "136"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (mt-M<PERSON>, JosephN<PERSON>al)", "privLocale": "mt-MT", "privShortName": "mt-MT-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Maltese (Malta)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "130"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (my-MM, NilarNeural)", "privLocale": "my-MM", "privShortName": "my-MM-<PERSON><PERSON><PERSON><PERSON>al", "privLocaleName": "Burmese (Myanmar)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "နီလာ", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "63"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (my-MM, ThihaNeural)", "privLocale": "my-MM", "privShortName": "my-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Burmese (Myanmar)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "သီဟ", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "71"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, <PERSON>nilleNeural)", "privLocale": "nb-NO", "privShortName": "nb-NO-<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Norwegian <PERSON><PERSON><PERSON><PERSON><PERSON> (Norway)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "160"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, FinnNeural)", "privLocale": "nb-NO", "privShortName": "nb-NO-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Norwegian <PERSON><PERSON><PERSON><PERSON><PERSON> (Norway)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "145"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, IselinNeural)", "privLocale": "nb-NO", "privShortName": "nb-NO-IselinNeural", "privLocaleName": "Norwegian <PERSON><PERSON><PERSON><PERSON><PERSON> (Norway)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "154"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ne-NP, HemkalaNeural)", "privLocale": "ne-NP", "privShortName": "ne-NP-HemkalaNeural", "privLocaleName": "Nepali (Nepal)", "privDisplayName": "<PERSON><PERSON><PERSON><PERSON>", "privLocalName": "हेमकला", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "119"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ne-NP, SagarNeural)", "privLocale": "ne-NP", "privShortName": "ne-NP-SagarNeural", "privLocaleName": "Nepali (Nepal)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "सागर", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "119"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (nl-BE, DenaNeural)", "privLocale": "nl-BE", "privShortName": "nl-BE-DenaNeural", "privLocaleName": "Dutch (Belgium)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (nl-B<PERSON>, ArnaudNeural)", "privLocale": "nl-BE", "privShortName": "nl-BE-<PERSON><PERSON>udNeural", "privLocaleName": "Dutch (Belgium)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (nl-NL, FennaNeural)", "privLocale": "nl-NL", "privShortName": "nl-NL-FennaNeural", "privLocaleName": "Dutch (Netherlands)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "140"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (nl-NL, MaartenNeural)", "privLocale": "nl-NL", "privShortName": "nl-NL-MaartenNeural", "privLocaleName": "Dutch (Netherlands)", "privDisplayName": "Ma<PERSON>n", "privLocalName": "Ma<PERSON>n", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "151"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (nl-NL, ColetteNeural)", "privLocale": "nl-NL", "privShortName": "nl-NL-ColetteNeural", "privLocaleName": "Dutch (Netherlands)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "132"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pl-PL, AgnieszkaNeural)", "privLocale": "pl-PL", "privShortName": "pl-PL-AgnieszkaNeural", "privLocaleName": "Polish (Poland)", "privDisplayName": "A<PERSON><PERSON>z<PERSON>", "privLocalName": "A<PERSON><PERSON>z<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "112"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pl-PL, MarekNeural)", "privLocale": "pl-PL", "privShortName": "pl-PL-MarekNeural", "privLocaleName": "Polish (Poland)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "128"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pl-PL, ZofiaNeural)", "privLocale": "pl-PL", "privShortName": "pl-PL-ZofiaNeural", "privLocaleName": "Polish (Poland)", "privDisplayName": "Zofia", "privLocalName": "Zofia", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "127"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ps-AF, LatifaNeural)", "privLocale": "ps-AF", "privShortName": "ps-AF-LatifaNeural", "privLocaleName": "<PERSON><PERSON><PERSON> (Afghanistan)", "privDisplayName": "Latifa", "privLocalName": "لطيفه", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "165"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ps-AF, GulNawazNeural)", "privLocale": "ps-AF", "privShortName": "ps-AF-GulNawazNeural", "privLocaleName": "<PERSON><PERSON><PERSON> (Afghanistan)", "privDisplayName": "Gul Nawaz", "privLocalName": " ګل نواز", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "170"}, {"privStyleList": ["calm"], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-FranciscaNeural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "Francisca", "privLocalName": "Francisca", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, AntonioNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-AntonioNeural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "Antonio", "privLocalName": "<PERSON><PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "138"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-<PERSON>, <PERSON>N<PERSON><PERSON>)", "privLocale": "pt-BR", "privShortName": "pt-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "144"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, DonatoNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-DonatoNeural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "152"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, ElzaNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-ElzaNeural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "Elza", "privLocalName": "Elza", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, FabioNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-<PERSON>abio<PERSON><PERSON>al", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "134"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, GiovannaNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-Giovanna<PERSON>eural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "Giovanna", "privLocalName": "Giovanna", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "143"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, HumbertoNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-HumbertoNeural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "146"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, JulioNeural)", "privLocale": "pt-BR", "privShortName": "pt-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "136"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, LeilaNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-Leila<PERSON><PERSON><PERSON>", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "153"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, LeticiaNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-Leticia<PERSON>eural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "128"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, ManuelaNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-ManuelaNeural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, NicolauNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-Nicola<PERSON><PERSON><PERSON>", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "132"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, ValerioNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-ValerioNeural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "Valerio", "privLocalName": "Valerio", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "131"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-BR, YaraNeural)", "privLocale": "pt-BR", "privShortName": "pt-BR-YaraNeural", "privLocaleName": "Portuguese (Brazil)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "136"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-PT, <PERSON>quel<PERSON>eural)", "privLocale": "pt-PT", "privShortName": "pt-<PERSON>-<PERSON><PERSON>", "privLocaleName": "Portuguese (Portugal)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "144"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-PT, DuarteNeural)", "privLocale": "pt-PT", "privShortName": "pt-PT-DuarteNeural", "privLocaleName": "Portuguese (Portugal)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "182"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (pt-PT, FernandaNeural)", "privLocale": "pt-PT", "privShortName": "pt-PT-FernandaNeural", "privLocaleName": "Portuguese (Portugal)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "166"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ro<PERSON>R<PERSON>, AlinaNeural)", "privLocale": "ro-RO", "privShortName": "ro-RO-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Romanian (Romania)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "141"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, EmilNeural)", "privLocale": "ro-RO", "privShortName": "ro-RO-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Romanian (Romania)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "144"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ru-R<PERSON>, SvetlanaNeural)", "privLocale": "ru-RU", "privShortName": "ru-RU-<PERSON><PERSON><PERSON>", "privLocaleName": "Russian (Russia)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "Светлана", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "113"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (r<PERSON>-<PERSON><PERSON>, DmitryNeural)", "privLocale": "ru-RU", "privShortName": "ru-RU-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Russian (Russia)", "privDisplayName": "Dmitry", "privLocalName": "Д<PERSON><PERSON><PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "113"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ru-R<PERSON>, DariyaNeural)", "privLocale": "ru-RU", "privShortName": "ru-RU-DariyaNeural", "privLocaleName": "Russian (Russia)", "privDisplayName": "Dariya", "privLocalName": "Дар<PERSON>я", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "113"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (si-LK, ThiliniNeural)", "privLocale": "si-LK", "privShortName": "si-LK-ThiliniNeural", "privLocaleName": "Sinhala (Sri Lanka)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "තිළිණි", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "142"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (si-LK, SameeraNeural)", "privLocale": "si-LK", "privShortName": "si-LK-SameeraNeural", "privLocaleName": "Sinhala (Sri Lanka)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "සමීර", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "155"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sk-SK, ViktoriaNeural)", "privLocale": "sk-SK", "privShortName": "sk-SK-ViktoriaNeural", "privLocaleName": "Slovak (Slovakia)", "privDisplayName": "Viktoria", "privLocalName": "Viktória", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "118"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sk-SK, LukasNeural)", "privLocale": "sk-SK", "privShortName": "sk-SK-LukasNeural", "privLocaleName": "Slovak (Slovakia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "132"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sl-SI, PetraNeural)", "privLocale": "sl-SI", "privShortName": "sl-SI-PetraNeural", "privLocaleName": "Slovenian (Slovenia)", "privDisplayName": "Petra", "privLocalName": "Petra", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "138"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sl-SI, RokNeural)", "privLocale": "sl-SI", "privShortName": "sl-SI-RokNeural", "privLocaleName": "Slovenian (Slovenia)", "privDisplayName": "Rok", "privLocalName": "Rok", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "126"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (so-SO, UbaxNeural)", "privLocale": "so-SO", "privShortName": "so-SO-UbaxNeural", "privLocaleName": "Somali (Somalia)", "privDisplayName": "Ubax", "privLocalName": "Ubax", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "126"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (so-SO, MuuseNeural)", "privLocale": "so-SO", "privShortName": "so-SO-MuuseNeural", "privLocaleName": "Somali (Somalia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "136"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sq-AL, AnilaNeural)", "privLocale": "sq-AL", "privShortName": "sq-AL-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Albanian (Albania)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "141"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sq-AL, IlirNeural)", "privLocale": "sq-AL", "privShortName": "sq-AL-IlirNeural", "privLocaleName": "Albanian (Albania)", "privDisplayName": "Ilir", "privLocalName": "Ilir", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "141"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sr-<PERSON>tn-RS, NicholasN<PERSON>)", "privLocale": "sr-Latn-RS", "privShortName": "sr-Latn-RS-Nicholas<PERSON><PERSON><PERSON>", "privLocaleName": "Serbian (Latin, Serbia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sr-<PERSON>tn-RS, SophieNeural)", "privLocale": "sr-Latn-RS", "privShortName": "sr-Latn-RS-SophieNeural", "privLocaleName": "Serbian (Latin, Serbia)", "privDisplayName": "<PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sr-RS, SophieNeural)", "privLocale": "sr-RS", "privShortName": "sr-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Serbian (Cyrillic, Serbia)", "privDisplayName": "<PERSON>", "privLocalName": "Софија", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "132"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sr-<PERSON>, NicholasN<PERSON>al)", "privLocale": "sr-RS", "privShortName": "sr-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Serbian (Cyrillic, Serbia)", "privDisplayName": "<PERSON>", "privLocalName": "Никола", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "128"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (su-ID, TutiNeural)", "privLocale": "su-ID", "privShortName": "su-ID-TutiNeural", "privLocaleName": "<PERSON><PERSON><PERSON> (Indonesia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "111"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (su-ID, JajangNeural)", "privLocale": "su-ID", "privShortName": "su-ID-JajangNeural", "privLocaleName": "<PERSON><PERSON><PERSON> (Indonesia)", "privDisplayName": "Jajang", "privLocalName": "Jajang", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "115"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sv-SE, SofieNeural)", "privLocale": "sv-SE", "privShortName": "sv-SE-SofieNeural", "privLocaleName": "Swedish (Sweden)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "138"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sv-SE, MattiasNeural)", "privLocale": "sv-SE", "privShortName": "sv-SE-Mattias<PERSON>al", "privLocaleName": "Swedish (Sweden)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "135"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sv-SE, HilleviNeural)", "privLocale": "sv-SE", "privShortName": "sv-SE-HilleviNeural", "privLocaleName": "Swedish (Sweden)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "147"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sw-K<PERSON>, ZuriNeural)", "privLocale": "sw-KE", "privShortName": "sw-KE-ZuriNeural", "privLocaleName": "<PERSON><PERSON><PERSON><PERSON> (Kenya)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "113"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sw-K<PERSON>, RafikiNeural)", "privLocale": "sw-KE", "privShortName": "sw-KE-RafikiNeural", "privLocaleName": "<PERSON><PERSON><PERSON><PERSON> (Kenya)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "121"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sw-TZ, RehemaNeural)", "privLocale": "sw-TZ", "privShortName": "sw-TZ-RehemaNeural", "privLocaleName": "<PERSON><PERSON><PERSON><PERSON> (Tanzania)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "<PERSON><PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "108"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (sw-TZ, DaudiNeural)", "privLocale": "sw-TZ", "privShortName": "sw-TZ-DaudiNeural", "privLocaleName": "<PERSON><PERSON><PERSON><PERSON> (Tanzania)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "114"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ta-IN, PallaviNeural)", "privLocale": "ta-IN", "privShortName": "ta-IN-PallaviNeural", "privLocaleName": "Tamil (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "பல்லவி", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "79"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ta-IN, ValluvarNeural)", "privLocale": "ta-IN", "privShortName": "ta-IN-ValluvarNeural", "privLocaleName": "Tamil (India)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "வள்ளுவர்", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "98"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ta-LK, SaranyaNeural)", "privLocale": "ta-LK", "privShortName": "ta-LK-SaranyaNeural", "privLocaleName": "Tamil (Sri Lanka)", "privDisplayName": "Saranya", "privLocalName": "சரண்யா", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "75"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ta<PERSON>L<PERSON>, KumarNeural)", "privLocale": "ta-LK", "privShortName": "ta-LK-KumarNeural", "privLocaleName": "Tamil (Sri Lanka)", "privDisplayName": "<PERSON>", "privLocalName": "குமார்", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "93"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ta-MY, KaniNeural)", "privLocale": "ta-MY", "privShortName": "ta-MY-KaniNeural", "privLocaleName": "Tamil (Malaysia)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "கனி", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "83"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ta-MY, SuryaNeural)", "privLocale": "ta-MY", "privShortName": "ta-MY-SuryaNeural", "privLocaleName": "Tamil (Malaysia)", "privDisplayName": "Surya", "privLocalName": "சூர்யா", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "93"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ta-SG, VenbaNeural)", "privLocale": "ta-SG", "privShortName": "ta-SG-VenbaNeural", "privLocaleName": "Tamil (Singapore)", "privDisplayName": "Venba", "privLocalName": "வெண்பா", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "83"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ta-SG, AnbuNeural)", "privLocale": "ta-SG", "privShortName": "ta-SG-AnbuNeural", "privLocaleName": "Tamil (Singapore)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "அன்பு", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "103"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (te-IN, ShrutiNeural)", "privLocale": "te-IN", "privShortName": "te-IN-ShrutiNeural", "privLocaleName": "Telugu (India)", "privDisplayName": "Shruti", "privLocalName": "శ్రుతి", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "79"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (te-<PERSON>, MohanNeural)", "privLocale": "te-IN", "privShortName": "te-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Telugu (India)", "privDisplayName": "<PERSON>", "privLocalName": "మోహన్", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "103"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (th-T<PERSON>, PremwadeeNeural)", "privLocale": "th-TH", "privShortName": "th-TH-<PERSON><PERSON><PERSON><PERSON>eNeural", "privLocaleName": "<PERSON> (Thailand)", "privDisplayName": "Premwadee", "privLocalName": "เปรมวดี", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "49"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (th-TH, NiwatNeural)", "privLocale": "th-TH", "privShortName": "th-TH-NiwatNeural", "privLocaleName": "<PERSON> (Thailand)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "นิวัฒน์", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "49"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (th-TH, AcharaNeural)", "privLocale": "th-TH", "privShortName": "th-TH-A<PERSON>raNeural", "privLocaleName": "<PERSON> (Thailand)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "อัจฉรา", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "51"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "privLocale": "tr-TR", "privShortName": "tr-TR-EmelNeural", "privLocaleName": "Turkish (Turkey)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "111"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (tr-TR, AhmetNeural)", "privLocale": "tr-TR", "privShortName": "tr-TR-AhmetNeural", "privLocaleName": "Turkish (Turkey)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "108"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (uk-UA, PolinaNeural)", "privLocale": "uk-UA", "privShortName": "uk-UA-PolinaNeural", "privLocaleName": "Ukrainian (Ukraine)", "privDisplayName": "Pol<PERSON>", "privLocalName": "Пол<PERSON>на", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "111"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (uk-UA, OstapNeural)", "privLocale": "uk-UA", "privShortName": "uk-UA-OstapNeural", "privLocaleName": "Ukrainian (Ukraine)", "privDisplayName": "Ostap", "privLocalName": "Остап", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "109"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ur-IN, GulNeural)", "privLocale": "ur-IN", "privShortName": "ur-IN-GulNeural", "privLocaleName": "Urdu (India)", "privDisplayName": "Gul", "privLocalName": "گل", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "157"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ur-<PERSON>, SalmanNeural)", "privLocale": "ur-IN", "privShortName": "ur-IN-SalmanN<PERSON><PERSON>", "privLocaleName": "Urdu (India)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "سلمان", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "103"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ur-P<PERSON>, UzmaNeural)", "privLocale": "ur-PK", "privShortName": "ur-PK-UzmaNeural", "privLocaleName": "Urdu (Pakistan)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "عظمیٰ", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "168"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (ur-<PERSON><PERSON>, AsadNeural)", "privLocale": "ur-PK", "privShortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocaleName": "Urdu (Pakistan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "اسد", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "167"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (uz-UZ, MadinaNeural)", "privLocale": "uz-UZ", "privShortName": "uz-UZ-MadinaNeural", "privLocaleName": "Uzbek (Latin, Uzbekistan)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "105"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (uz-UZ, SardorNeural)", "privLocale": "uz-UZ", "privShortName": "uz-UZ-SardorNeural", "privLocaleName": "Uzbek (Latin, Uzbekistan)", "privDisplayName": "Sardor", "privLocalName": "Sardor", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "112"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (vi-VN, HoaiMyNeural)", "privLocale": "vi-VN", "privShortName": "vi-VN-HoaiMyNeural", "privLocaleName": "Vietnamese (Vietnam)", "privDisplayName": "HoaiMy", "privLocalName": "Hoài My", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "202"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (vi-VN, NamMinhNeural)", "privLocale": "vi-VN", "privShortName": "vi-VN-Nam<PERSON>inhNeural", "privLocaleName": "Vietnamese (Vietnam)", "privDisplayName": "<PERSON><PERSON><PERSON><PERSON>", "privLocalName": "<PERSON>", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "204"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (wu<PERSON><PERSON><PERSON><PERSON>, XiaotongNeural)", "privLocale": "wuu-CN", "privShortName": "wuu-<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON>", "privLocaleName": "Chinese (<PERSON>, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓彤", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "238"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (wuu-C<PERSON>, YunzheNeural)", "privLocale": "wuu-CN", "privShortName": "wuu-CN-YunzheNeural", "privLocaleName": "Chinese (<PERSON>, Simplified)", "privDisplayName": "Yunzhe", "privLocalName": "云哲", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "244"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, XiaoMinNeural)", "privLocale": "yue-CN", "privShortName": "yue-CN-Xiao<PERSON>inNeur<PERSON>", "privLocaleName": "Chinese (Cantonese, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓敏", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "214"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (yue-C<PERSON>, YunSongNeural)", "privLocale": "yue-CN", "privShortName": "yue-CN-YunSongNeural", "privLocaleName": "Chinese (Cantonese, Simplified)", "privDisplayName": "YunSong", "privLocalName": "云松", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "221"}, {"privStyleList": ["assistant", "chat", "customerservice", "newscast", "affectionate", "angry", "calm", "cheerful", "disgruntled", "fearful", "gentle", "lyrical", "sad", "serious", "poetry-reading", "friendly", "chat-casual", "whisper", "sorry"], "privName": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaoxiaoNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓晓", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "274"}, {"privStyleList": ["narration-relaxed", "embarrassed", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "chat", "assistant", "newscast"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunxiNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-YunxiNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "Yunxi", "privLocalName": "云希", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "293", "privRolePlayList": ["Narrator", "YoungAdultMale", "Boy"]}, {"privStyleList": ["narration-relaxed", "sports-commentary", "sports-commentary-excited", "angry", "disgruntled", "cheerful", "sad", "serious", "depressed", "documentary-narration"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunjianNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-YunjianNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "云健", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "279"}, {"privStyleList": ["angry", "disgruntled", "affectionate", "cheerful", "fearful", "sad", "embarrassed", "serious", "gentle"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoyiNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaoyiNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "晓伊", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "263"}, {"privStyleList": ["customerservice", "narration-professional", "newscast-casual"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-YunyangNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "Yunyang", "privLocalName": "云扬", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "293"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, XiaochenNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-<PERSON><PERSON>N<PERSON>al", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "晓辰", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "283"}, {"privStyleList": ["calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "gentle", "affectionate", "embarrassed"], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON>h<PERSON><PERSON><PERSON>, XiaohanNeural)", "privLocale": "zh-CN", "privShortName": "zh-C<PERSON>-<PERSON><PERSON>Neural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "晓涵", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "259"}, {"privStyleList": ["chat"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-<PERSON><PERSON>, XiaomengNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaomengNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓梦", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "272"}, {"privStyleList": ["embarrassed", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "affectionate", "gentle", "envious"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaomoNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaomoNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "Xiao<PERSON>", "privLocalName": "晓墨", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "286", "privRolePlayList": ["YoungAdultFemale", "YoungAdultMale", "OlderAdultFemale", "OlderAdultMale", "<PERSON><PERSON><PERSON><PERSON>", "SeniorMale", "Girl", "Boy"]}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoqiuNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaoqiuNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓秋", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "232"}, {"privStyleList": ["calm", "fearful", "angry", "sad"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoruiNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaoruiNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓睿", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "243"}, {"privStyleList": ["chat"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoshuangNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaoshuangNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓双", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "225"}, {"privStyleList": ["calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "gentle", "depressed"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoxuanNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaoxuanNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓萱", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "273", "privRolePlayList": ["YoungAdultFemale", "YoungAdultMale", "OlderAdultFemale", "OlderAdultMale", "<PERSON><PERSON><PERSON><PERSON>", "SeniorMale", "Girl", "Boy"]}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoyanNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-<PERSON>yanNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "晓颜", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "279"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoyouNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaoyouNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "晓悠", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "211"}, {"privStyleList": ["angry", "disgruntled", "cheerful", "fearful", "sad", "serious"], "privName": "Microsoft Server Speech Text to Speech Voice (<PERSON>h<PERSON><PERSON><PERSON>, XiaozhenNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-XiaozhenNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "Xiaozhen", "privLocalName": "晓甄", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "273"}, {"privStyleList": ["angry", "disgruntled", "cheerful", "fearful", "sad", "serious", "depressed"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunfengNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-YunfengNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "Yunfeng", "privLocalName": "云枫", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "320"}, {"privStyleList": ["advertisement-upbeat"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunhaoNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-YunhaoNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "Yunhao", "privLocalName": "云皓", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "315"}, {"privStyleList": ["calm", "fearful", "cheerful", "angry", "sad"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunxiaNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-YunxiaNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "Yunxia", "privLocalName": "云夏", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "269"}, {"privStyleList": ["embarrassed", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunyeNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-YunyeNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "云野", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "278", "privRolePlayList": ["YoungAdultFemale", "YoungAdultMale", "OlderAdultFemale", "OlderAdultMale", "<PERSON><PERSON><PERSON><PERSON>", "SeniorMale", "Girl", "Boy"]}, {"privStyleList": ["calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "documentary-narration"], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunzeNeural)", "privLocale": "zh-CN", "privShortName": "zh-CN-YunzeNeural", "privLocaleName": "Chinese (Mandarin, Simplified)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "云泽", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "255", "privRolePlayList": ["OlderAdultMale", "SeniorMale"]}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-<PERSON><PERSON><PERSON><PERSON><PERSON>, YundengNeural)", "privLocale": "zh-CN-henan", "privShortName": "zh-C<PERSON>-<PERSON><PERSON>-YundengNeural", "privLocaleName": "Chinese (<PERSON><PERSON><PERSON>, Simplified)", "privDisplayName": "Yun<PERSON><PERSON>", "privLocalName": "云登", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "285"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-CN-liaoning, XiaobeiNeural)", "privLocale": "zh-CN-liaoning", "privShortName": "zh-CN-liaoning-XiaobeiNeural", "privLocaleName": "Chinese (Northeastern Mandarin, Simplified)", "privDisplayName": "Xiaobei", "privLocalName": "晓北", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "229"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-<PERSON><PERSON><PERSON><PERSON>, XiaoniNeural)", "privLocale": "zh-CN-shaanxi", "privShortName": "zh-CN-shaanxi-XiaoniNeural", "privLocaleName": "Chinese (<PERSON><PERSON><PERSON> Mandarin Shaanxi, Simplified)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "晓妮", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "263"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>-shand<PERSON>, YunxiangNeural)", "privLocale": "zh-CN-shandong", "privShortName": "zh-CN-shandong-YunxiangNeural", "privLocaleName": "Chinese (Jilu Mandarin, Simplified)", "privDisplayName": "Yun<PERSON><PERSON>", "privLocalName": "云翔", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "279"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-CN-sichuan, YunxiNeural)", "privLocale": "zh-CN-sichuan", "privShortName": "zh-CN-sichuan-YunxiNeural", "privLocaleName": "Chinese (Southwestern Mandarin, Simplified)", "privDisplayName": "YunxiSichuan", "privLocalName": "云希四川", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "285"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-HK, HiuMaanNeural)", "privLocale": "zh-HK", "privShortName": "zh-HK-HiuMaanNeural", "privLocaleName": "Chinese (Cantonese, Traditional)", "privDisplayName": "HiuMaan", "privLocalName": "曉曼", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "244"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-HK, WanLungNeural)", "privLocale": "zh-HK", "privShortName": "zh-HK-WanLungNeural", "privLocaleName": "Chinese (Cantonese, Traditional)", "privDisplayName": "Wan<PERSON><PERSON>", "privLocalName": "雲龍", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "259"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-HK, HiuGaaiNeural)", "privLocale": "zh-HK", "privShortName": "zh-HK-HiuGaaiNeural", "privLocaleName": "Chinese (Cantonese, Traditional)", "privDisplayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "privLocalName": "曉佳", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "194"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-T<PERSON>, HsiaoChenNeural)", "privLocale": "zh-TW", "privShortName": "zh-TW-HsiaoChenNeural", "privLocaleName": "Chinese (Taiwanese Mandarin, Traditional)", "privDisplayName": "HsiaoChen", "privLocalName": "曉臻", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "272"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, YunJheNeural)", "privLocale": "zh-TW", "privShortName": "zh-TW-YunJheNeural", "privLocaleName": "Chinese (Taiwanese Mandarin, Traditional)", "privDisplayName": "<PERSON><PERSON><PERSON>", "privLocalName": "雲哲", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "285"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zh-TW, HsiaoYuNeural)", "privLocale": "zh-TW", "privShortName": "zh-TW-HsiaoYuNeural", "privLocaleName": "Chinese (Taiwanese Mandarin, Traditional)", "privDisplayName": "HsiaoYu", "privLocalName": "曉雨", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "223"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zu-ZA, ThandoNeural)", "privLocale": "zu-ZA", "privShortName": "zu-ZA-ThandoNeural", "privLocaleName": "Zulu (South Africa)", "privDisplayName": "<PERSON><PERSON>", "privLocalName": "<PERSON><PERSON>", "privVoiceType": 1, "privGender": 1, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "83"}, {"privStyleList": [], "privName": "Microsoft Server Speech Text to Speech Voice (zu-ZA, ThembaNeural)", "privLocale": "zu-ZA", "privShortName": "zu-ZA-ThembaNeural", "privLocaleName": "Zulu (South Africa)", "privDisplayName": "Themba", "privLocalName": "Themba", "privVoiceType": 1, "privGender": 2, "privSampleRateHertz": "48000", "privStatus": "GA", "privWordsPerMinute": "90"}]